# Restaurant POS System

A comprehensive multi-tenant Restaurant Point of Sale system built with Django 5, React, PostgreSQL, and MongoDB.

## Features

- **Multi-Restaurant Support**: Complete tenant isolation with subscription management
- **Role-Based Access Control**: Owner, Manager, Cashier, Waiter, Kitchen Staff, Super Admin
- **Comprehensive POS**: Order management, billing, inventory, and reporting
- **Kitchen Display System**: Real-time order tracking for kitchen staff
- **Offline Mode**: Local caching with sync when connection restored
- **Multi-Language & Multi-Currency**: Global restaurant support
- **Analytics & Reporting**: MongoDB-optimized reporting with real-time dashboards

## Tech Stack

### Backend
- **Django 5** with Django REST Framework
- **PostgreSQL** for transactional data
- **MongoDB** for analytics and reporting
- **JWT Authentication** with role-based permissions
- **Celery** for background tasks

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for responsive design
- **Redux Toolkit** for state management
- **React Query** for API caching

### Infrastructure
- **Docker & Docker Compose** for development
- **Redis** for caching and Celery broker
- **Nginx** for reverse proxy and static files

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.12+
- Node.js 18+

### Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd restaurant-pos
```

2. Start the development environment:
```bash
docker-compose up -d
```

3. Run database migrations:
```bash
docker-compose exec backend python manage.py migrate
```

4. Create a superuser:
```bash
docker-compose exec backend python manage.py createsuperuser
```

5. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Admin Panel: http://localhost:8000/admin

## Project Structure

```
restaurant-pos/
├── backend/                 # Django backend
│   ├── apps/               # Django apps
│   ├── config/             # Django settings
│   ├── requirements/       # Python dependencies
│   └── manage.py
├── frontend/               # React frontend
│   ├── src/
│   ├── public/
│   └── package.json
├── docker/                 # Docker configurations
├── docs/                   # Documentation
├── scripts/                # Utility scripts
├── docker-compose.yml
└── README.md
```

## API Documentation

API documentation is available at `/api/docs/` when running the development server.

## Testing

Run backend tests:
```bash
docker-compose exec backend python manage.py test
```

Run frontend tests:
```bash
docker-compose exec frontend npm test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

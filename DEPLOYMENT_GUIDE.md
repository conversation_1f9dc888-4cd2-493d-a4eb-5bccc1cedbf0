# 🚀 Restaurant POS System - Deployment Guide

This guide covers deploying the Restaurant POS System to production environments.

## 📋 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores recommended

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- SSL Certificate (for HTTPS)

## 🏗️ Production Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │   Application   │
│     (Nginx)     │────│     (Nginx)     │────│    (Django)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Frontend    │    │      Cache      │    │    Database     │
│     (React)     │    │     (Redis)     │    │  (PostgreSQL)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Task Queue    │    │   Analytics     │
                       │    (Celery)     │    │   (MongoDB)     │
                       └─────────────────┘    └─────────────────┘
```

## 🔧 Environment Setup

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Git
sudo apt install git -y
```

### 2. Application Deployment

```bash
# Clone repository
git clone <your-repository-url> restaurant-pos
cd restaurant-pos

# Create production environment file
cp .env.example .env.prod
```

### 3. Environment Configuration

Edit `.env.prod` with production values:

```env
# Django Settings
DEBUG=False
SECRET_KEY=your-super-secret-production-key-here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,api.yourdomain.com

# Database Configuration
DATABASE_URL=***************************************************/restaurant_pos_prod
MONGODB_URL=*************************************************************************

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# CORS Settings
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Media and Static Files
MEDIA_URL=/media/
STATIC_URL=/static/
```

### 4. SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🐳 Docker Production Setup

### 1. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: restaurant_pos_prod
      POSTGRES_USER: pos_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - backend

  mongodb:
    image: mongo:6
    environment:
      MONGO_INITDB_ROOT_USERNAME: pos_user
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped
    networks:
      - backend

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    env_file: .env.prod
    depends_on:
      - postgres
      - mongodb
      - redis
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    restart: unless-stopped
    networks:
      - backend

  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    command: celery -A config worker -l info
    env_file: .env.prod
    depends_on:
      - postgres
      - mongodb
      - redis
    restart: unless-stopped
    networks:
      - backend

  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    command: celery -A config beat -l info
    env_file: .env.prod
    depends_on:
      - postgres
      - mongodb
      - redis
    restart: unless-stopped
    networks:
      - backend

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    networks:
      - frontend

  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - frontend
      - backend

volumes:
  postgres_data:
  mongodb_data:
  static_volume:
  media_volume:

networks:
  frontend:
  backend:
```

### 2. Production Dockerfiles

**Backend Dockerfile.prod:**
```dockerfile
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Collect static files
RUN python manage.py collectstatic --noinput

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "config.wsgi:application"]
```

**Frontend Dockerfile.prod:**
```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. Nginx Configuration

Create `nginx/nginx.prod.conf`:

```nginx
upstream backend {
    server backend:8000;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Frontend
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # API
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files
    location /static/ {
        alias /app/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /app/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # WebSocket support
    location /ws/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🚀 Deployment Process

### 1. Initial Deployment

```bash
# Build and start services
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# Create superuser
docker-compose -f docker-compose.prod.yml exec backend python manage.py createsuperuser

# Load initial data (optional)
docker-compose -f docker-compose.prod.yml exec backend python manage.py loaddata fixtures/initial_data.json
```

### 2. Health Checks

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f nginx

# Test endpoints
curl -k https://yourdomain.com/api/health/
curl -k https://yourdomain.com
```

## 📊 Monitoring and Maintenance

### 1. Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/docker-containers

# Add configuration:
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
```

### 2. Backup Strategy

```bash
#!/bin/bash
# backup.sh - Database backup script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# PostgreSQL backup
docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U pos_user restaurant_pos_prod > $BACKUP_DIR/postgres_$DATE.sql

# MongoDB backup
docker-compose -f docker-compose.prod.yml exec -T mongodb mongodump --username pos_user --password $MONGO_PASSWORD --authenticationDatabase admin --out /tmp/backup
docker cp $(docker-compose -f docker-compose.prod.yml ps -q mongodb):/tmp/backup $BACKUP_DIR/mongodb_$DATE

# Media files backup
tar -czf $BACKUP_DIR/media_$DATE.tar.gz media/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 3. Update Process

```bash
#!/bin/bash
# update.sh - Application update script

# Pull latest code
git pull origin main

# Backup database
./backup.sh

# Build new images
docker-compose -f docker-compose.prod.yml build

# Update services with zero downtime
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
docker-compose -f docker-compose.prod.yml up -d --no-deps frontend

# Run migrations
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# Restart services
docker-compose -f docker-compose.prod.yml restart nginx
```

## 🔒 Security Considerations

### 1. Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. Database Security

```bash
# PostgreSQL security
docker-compose -f docker-compose.prod.yml exec postgres psql -U pos_user -d restaurant_pos_prod -c "ALTER USER pos_user WITH PASSWORD 'new_secure_password';"

# MongoDB security
docker-compose -f docker-compose.prod.yml exec mongodb mongo admin -u pos_user -p --eval "db.changeUserPassword('pos_user', 'new_secure_password')"
```

### 3. Regular Security Updates

```bash
# System updates
sudo apt update && sudo apt upgrade -y

# Docker image updates
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 Performance Optimization

### 1. Database Optimization

```sql
-- PostgreSQL performance tuning
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

### 2. Redis Configuration

```bash
# Redis optimization
echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.core.somaxconn = 65535' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 3. Nginx Optimization

```nginx
# Add to nginx.conf
worker_processes auto;
worker_connections 1024;

gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

client_max_body_size 100M;
client_body_timeout 60s;
client_header_timeout 60s;
```

## 🆘 Troubleshooting

### Common Issues

1. **Service won't start**
   ```bash
   docker-compose -f docker-compose.prod.yml logs service_name
   ```

2. **Database connection issues**
   ```bash
   docker-compose -f docker-compose.prod.yml exec backend python manage.py dbshell
   ```

3. **SSL certificate issues**
   ```bash
   sudo certbot renew --dry-run
   ```

4. **Performance issues**
   ```bash
   docker stats
   htop
   ```

## 📞 Support

For deployment support:
- Check logs: `docker-compose logs -f`
- Monitor resources: `docker stats`
- Database health: `docker-compose exec postgres pg_isready`
- Application health: `curl https://yourdomain.com/api/health/`

---

**Production deployment completed! 🎉**

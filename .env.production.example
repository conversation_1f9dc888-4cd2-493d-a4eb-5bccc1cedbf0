# Production Environment Variables
# Copy this file to .env.production and fill in your values

# D<PERSON>go Settings
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,your-server-ip
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database
DB_PASSWORD=your-secure-database-password
DATABASE_URL=***********************************************************/restaurant_pos_prod

# Redis
REDIS_URL=redis://redis:6379/1

# Celery
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Customer Interface
CUSTOMER_INTERFACE_URL=https://yourdomain.com
REACT_APP_API_URL=https://yourdomain.com/api

# Email Configuration
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS S3 (Optional - for file storage)
USE_S3=False
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Backup S3 (Optional)
BACKUP_AWS_ACCESS_KEY_ID=your-backup-aws-access-key
BACKUP_AWS_SECRET_ACCESS_KEY=your-backup-aws-secret-key
BACKUP_AWS_STORAGE_BUCKET_NAME=your-backup-bucket-name
BACKUP_AWS_S3_REGION_NAME=us-east-1

# Payment Gateways
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key

PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=live

# SMS/Notifications (Optional)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Monitoring (Optional)
SENTRY_DSN=https://<EMAIL>/project-id
ENVIRONMENT=production

# SSL Certificate paths (if using custom certificates)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

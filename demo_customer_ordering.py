#!/usr/bin/env python
"""
Demo script showing the customer ordering system workflow.
This script demonstrates the complete customer journey from QR code scan to order completion.
"""

import json
from datetime import datetime

def demo_customer_ordering_flow():
    """
    Demonstrate the complete customer ordering flow.
    """
    
    print("🍽️  Restaurant POS - Customer Ordering System Demo")
    print("=" * 60)
    
    # Step 1: Customer scans QR code
    print("\n📱 Step 1: Customer scans QR code at table")
    qr_url = "http://localhost:3000/order/bella-vista/table/5"
    print(f"   QR Code URL: {qr_url}")
    print("   Customer is redirected to mobile ordering interface")
    
    # Step 2: Load restaurant and table info
    print("\n🏪 Step 2: Load restaurant and table information")
    restaurant_info = {
        "restaurant": {
            "name": "Bella Vista Restaurant",
            "slug": "bella-vista",
            "description": "Authentic Italian cuisine with a modern twist",
            "phone": "+****************",
            "logo": "/media/logos/bella-vista.jpg"
        },
        "table": {
            "number": "5",
            "display_name": "Table 5",
            "capacity": 4,
            "section": "Main Dining"
        }
    }
    print(f"   Restaurant: {restaurant_info['restaurant']['name']}")
    print(f"   Table: {restaurant_info['table']['display_name']}")
    print(f"   Section: {restaurant_info['table']['section']}")
    
    # Step 3: Load menu
    print("\n📋 Step 3: Load customer menu")
    menu_data = {
        "restaurant_name": "Bella Vista Restaurant",
        "categories": [
            {
                "id": "appetizers",
                "name": "Appetizers",
                "items": [
                    {
                        "id": "bruschetta",
                        "name": "Classic Bruschetta",
                        "description": "Toasted bread with fresh tomatoes, basil, and garlic",
                        "price": "12.99",
                        "is_available": True,
                        "prep_time_minutes": 10
                    },
                    {
                        "id": "calamari",
                        "name": "Fried Calamari",
                        "description": "Crispy squid rings with marinara sauce",
                        "price": "16.99",
                        "is_available": True,
                        "prep_time_minutes": 15
                    }
                ]
            },
            {
                "id": "mains",
                "name": "Main Courses",
                "items": [
                    {
                        "id": "margherita",
                        "name": "Margherita Pizza",
                        "description": "Fresh mozzarella, tomato sauce, and basil",
                        "price": "18.99",
                        "is_available": True,
                        "prep_time_minutes": 20
                    },
                    {
                        "id": "carbonara",
                        "name": "Spaghetti Carbonara",
                        "description": "Pasta with eggs, cheese, pancetta, and black pepper",
                        "price": "22.99",
                        "is_available": True,
                        "prep_time_minutes": 18
                    }
                ]
            }
        ]
    }
    
    print(f"   Categories loaded: {len(menu_data['categories'])}")
    for category in menu_data['categories']:
        print(f"   - {category['name']}: {len(category['items'])} items")
    
    # Step 4: Customer builds order
    print("\n🛒 Step 4: Customer builds order")
    customer_cart = [
        {
            "menu_item_id": "bruschetta",
            "name": "Classic Bruschetta",
            "price": 12.99,
            "quantity": 1,
            "special_instructions": ""
        },
        {
            "menu_item_id": "margherita",
            "name": "Margherita Pizza",
            "price": 18.99,
            "quantity": 1,
            "special_instructions": "Extra basil please"
        },
        {
            "menu_item_id": "carbonara",
            "name": "Spaghetti Carbonara",
            "price": 22.99,
            "quantity": 2,
            "special_instructions": "Light on the pepper"
        }
    ]
    
    total_amount = sum(item['price'] * item['quantity'] for item in customer_cart)
    total_items = sum(item['quantity'] for item in customer_cart)
    
    print(f"   Items in cart: {total_items}")
    for item in customer_cart:
        print(f"   - {item['quantity']}x {item['name']} (${item['price']:.2f})")
        if item['special_instructions']:
            print(f"     Note: {item['special_instructions']}")
    print(f"   Total: ${total_amount:.2f}")
    
    # Step 5: Customer provides information
    print("\n👤 Step 5: Customer provides information")
    customer_info = {
        "customer_name": "John Smith",
        "customer_phone": "+****************",
        "customer_email": "<EMAIL>"
    }
    print(f"   Name: {customer_info['customer_name']}")
    print(f"   Phone: {customer_info['customer_phone']}")
    print(f"   Email: {customer_info['customer_email']}")
    
    # Step 6: Place order
    print("\n📤 Step 6: Place order")
    order_data = {
        **customer_info,
        "items": [
            {
                "menu_item_id": item["menu_item_id"],
                "quantity": item["quantity"],
                "special_instructions": item["special_instructions"]
            }
            for item in customer_cart
        ]
    }
    
    # Simulate order creation response
    order_response = {
        "order_id": "550e8400-e29b-41d4-a716-446655440000",
        "order_number": "ORD-2024-001",
        "status": "pending",
        "total_amount": str(total_amount),
        "message": "Order placed successfully!"
    }
    
    print(f"   Order ID: {order_response['order_id']}")
    print(f"   Order Number: {order_response['order_number']}")
    print(f"   Status: {order_response['status']}")
    print(f"   Total: ${order_response['total_amount']}")
    print(f"   ✅ {order_response['message']}")
    
    # Step 7: Order tracking
    print("\n📊 Step 7: Order status tracking")
    order_statuses = [
        {"status": "pending", "message": "Order received and waiting for confirmation", "time": "14:30:15"},
        {"status": "confirmed", "message": "Order confirmed and will be prepared shortly", "time": "14:31:02"},
        {"status": "preparing", "message": "Order is being prepared in the kitchen", "time": "14:33:45"},
        {"status": "ready", "message": "Order is ready! It will be served to your table soon", "time": "14:48:20"},
        {"status": "served", "message": "Order has been served. Enjoy your meal!", "time": "14:52:10"}
    ]
    
    for i, status_update in enumerate(order_statuses):
        print(f"   {status_update['time']} - {status_update['status'].upper()}: {status_update['message']}")
        if i < len(order_statuses) - 1:
            print("   ⏳ Waiting for next update...")
    
    # Step 8: Additional customer actions
    print("\n🔔 Step 8: Additional customer actions available")
    actions = [
        "📞 Call Waiter - Notify staff for assistance",
        "💰 Request Bill - Request bill when ready to pay",
        "📋 View Order History - See previous orders",
        "⭐ Leave Feedback - Rate the dining experience"
    ]
    
    for action in actions:
        print(f"   {action}")
    
    # Step 9: Integration with POS system
    print("\n🖥️  Step 9: Integration with POS system")
    pos_integration = [
        "✅ Order appears in staff POS interface",
        "✅ Kitchen display shows order details",
        "✅ Waiter notifications for table service",
        "✅ Real-time order status updates",
        "✅ Inventory automatically updated",
        "✅ Sales analytics recorded"
    ]
    
    for integration in pos_integration:
        print(f"   {integration}")
    
    print("\n" + "=" * 60)
    print("🎉 Customer ordering flow completed successfully!")
    print("\nKey Benefits:")
    print("• Contactless ordering experience")
    print("• Reduced wait times for customers")
    print("• Improved order accuracy")
    print("• Better staff efficiency")
    print("• Real-time order tracking")
    print("• Seamless POS integration")
    
    return True

def demo_qr_code_generation():
    """
    Demonstrate QR code generation process.
    """
    
    print("\n" + "=" * 60)
    print("🔗 QR Code Generation Demo")
    print("=" * 60)
    
    # Sample restaurant data
    restaurants = [
        {
            "name": "Bella Vista Restaurant",
            "slug": "bella-vista",
            "tables": [
                {"number": "1", "capacity": 2, "section": "Window"},
                {"number": "2", "capacity": 4, "section": "Main Dining"},
                {"number": "3", "capacity": 6, "section": "Main Dining"},
                {"number": "4", "capacity": 2, "section": "Patio"},
                {"number": "5", "capacity": 4, "section": "Main Dining"}
            ]
        }
    ]
    
    base_url = "http://localhost:3000"
    
    for restaurant in restaurants:
        print(f"\n🏪 Restaurant: {restaurant['name']}")
        print(f"   Slug: {restaurant['slug']}")
        print(f"   Tables: {len(restaurant['tables'])}")
        
        for table in restaurant['tables']:
            qr_url = f"{base_url}/order/{restaurant['slug']}/table/{table['number']}"
            print(f"\n   📋 Table {table['number']} ({table['section']})")
            print(f"      Capacity: {table['capacity']} people")
            print(f"      QR Code URL: {qr_url}")
            print(f"      QR Code File: table_{restaurant['slug']}_{table['number']}_qr.png")
    
    print(f"\n✅ QR codes generated for {sum(len(r['tables']) for r in restaurants)} tables")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Restaurant POS Customer Ordering Demo")
    print("This demo shows how customers interact with the QR code ordering system")
    
    try:
        # Run the main customer ordering flow demo
        demo_customer_ordering_flow()
        
        # Run the QR code generation demo
        demo_qr_code_generation()
        
        print("\n" + "=" * 60)
        print("✅ All demos completed successfully!")
        print("\nNext Steps:")
        print("1. Set up the development environment")
        print("2. Install dependencies (qrcode library)")
        print("3. Run database migrations")
        print("4. Generate QR codes for your tables")
        print("5. Test the customer ordering interface")
        print("6. Integrate with your existing POS system")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()

"""
API tests for Restaurant POS system.
"""

import pytest
from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model

from apps.restaurants.models import Restaurant
from apps.users.models import User
from apps.menu.models import MenuItem, MenuCategory
from apps.orders.models import Order
from apps.customers.models import Customer

User = get_user_model()


class AuthenticationAPITest(APITestCase):
    """Test authentication API endpoints."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.CASHIER,
            first_name='Test',
            last_name='User'
        )
    
    def test_login_success(self):
        """Test successful login."""
        url = reverse('authentication:login')
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
    
    def test_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        url = reverse('authentication:login')
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_token_refresh(self):
        """Test token refresh."""
        # First login to get tokens
        login_url = reverse('authentication:login')
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        login_response = self.client.post(login_url, login_data, format='json')
        refresh_token = login_response.data['refresh']
        
        # Test token refresh
        refresh_url = reverse('authentication:token-refresh')
        refresh_data = {'refresh': refresh_token}
        response = self.client.post(refresh_url, refresh_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)


class MenuAPITest(APITestCase):
    """Test menu API endpoints."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.MANAGER
        )
        
        self.category = MenuCategory.objects.create(
            restaurant=self.restaurant,
            name='Main Courses'
        )
        
        self.menu_item = MenuItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Burger',
            description='A delicious test burger',
            price=Decimal('12.99'),
            is_available=True
        )
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
    
    def test_list_menu_items(self):
        """Test listing menu items."""
        url = reverse('menu:menu-items-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Test Burger')
    
    def test_create_menu_item(self):
        """Test creating a menu item."""
        url = reverse('menu:menu-items-list')
        data = {
            'category': self.category.id,
            'name': 'New Burger',
            'description': 'A new burger',
            'price': '15.99',
            'is_available': True
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Burger')
        self.assertEqual(MenuItem.objects.count(), 2)
    
    def test_update_menu_item(self):
        """Test updating a menu item."""
        url = reverse('menu:menu-items-detail', kwargs={'pk': self.menu_item.id})
        data = {
            'category': self.category.id,
            'name': 'Updated Burger',
            'description': 'An updated burger',
            'price': '13.99',
            'is_available': True
        }
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Burger')
        
        # Verify in database
        self.menu_item.refresh_from_db()
        self.assertEqual(self.menu_item.name, 'Updated Burger')
    
    def test_delete_menu_item(self):
        """Test deleting a menu item."""
        url = reverse('menu:menu-items-detail', kwargs={'pk': self.menu_item.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify soft delete
        self.menu_item.refresh_from_db()
        self.assertFalse(self.menu_item.is_active)


class OrderAPITest(APITestCase):
    """Test order API endpoints."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.WAITER
        )
        
        self.customer = Customer.objects.create(
            restaurant=self.restaurant,
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>'
        )
        
        self.category = MenuCategory.objects.create(
            restaurant=self.restaurant,
            name='Main Courses'
        )
        
        self.menu_item = MenuItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Burger',
            price=Decimal('12.99')
        )
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
    
    def test_create_order(self):
        """Test creating an order."""
        url = reverse('orders:orders-list')
        data = {
            'customer': self.customer.id,
            'order_type': 'dine_in',
            'items': [
                {
                    'menu_item': self.menu_item.id,
                    'quantity': 2,
                    'special_instructions': 'No onions'
                }
            ]
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['order_type'], 'dine_in')
        self.assertEqual(len(response.data['items']), 1)
        self.assertEqual(Order.objects.count(), 1)
    
    def test_list_orders(self):
        """Test listing orders."""
        # Create an order first
        order = Order.objects.create(
            restaurant=self.restaurant,
            customer=self.customer,
            waiter=self.user,
            order_type=Order.OrderType.DINE_IN
        )
        
        url = reverse('orders:orders-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_update_order_status(self):
        """Test updating order status."""
        order = Order.objects.create(
            restaurant=self.restaurant,
            customer=self.customer,
            waiter=self.user,
            order_type=Order.OrderType.DINE_IN,
            status=Order.Status.PENDING
        )
        
        url = reverse('orders:orders-update-status', kwargs={'pk': order.id})
        data = {
            'status': 'confirmed',
            'notes': 'Order confirmed by kitchen'
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify status update
        order.refresh_from_db()
        self.assertEqual(order.status, Order.Status.CONFIRMED)


class CustomerAPITest(APITestCase):
    """Test customer API endpoints."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.CASHIER
        )
        
        self.customer = Customer.objects.create(
            restaurant=self.restaurant,
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
    
    def test_list_customers(self):
        """Test listing customers."""
        url = reverse('customers:customers-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['first_name'], 'Test')
    
    def test_create_customer(self):
        """Test creating a customer."""
        url = reverse('customers:customers-list')
        data = {
            'first_name': 'New',
            'last_name': 'Customer',
            'email': '<EMAIL>',
            'phone': '+1987654321',
            'customer_type': 'regular'
        }
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['first_name'], 'New')
        self.assertEqual(Customer.objects.count(), 2)
    
    def test_search_customers(self):
        """Test searching customers."""
        url = reverse('customers:customers-list')
        response = self.client.get(url, {'search': 'Test'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        
        # Search with no results
        response = self.client.get(url, {'search': 'NonExistent'})
        self.assertEqual(len(response.data['results']), 0)


class AnalyticsAPITest(APITestCase):
    """Test analytics API endpoints."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.MANAGER
        )
        
        # Authenticate user
        self.client.force_authenticate(user=self.user)
    
    def test_dashboard_data(self):
        """Test dashboard data endpoint."""
        url = reverse('analytics:dashboard-data')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_revenue_today', response.data)
        self.assertIn('total_orders_today', response.data)
        self.assertIn('sales_chart_data', response.data)
    
    def test_sales_analytics(self):
        """Test sales analytics endpoint."""
        url = reverse('analytics:sales-analytics')
        response = self.client.get(url, {'period': 'month'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_revenue', response.data)
        self.assertIn('total_orders', response.data)
        self.assertIn('daily_sales', response.data)


class PermissionTest(APITestCase):
    """Test API permissions."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+1234567890'
        )
        
        self.cashier = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.CASHIER
        )
        
        self.manager = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.MANAGER
        )
    
    def test_cashier_permissions(self):
        """Test cashier role permissions."""
        self.client.force_authenticate(user=self.cashier)
        
        # Cashier should be able to view menu items
        url = reverse('menu:menu-items-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Cashier should NOT be able to create menu items
        data = {
            'name': 'New Item',
            'price': '10.00'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_manager_permissions(self):
        """Test manager role permissions."""
        self.client.force_authenticate(user=self.manager)
        
        # Manager should be able to view and create menu items
        url = reverse('menu:menu-items-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Create category first
        category = MenuCategory.objects.create(
            restaurant=self.restaurant,
            name='Test Category'
        )
        
        data = {
            'category': category.id,
            'name': 'New Item',
            'price': '10.00',
            'is_available': True
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_unauthenticated_access(self):
        """Test unauthenticated access is denied."""
        url = reverse('menu:menu-items-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

"""
Model tests for Restaurant POS system.
"""

import pytest
from decimal import Decimal
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from apps.restaurants.models import Restaurant, Table
from apps.users.models import User
from apps.menu.models import MenuItem, MenuCategory, MenuModifierGroup, MenuModifier
from apps.inventory.models import InventoryItem, InventoryCategory, Supplier
from apps.orders.models import Order, OrderItem
from apps.billing.models import PaymentMethod, Payment
from apps.customers.models import Customer, LoyaltyProgram, CustomerLoyalty

User = get_user_model()


class RestaurantModelTest(TestCase):
    """Test Restaurant model."""
    
    def setUp(self):
        self.restaurant_data = {
            'name': 'Test Restaurant',
            'email': '<EMAIL>',
            'phone': '+**********',
            'address_line_1': '123 Test St',
            'city': 'Test City',
            'state': 'Test State',
            'postal_code': '12345',
            'country': 'Test Country',
            'currency': 'USD',
            'timezone': 'UTC'
        }
    
    def test_create_restaurant(self):
        """Test creating a restaurant."""
        restaurant = Restaurant.objects.create(**self.restaurant_data)
        self.assertEqual(restaurant.name, 'Test Restaurant')
        self.assertEqual(restaurant.currency, 'USD')
        self.assertTrue(restaurant.is_active)
    
    def test_restaurant_str_representation(self):
        """Test restaurant string representation."""
        restaurant = Restaurant.objects.create(**self.restaurant_data)
        self.assertEqual(str(restaurant), 'Test Restaurant')
    
    def test_restaurant_slug_generation(self):
        """Test restaurant slug generation."""
        restaurant = Restaurant.objects.create(**self.restaurant_data)
        self.assertEqual(restaurant.slug, 'test-restaurant')


class UserModelTest(TestCase):
    """Test User model."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+**********'
        )
        
        self.user_data = {
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'phone': '+**********',
            'restaurant': self.restaurant,
            'role': User.Role.CASHIER
        }
    
    def test_create_user(self):
        """Test creating a user."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            **{k: v for k, v in self.user_data.items() if k != 'email'}
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, User.Role.CASHIER)
        self.assertTrue(user.is_active)
    
    def test_user_full_name(self):
        """Test user full name method."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            **{k: v for k, v in self.user_data.items() if k != 'email'}
        )
        self.assertEqual(user.get_full_name(), 'Test User')


class MenuModelTest(TestCase):
    """Test Menu models."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+**********'
        )
        
        self.category = MenuCategory.objects.create(
            restaurant=self.restaurant,
            name='Main Courses',
            description='Main course items'
        )
    
    def test_create_menu_item(self):
        """Test creating a menu item."""
        item = MenuItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Burger',
            description='A delicious test burger',
            price=Decimal('12.99'),
            is_available=True
        )
        self.assertEqual(item.name, 'Test Burger')
        self.assertEqual(item.price, Decimal('12.99'))
        self.assertTrue(item.is_available)
    
    def test_menu_item_str_representation(self):
        """Test menu item string representation."""
        item = MenuItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Burger',
            price=Decimal('12.99')
        )
        self.assertEqual(str(item), 'Test Burger')


class OrderModelTest(TestCase):
    """Test Order models."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+**********'
        )
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            restaurant=self.restaurant,
            role=User.Role.CASHIER
        )
        
        self.customer = Customer.objects.create(
            restaurant=self.restaurant,
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>'
        )
        
        self.table = Table.objects.create(
            restaurant=self.restaurant,
            name='Table 1',
            capacity=4
        )
        
        self.category = MenuCategory.objects.create(
            restaurant=self.restaurant,
            name='Main Courses'
        )
        
        self.menu_item = MenuItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Burger',
            price=Decimal('12.99')
        )
    
    def test_create_order(self):
        """Test creating an order."""
        order = Order.objects.create(
            restaurant=self.restaurant,
            customer=self.customer,
            table=self.table,
            waiter=self.user,
            order_type=Order.OrderType.DINE_IN,
            status=Order.Status.PENDING
        )
        self.assertEqual(order.status, Order.Status.PENDING)
        self.assertEqual(order.order_type, Order.OrderType.DINE_IN)
        self.assertIsNotNone(order.order_number)
    
    def test_order_total_calculation(self):
        """Test order total calculation."""
        order = Order.objects.create(
            restaurant=self.restaurant,
            customer=self.customer,
            table=self.table,
            waiter=self.user,
            order_type=Order.OrderType.DINE_IN
        )
        
        # Add order item
        order_item = OrderItem.objects.create(
            order=order,
            menu_item=self.menu_item,
            quantity=2,
            unit_price=self.menu_item.price
        )
        order_item.calculate_total()
        
        # Update order total
        order.calculate_totals()
        
        expected_total = Decimal('25.98')  # 2 * 12.99
        self.assertEqual(order.subtotal, expected_total)


class PaymentModelTest(TestCase):
    """Test Payment models."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+**********'
        )
        
        self.payment_method = PaymentMethod.objects.create(
            restaurant=self.restaurant,
            name='Cash',
            type=PaymentMethod.Type.CASH,
            is_active=True
        )
        
        self.customer = Customer.objects.create(
            restaurant=self.restaurant,
            first_name='Test',
            last_name='Customer'
        )
        
        self.order = Order.objects.create(
            restaurant=self.restaurant,
            customer=self.customer,
            order_type=Order.OrderType.DINE_IN,
            total_amount=Decimal('25.98')
        )
    
    def test_create_payment(self):
        """Test creating a payment."""
        payment = Payment.objects.create(
            restaurant=self.restaurant,
            order=self.order,
            payment_method=self.payment_method,
            amount=Decimal('25.98'),
            status=Payment.Status.COMPLETED
        )
        self.assertEqual(payment.amount, Decimal('25.98'))
        self.assertEqual(payment.status, Payment.Status.COMPLETED)


class CustomerModelTest(TestCase):
    """Test Customer models."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+**********'
        )
    
    def test_create_customer(self):
        """Test creating a customer."""
        customer = Customer.objects.create(
            restaurant=self.restaurant,
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>',
            phone='+**********',
            customer_type=Customer.CustomerType.REGULAR
        )
        self.assertEqual(customer.get_full_name(), 'Test Customer')
        self.assertEqual(customer.customer_type, Customer.CustomerType.REGULAR)
    
    def test_loyalty_program(self):
        """Test loyalty program functionality."""
        # Create loyalty program
        program = LoyaltyProgram.objects.create(
            restaurant=self.restaurant,
            name='Test Loyalty Program',
            program_type=LoyaltyProgram.ProgramType.POINTS,
            points_per_dollar=Decimal('1.0'),
            points_redemption_value=Decimal('0.01')
        )
        
        # Create customer
        customer = Customer.objects.create(
            restaurant=self.restaurant,
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>'
        )
        
        # Create loyalty account
        loyalty_account = CustomerLoyalty.objects.create(
            restaurant=self.restaurant,
            customer=customer,
            loyalty_program=program
        )
        
        # Test adding points
        loyalty_account.add_points(100, "Test points")
        self.assertEqual(loyalty_account.current_points, 100)
        self.assertEqual(loyalty_account.lifetime_points_earned, 100)
        
        # Test redeeming points
        success = loyalty_account.redeem_points(50, "Test redemption")
        self.assertTrue(success)
        self.assertEqual(loyalty_account.current_points, 50)
        self.assertEqual(loyalty_account.lifetime_points_redeemed, 50)


class InventoryModelTest(TestCase):
    """Test Inventory models."""
    
    def setUp(self):
        self.restaurant = Restaurant.objects.create(
            name='Test Restaurant',
            email='<EMAIL>',
            phone='+**********'
        )
        
        self.category = InventoryCategory.objects.create(
            restaurant=self.restaurant,
            name='Ingredients'
        )
        
        self.supplier = Supplier.objects.create(
            restaurant=self.restaurant,
            name='Test Supplier',
            email='<EMAIL>',
            phone='+**********'
        )
    
    def test_create_inventory_item(self):
        """Test creating an inventory item."""
        item = InventoryItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Ingredient',
            sku='TEST001',
            unit_of_measure='kg',
            current_stock=Decimal('10.0'),
            reorder_point=Decimal('5.0'),
            cost_per_unit=Decimal('2.50'),
            supplier=self.supplier
        )
        self.assertEqual(item.name, 'Test Ingredient')
        self.assertEqual(item.current_stock, Decimal('10.0'))
        self.assertFalse(item.is_low_stock())  # 10 > 5
    
    def test_low_stock_detection(self):
        """Test low stock detection."""
        item = InventoryItem.objects.create(
            restaurant=self.restaurant,
            category=self.category,
            name='Test Ingredient',
            current_stock=Decimal('3.0'),
            reorder_point=Decimal('5.0')
        )
        self.assertTrue(item.is_low_stock())  # 3 <= 5

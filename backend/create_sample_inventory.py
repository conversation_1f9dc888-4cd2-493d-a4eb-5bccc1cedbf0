#!/usr/bin/env python
"""
Create sample inventory items for testing
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apps.inventory.models import InventoryItem, InventoryCategory, Supplier, UnitOfMeasure
from apps.restaurants.models import Restaurant
from decimal import Decimal


def create_sample_inventory():
    # Get the restaurant
    restaurant = Restaurant.objects.first()

    print(f"Creating sample inventory for restaurant: {restaurant.name}")

    # Create units of measure (global, not restaurant-specific)
    units = [
        ("kg", "Kilogram", "weight"),
        ("g", "Gram", "weight"),
        ("l", "Liter", "volume"),
        ("ml", "Milliliter", "volume"),
        ("pcs", "Pieces", "count"),
        ("box", "Box", "count"),
        ("bottle", "Bottle", "count"),
    ]

    for abbreviation, name, unit_type in units:
        try:
            unit, created = UnitOfMeasure.objects.get_or_create(abbreviation=abbreviation, defaults={"name": name, "unit_type": unit_type})
            if created:
                print(f"Created unit: {name}")
        except Exception as e:
            # Unit might already exist, just get it
            try:
                unit = UnitOfMeasure.objects.get(abbreviation=abbreviation)
                print(f"Using existing unit: {unit.name}")
            except UnitOfMeasure.DoesNotExist:
                print(f"Could not create or find unit: {name}")

    # Create categories
    categories = [
        ("Vegetables", "Fresh vegetables and produce"),
        ("Meat & Poultry", "Fresh meat and poultry products"),
        ("Dairy", "Dairy products and eggs"),
        ("Beverages", "Drinks and beverages"),
        ("Dry Goods", "Dry and packaged goods"),
        ("Condiments", "Sauces and condiments"),
    ]

    for name, description in categories:
        category, created = InventoryCategory.objects.get_or_create(restaurant=restaurant, name=name, defaults={"description": description})
        if created:
            print(f"Created category: {name}")

    # Create suppliers
    suppliers = [
        ("Fresh Foods Co.", "<EMAIL>", "555-0001"),
        ("Meat Masters", "<EMAIL>", "555-0002"),
        ("Dairy Direct", "<EMAIL>", "555-0003"),
        ("Beverage World", "<EMAIL>", "555-0004"),
    ]

    for name, email, phone in suppliers:
        supplier, created = Supplier.objects.get_or_create(restaurant=restaurant, name=name, defaults={"email": email, "phone": phone})
        if created:
            print(f"Created supplier: {name}")

    # Get created objects
    try:
        kg_unit = UnitOfMeasure.objects.get(abbreviation="kg")
        g_unit = UnitOfMeasure.objects.get(abbreviation="g")
        l_unit = UnitOfMeasure.objects.get(abbreviation="L")  # Capital L
        pcs_unit = UnitOfMeasure.objects.get(abbreviation="pcs")
        bottle_unit = UnitOfMeasure.objects.get(abbreviation="bottle")
    except UnitOfMeasure.DoesNotExist as e:
        print(f"Error: Could not find required units: {e}")
        return

    veg_category = InventoryCategory.objects.get(restaurant=restaurant, name="Vegetables")
    meat_category = InventoryCategory.objects.get(restaurant=restaurant, name="Meat & Poultry")
    dairy_category = InventoryCategory.objects.get(restaurant=restaurant, name="Dairy")
    beverage_category = InventoryCategory.objects.get(restaurant=restaurant, name="Beverages")
    dry_category = InventoryCategory.objects.get(restaurant=restaurant, name="Dry Goods")

    fresh_supplier = Supplier.objects.get(restaurant=restaurant, name="Fresh Foods Co.")
    meat_supplier = Supplier.objects.get(restaurant=restaurant, name="Meat Masters")
    dairy_supplier = Supplier.objects.get(restaurant=restaurant, name="Dairy Direct")
    beverage_supplier = Supplier.objects.get(restaurant=restaurant, name="Beverage World")

    # Create inventory items
    items = [
        # Vegetables
        ("Tomatoes", veg_category, kg_unit, Decimal("25.50"), Decimal("5.00"), Decimal("2.50"), fresh_supplier),
        ("Lettuce", veg_category, pcs_unit, Decimal("15.00"), Decimal("3.00"), Decimal("1.50"), fresh_supplier),
        ("Onions", veg_category, kg_unit, Decimal("12.30"), Decimal("2.00"), Decimal("1.00"), fresh_supplier),
        ("Bell Peppers", veg_category, kg_unit, Decimal("8.75"), Decimal("2.00"), Decimal("1.00"), fresh_supplier),
        # Meat & Poultry
        ("Chicken Breast", meat_category, kg_unit, Decimal("18.50"), Decimal("5.00"), Decimal("2.00"), meat_supplier),
        ("Ground Beef", meat_category, kg_unit, Decimal("22.75"), Decimal("8.00"), Decimal("3.00"), meat_supplier),
        ("Bacon", meat_category, kg_unit, Decimal("5.25"), Decimal("2.00"), Decimal("0.50"), meat_supplier),
        # Dairy
        ("Milk", dairy_category, l_unit, Decimal("8.50"), Decimal("2.50"), Decimal("1.00"), dairy_supplier),
        ("Cheese (Cheddar)", dairy_category, kg_unit, Decimal("3.20"), Decimal("1.00"), Decimal("0.25"), dairy_supplier),
        ("Eggs", dairy_category, pcs_unit, Decimal("24.00"), Decimal("6.00"), Decimal("2.00"), dairy_supplier),
        # Beverages
        ("Coca Cola", beverage_category, bottle_unit, Decimal("48.00"), Decimal("12.00"), Decimal("6.00"), beverage_supplier),
        ("Orange Juice", beverage_category, l_unit, Decimal("6.75"), Decimal("2.00"), Decimal("1.00"), beverage_supplier),
        ("Water Bottles", beverage_category, bottle_unit, Decimal("36.00"), Decimal("12.00"), Decimal("6.00"), beverage_supplier),
        # Dry Goods
        ("Flour", dry_category, kg_unit, Decimal("45.50"), Decimal("10.00"), Decimal("5.00"), fresh_supplier),
        ("Rice", dry_category, kg_unit, Decimal("28.75"), Decimal("5.00"), Decimal("2.00"), fresh_supplier),
        ("Pasta", dry_category, kg_unit, Decimal("12.25"), Decimal("3.00"), Decimal("1.00"), fresh_supplier),
    ]

    for name, category, unit, current_stock, reorder_point, min_stock, supplier in items:
        item, created = InventoryItem.objects.get_or_create(
            restaurant=restaurant,
            name=name,
            defaults={
                "category": category,
                "unit_of_measure": unit,
                "current_stock": current_stock,
                "reorder_point": reorder_point,
                "minimum_stock": min_stock,
                "primary_supplier": supplier,
                "unit_cost": Decimal("2.50"),  # Default cost
                "is_active": True,
            },
        )
        if created:
            print(f"Created inventory item: {name} - {current_stock} {unit.abbreviation}")

    print("Sample inventory created successfully!")


if __name__ == "__main__":
    create_sample_inventory()

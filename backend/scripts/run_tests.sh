#!/bin/bash

# Restaurant POS System - Test Runner Script

set -e

echo "🧪 Running Restaurant POS System Tests"
echo "======================================"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
fi

# Install test dependencies
echo "📥 Installing test dependencies..."
pip install pytest pytest-django pytest-cov coverage

# Set environment variables
export DJANGO_SETTINGS_MODULE=config.settings
export DATABASE_URL=sqlite:///test_db.sqlite3

# Run database migrations for tests
echo "🗄️  Running test database migrations..."
python manage.py migrate --settings=config.settings

# Run unit tests
echo "🔬 Running unit tests..."
pytest tests/test_models.py -v --tb=short

# Run API tests
echo "🌐 Running API tests..."
pytest tests/test_api.py -v --tb=short

# Run all tests with coverage
echo "📊 Running all tests with coverage..."
pytest tests/ --cov=apps --cov-report=html --cov-report=term-missing --cov-fail-under=70

# Run Django's built-in tests
echo "🐍 Running Django tests..."
python manage.py test --settings=config.settings --verbosity=2

# Check code quality with flake8 (if available)
if command -v flake8 &> /dev/null; then
    echo "✨ Running code quality checks..."
    flake8 apps/ --max-line-length=120 --exclude=migrations
fi

# Check security with bandit (if available)
if command -v bandit &> /dev/null; then
    echo "🔒 Running security checks..."
    bandit -r apps/ -f json -o security_report.json || true
fi

echo ""
echo "✅ All tests completed!"
echo "📈 Coverage report generated in htmlcov/index.html"
echo ""

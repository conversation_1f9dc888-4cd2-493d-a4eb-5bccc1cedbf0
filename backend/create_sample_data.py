#!/usr/bin/env python
"""
Script to create sample data for the restaurant POS system.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apps.restaurants.models import Restaurant, RestaurantSettings, Table
from apps.menu.models import MenuCategory, MenuItem
from apps.inventory.models import UnitOfMeasure, InventoryCategory
from django.utils.text import slugify


def create_sample_data():
    print("Creating sample data...")

    # Create a sample restaurant
    restaurant, created = Restaurant.objects.get_or_create(
        slug="demo-restaurant",
        defaults={
            "name": "Demo Restaurant",
            "subdomain": "demo",
            "description": "A sample restaurant for testing",
            "address_line_1": "123 Main Street",
            "city": "Demo City",
            "state": "Demo State",
            "postal_code": "12345",
            "country": "United States",
            "phone": "+1234567890",
            "email": "<EMAIL>",
            "website": "https://demo-restaurant.com",
            "cuisine_type": "International",
            "status": "active",
        },
    )

    if created:
        print(f"✓ Created restaurant: {restaurant.name}")
    else:
        print(f"✓ Restaurant already exists: {restaurant.name}")

    # Create restaurant settings
    settings, created = RestaurantSettings.objects.get_or_create(
        restaurant=restaurant,
        defaults={
            "auto_print_receipts": True,
            "receipt_footer_text": "Thank you for dining with us!",
            "order_number_prefix": "ORD",
            "table_number_prefix": "T",
            "kitchen_display_enabled": True,
            "email_notifications": True,
            "accept_cash": True,
            "accept_cards": True,
            "tip_suggestions": [15, 18, 20, 25],
        },
    )

    if created:
        print("✓ Created restaurant settings")
    else:
        print("✓ Restaurant settings already exist")

    # Create some tables
    existing_tables = Table.objects.filter(restaurant=restaurant).count()
    if existing_tables == 0:
        for i in range(1, 11):
            Table.objects.create(restaurant=restaurant, number=str(i), capacity=4 if i <= 5 else 6, status="available")
        print("✓ Created 10 tables")
    else:
        print(f"✓ Tables already exist ({existing_tables} tables)")

    # Create some units of measure
    units = [
        ("Kilogram", "kg", "weight"),
        ("Gram", "g", "weight"),
        ("Liter", "L", "volume"),
        ("Milliliter", "mL", "volume"),
        ("Piece", "pc", "count"),
        ("Cup", "cup", "volume"),
        ("Tablespoon", "tbsp", "volume"),
        ("Teaspoon", "tsp", "volume"),
    ]

    units_created = 0
    for name, abbr, unit_type in units:
        unit, created = UnitOfMeasure.objects.get_or_create(name=name, defaults={"abbreviation": abbr, "unit_type": unit_type, "is_active": True})
        if created:
            units_created += 1

    if units_created > 0:
        print(f"✓ Created {units_created} units of measure")
    else:
        print("✓ Units of measure already exist")

    # Create inventory categories
    inv_categories = [
        ("Vegetables", "Fresh vegetables and produce"),
        ("Meat & Poultry", "Fresh meat and poultry products"),
        ("Dairy", "Milk, cheese, and dairy products"),
        ("Beverages", "Drinks and beverages"),
        ("Spices & Seasonings", "Herbs, spices, and seasonings"),
    ]

    inv_cats_created = 0
    for name, desc in inv_categories:
        cat, created = InventoryCategory.objects.get_or_create(restaurant=restaurant, name=name, defaults={"description": desc})
        if created:
            inv_cats_created += 1

    if inv_cats_created > 0:
        print(f"✓ Created {inv_cats_created} inventory categories")
    else:
        print("✓ Inventory categories already exist")

    # Create menu categories
    menu_categories = [
        ("Appetizers", "Starters and small plates", 1),
        ("Main Courses", "Main dishes and entrees", 2),
        ("Desserts", "Sweet treats and desserts", 3),
        ("Beverages", "Drinks and beverages", 4),
    ]

    menu_cats_created = 0
    for name, desc, sort_order in menu_categories:
        cat, created = MenuCategory.objects.get_or_create(restaurant=restaurant, name=name, defaults={"description": desc, "sort_order": sort_order, "is_active": True})
        if created:
            menu_cats_created += 1

    if menu_cats_created > 0:
        print(f"✓ Created {menu_cats_created} menu categories")
    else:
        print("✓ Menu categories already exist")

    # Create some sample menu items
    try:
        appetizers = MenuCategory.objects.get(restaurant=restaurant, name="Appetizers")
        main_courses = MenuCategory.objects.get(restaurant=restaurant, name="Main Courses")
        beverages = MenuCategory.objects.get(restaurant=restaurant, name="Beverages")

        sample_items = [
            (appetizers, "Caesar Salad", "Fresh romaine lettuce with caesar dressing", Decimal("12.99")),
            (appetizers, "Chicken Wings", "Spicy buffalo wings with ranch dip", Decimal("14.99")),
            (main_courses, "Grilled Salmon", "Fresh Atlantic salmon with vegetables", Decimal("24.99")),
            (main_courses, "Beef Burger", "Juicy beef burger with fries", Decimal("16.99")),
            (main_courses, "Chicken Pasta", "Creamy chicken alfredo pasta", Decimal("18.99")),
            (beverages, "Coca Cola", "Classic soft drink", Decimal("3.99")),
            (beverages, "Fresh Orange Juice", "Freshly squeezed orange juice", Decimal("5.99")),
        ]

        items_created = 0
        for category, name, desc, price in sample_items:
            item, created = MenuItem.objects.get_or_create(
                restaurant=restaurant, category=category, name=name, defaults={"description": desc, "price": price, "is_available": True, "item_type": "regular"}
            )
            if created:
                items_created += 1

        if items_created > 0:
            print(f"✓ Created {items_created} sample menu items")
        else:
            print("✓ Sample menu items already exist")

    except MenuCategory.DoesNotExist as e:
        print(f"✗ Error creating menu items: {e}")

    # Summary
    print(f"\n🎉 Sample data setup complete!")
    print(f"Restaurant: {restaurant.name}")
    print(f"Tables: {Table.objects.filter(restaurant=restaurant).count()}")
    print(f"Menu Categories: {MenuCategory.objects.filter(restaurant=restaurant).count()}")
    print(f"Menu Items: {MenuItem.objects.filter(restaurant=restaurant).count()}")
    print(f"Units of Measure: {UnitOfMeasure.objects.count()}")
    print(f"Inventory Categories: {InventoryCategory.objects.filter(restaurant=restaurant).count()}")


if __name__ == "__main__":
    create_sample_data()

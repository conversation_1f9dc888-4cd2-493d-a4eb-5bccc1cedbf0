#!/usr/bin/env python
"""
Test script for QR code generation functionality.
"""

import qrcode
from io import BytesIO
import os

def test_qr_generation():
    """Test QR code generation without Django."""
    
    # Test data
    restaurant_slug = "test-restaurant"
    table_number = "1"
    base_url = "http://localhost:3000"
    
    # Generate the customer ordering URL
    qr_url = f"{base_url}/order/{restaurant_slug}/table/{table_number}"
    print(f"Generated URL: {qr_url}")
    
    # Create QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(qr_url)
    qr.make(fit=True)
    
    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Save to file for testing
    output_path = "test_qr_code.png"
    img.save(output_path)
    
    print(f"QR code saved to: {output_path}")
    print(f"File size: {os.path.getsize(output_path)} bytes")
    
    return True

if __name__ == "__main__":
    try:
        test_qr_generation()
        print("✅ QR code generation test passed!")
    except Exception as e:
        print(f"❌ QR code generation test failed: {e}")
        import traceback
        traceback.print_exc()

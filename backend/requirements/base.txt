# Django and DRF
Django==5.2.6
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.5
django-extensions==3.2.3

# Database drivers
psycopg2-binary==2.9.10
pymongo==4.6.1
mongoengine==0.27.0

# Authentication and Security
djangorestframework-simplejwt==5.3.0
django-oauth-toolkit==1.7.1
cryptography==41.0.7

# Caching and Background Tasks
redis==5.0.1
celery==5.3.4
django-redis==5.4.0

# File Storage and Media
Pillow==11.1.0
django-storages==1.14.2
boto3==1.34.0
qrcode[pil]==7.4.2

# API Documentation
drf-spectacular==0.27.0
drf-spectacular-sidecar==2023.12.1

# Utilities
python-decouple==3.8
python-dotenv==1.0.0
dj-database-url==2.3.0
requests==2.31.0
pytz==2023.3
python-dateutil==2.8.2

# Validation and Serialization
marshmallow==3.20.2
pydantic==2.10.5

# Internationalization
django-modeltranslation==0.18.11
django-parler==2.3

# Multi-tenancy
django-tenant-schemas==1.10.0

# Monitoring and Logging
sentry-sdk==1.39.2
structlog==23.2.0

# Payment Processing
stripe==7.8.0

# Communication
twilio==8.11.1

# Data Processing
pandas==2.2.3
numpy==2.2.1

# WebSocket support
channels==4.0.0
channels-redis==4.1.0

# Development and Testing (will be in development.txt)
# pytest==7.4.3
# pytest-django==4.7.0
# factory-boy==3.3.0

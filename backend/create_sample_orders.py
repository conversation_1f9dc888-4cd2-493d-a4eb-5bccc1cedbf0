#!/usr/bin/env python
"""
Create sample orders for testing
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.orders.models import Order, OrderItem
from apps.menu.models import MenuItem
from apps.restaurants.models import Restaurant, Table
from apps.users.models import User
from decimal import Decimal

def create_sample_orders():
    # Get the restaurant and user
    restaurant = Restaurant.objects.first()
    user = User.objects.first()
    menu_items = MenuItem.objects.filter(restaurant=restaurant)[:3]
    table = Table.objects.filter(restaurant=restaurant).first()

    print(f'Creating sample orders for restaurant: {restaurant.name}')
    print(f'Available menu items: {[item.name for item in menu_items]}')

    # Create sample orders
    for i in range(3):
        order = Order.objects.create(
            restaurant=restaurant,
            order_type=Order.OrderType.DINE_IN,
            status=Order.Status.PENDING if i == 0 else Order.Status.CONFIRMED if i == 1 else Order.Status.PREPARING,
            customer_name=f'Customer {i+1}',
            customer_phone=f'555-000{i+1}',
            table=table,
            waiter=user
        )
        
        # Add items to order
        for j, menu_item in enumerate(menu_items[:2]):  # Add 2 items per order
            OrderItem.objects.create(
                order=order,
                menu_item=menu_item,
                quantity=j+1,
                unit_price=menu_item.price
            )
        
        # Calculate totals
        order.calculate_totals()
        print(f'Created order {order.order_number} with status {order.status} and total ${order.total_amount}')

    print('Sample orders created successfully!')

if __name__ == '__main__':
    create_sample_orders()

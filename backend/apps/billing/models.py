"""
Billing and payment models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from decimal import Decimal
from apps.core.models import BaseModel, TenantAwareModel, SoftDeleteModel
from apps.orders.models import Order
from apps.users.models import User


class PaymentMethod(TenantAwareModel):
    """
    Payment methods available for the restaurant.
    """
    
    class MethodType(models.TextChoices):
        CASH = 'cash', _('Cash')
        CREDIT_CARD = 'credit_card', _('Credit Card')
        DEBIT_CARD = 'debit_card', _('Debit Card')
        MOBILE_PAYMENT = 'mobile_payment', _('Mobile Payment')
        GIFT_CARD = 'gift_card', _('Gift Card')
        LOYALTY_POINTS = 'loyalty_points', _('Loyalty Points')
        BANK_TRANSFER = 'bank_transfer', _('Bank Transfer')
        CHECK = 'check', _('Check')
        VOUCHER = 'voucher', _('Voucher')
        SPLIT_PAYMENT = 'split_payment', _('Split Payment')

    name = models.CharField(max_length=100)
    method_type = models.CharField(
        max_length=20,
        choices=MethodType.choices,
        default=MethodType.CASH
    )
    is_active = models.BooleanField(default=True)
    requires_authorization = models.BooleanField(default=False)
    processing_fee_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=4, 
        default=0,
        help_text="Processing fee as percentage (e.g., 2.5 for 2.5%)"
    )
    processing_fee_fixed = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=0,
        help_text="Fixed processing fee amount"
    )
    
    # Integration settings
    gateway_name = models.CharField(max_length=50, blank=True)
    gateway_config = models.JSONField(default=dict, blank=True)
    
    # Display settings
    icon = models.CharField(max_length=50, blank=True)
    sort_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'payment_methods'
        verbose_name = _('Payment Method')
        verbose_name_plural = _('Payment Methods')
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"

    @property
    def display_name(self):
        return f"{self.name} ({self.get_method_type_display()})"


class Payment(BaseModel):
    """
    Individual payment transactions.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PROCESSING = 'processing', _('Processing')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')
        CANCELLED = 'cancelled', _('Cancelled')
        REFUNDED = 'refunded', _('Refunded')
        PARTIALLY_REFUNDED = 'partially_refunded', _('Partially Refunded')

    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='payments'
    )
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.PROTECT,
        related_name='payments'
    )
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    processing_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    
    # Transaction details
    transaction_id = models.CharField(max_length=100, blank=True)
    authorization_code = models.CharField(max_length=50, blank=True)
    reference_number = models.CharField(max_length=100, blank=True)
    
    # Gateway response
    gateway_response = models.JSONField(default=dict, blank=True)
    gateway_transaction_id = models.CharField(max_length=100, blank=True)
    
    # Staff and customer info
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_payments'
    )
    
    # Timing
    processed_at = models.DateTimeField(null=True, blank=True)
    
    # Additional details
    notes = models.TextField(blank=True)
    receipt_sent = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'payments'
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment {self.id} - {self.order.order_number} - ${self.amount}"

    def save(self, *args, **kwargs):
        # Calculate processing fee
        if not self.processing_fee:
            fee_percentage = self.payment_method.processing_fee_percentage / 100
            self.processing_fee = (self.amount * fee_percentage) + self.payment_method.processing_fee_fixed
            self.net_amount = self.amount - self.processing_fee
        
        super().save(*args, **kwargs)

    def process_payment(self):
        """Process the payment through the gateway."""
        # This would integrate with actual payment gateways
        # For now, we'll simulate the process
        
        if self.payment_method.method_type == PaymentMethod.MethodType.CASH:
            self.status = self.Status.COMPLETED
            self.transaction_id = f"CASH-{self.id}"
        else:
            # Simulate gateway processing
            self.status = self.Status.PROCESSING
            # In real implementation, this would call the payment gateway API
        
        from django.utils import timezone
        self.processed_at = timezone.now()
        self.save()

    def refund(self, amount=None, reason=''):
        """Process a refund for this payment."""
        refund_amount = amount or self.amount
        
        if refund_amount > self.amount:
            raise ValueError("Refund amount cannot exceed payment amount")
        
        # Create refund record
        refund = PaymentRefund.objects.create(
            payment=self,
            amount=refund_amount,
            reason=reason,
            processed_by=None  # Would be set by the view
        )
        
        # Update payment status
        if refund_amount == self.amount:
            self.status = self.Status.REFUNDED
        else:
            self.status = self.Status.PARTIALLY_REFUNDED
        
        self.save()
        return refund


class PaymentRefund(BaseModel):
    """
    Payment refund records.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PROCESSING = 'processing', _('Processing')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')

    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='refunds'
    )
    
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField(blank=True)
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    
    # Transaction details
    refund_transaction_id = models.CharField(max_length=100, blank=True)
    gateway_response = models.JSONField(default=dict, blank=True)
    
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_refunds'
    )
    processed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'payment_refunds'
        verbose_name = _('Payment Refund')
        verbose_name_plural = _('Payment Refunds')
        ordering = ['-created_at']

    def __str__(self):
        return f"Refund {self.id} - ${self.amount} for Payment {self.payment.id}"


class Receipt(BaseModel):
    """
    Receipt generation and tracking.
    """
    
    class ReceiptType(models.TextChoices):
        SALE = 'sale', _('Sale Receipt')
        REFUND = 'refund', _('Refund Receipt')
        VOID = 'void', _('Void Receipt')
        REPRINT = 'reprint', _('Reprint')

    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='receipts'
    )
    
    receipt_number = models.CharField(max_length=50, unique=True)
    receipt_type = models.CharField(
        max_length=20,
        choices=ReceiptType.choices,
        default=ReceiptType.SALE
    )
    
    # Receipt content
    receipt_data = models.JSONField(default=dict)
    receipt_html = models.TextField(blank=True)
    receipt_pdf = models.FileField(upload_to='receipts/', blank=True)
    
    # Delivery
    printed = models.BooleanField(default=False)
    emailed = models.BooleanField(default=False)
    sms_sent = models.BooleanField(default=False)
    
    # Staff info
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_receipts'
    )
    
    class Meta:
        db_table = 'receipts'
        verbose_name = _('Receipt')
        verbose_name_plural = _('Receipts')
        ordering = ['-created_at']

    def __str__(self):
        return f"Receipt {self.receipt_number} - {self.order.order_number}"

    def save(self, *args, **kwargs):
        if not self.receipt_number:
            # Generate receipt number
            from django.utils import timezone
            today = timezone.now().date()
            
            # Find last receipt number for today
            last_receipt = Receipt.objects.filter(
                created_at__date=today
            ).order_by('-created_at').first()
            
            if last_receipt and last_receipt.receipt_number.startswith('RCP'):
                try:
                    last_number = int(last_receipt.receipt_number.split('-')[-1])
                    self.receipt_number = f"RCP-{today.strftime('%Y%m%d')}-{last_number + 1:04d}"
                except (IndexError, ValueError):
                    self.receipt_number = f"RCP-{today.strftime('%Y%m%d')}-0001"
            else:
                self.receipt_number = f"RCP-{today.strftime('%Y%m%d')}-0001"
        
        super().save(*args, **kwargs)

    def generate_receipt_data(self):
        """Generate receipt data from order."""
        order = self.order
        restaurant = order.restaurant
        
        self.receipt_data = {
            'restaurant': {
                'name': restaurant.name,
                'address': restaurant.address,
                'phone': restaurant.phone,
                'email': restaurant.email,
                'tax_id': getattr(restaurant, 'tax_id', ''),
            },
            'order': {
                'order_number': order.order_number,
                'order_type': order.get_order_type_display(),
                'order_time': order.order_time.isoformat(),
                'table': order.table.display_name if order.table else None,
                'waiter': order.waiter.get_full_name() if order.waiter else None,
            },
            'customer': {
                'name': order.customer.get_full_name() if order.customer else order.customer_name,
                'phone': order.customer_phone,
                'email': order.customer_email,
            },
            'items': [
                {
                    'name': item.menu_item.name,
                    'quantity': item.quantity,
                    'unit_price': float(item.unit_price),
                    'total_price': float(item.total_price),
                    'modifiers': [
                        {
                            'name': mod.modifier.name,
                            'price': float(mod.unit_price)
                        }
                        for mod in item.modifiers.all()
                    ]
                }
                for item in order.items.all()
            ],
            'totals': {
                'subtotal': float(order.subtotal),
                'tax_amount': float(order.tax_amount),
                'service_charge': float(order.service_charge),
                'discount_amount': float(order.discount_amount),
                'tip_amount': float(order.tip_amount),
                'total_amount': float(order.total_amount),
            },
            'payments': [
                {
                    'method': payment.payment_method.name,
                    'amount': float(payment.amount),
                    'transaction_id': payment.transaction_id,
                }
                for payment in order.payments.filter(status=Payment.Status.COMPLETED)
            ],
            'receipt_info': {
                'receipt_number': self.receipt_number,
                'receipt_type': self.get_receipt_type_display(),
                'generated_at': self.created_at.isoformat(),
                'generated_by': self.generated_by.get_full_name() if self.generated_by else None,
            }
        }
        
        self.save(update_fields=['receipt_data'])


class Invoice(TenantAwareModel):
    """
    Invoice generation for orders (mainly for business customers).
    """
    
    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        SENT = 'sent', _('Sent')
        PAID = 'paid', _('Paid')
        OVERDUE = 'overdue', _('Overdue')
        CANCELLED = 'cancelled', _('Cancelled')

    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name='invoice'
    )
    
    invoice_number = models.CharField(max_length=50, unique=True)
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT
    )
    
    # Invoice details
    issue_date = models.DateField(auto_now_add=True)
    due_date = models.DateField()
    
    # Customer details
    bill_to_name = models.CharField(max_length=200)
    bill_to_address = models.TextField()
    bill_to_email = models.EmailField(blank=True)
    bill_to_phone = models.CharField(max_length=20, blank=True)
    bill_to_tax_id = models.CharField(max_length=50, blank=True)
    
    # Invoice content
    invoice_data = models.JSONField(default=dict)
    invoice_pdf = models.FileField(upload_to='invoices/', blank=True)
    
    # Payment terms
    payment_terms = models.CharField(max_length=100, default='Net 30')
    notes = models.TextField(blank=True)
    
    # Staff info
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_invoices'
    )
    
    class Meta:
        db_table = 'invoices'
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-created_at']

    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.order.order_number}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Generate invoice number
            from django.utils import timezone
            today = timezone.now().date()
            
            # Find last invoice number for today
            last_invoice = Invoice.objects.filter(
                restaurant=self.restaurant,
                created_at__date=today
            ).order_by('-created_at').first()
            
            if last_invoice and last_invoice.invoice_number.startswith('INV'):
                try:
                    last_number = int(last_invoice.invoice_number.split('-')[-1])
                    self.invoice_number = f"INV-{today.strftime('%Y%m%d')}-{last_number + 1:04d}"
                except (IndexError, ValueError):
                    self.invoice_number = f"INV-{today.strftime('%Y%m%d')}-0001"
            else:
                self.invoice_number = f"INV-{today.strftime('%Y%m%d')}-0001"
        
        super().save(*args, **kwargs)

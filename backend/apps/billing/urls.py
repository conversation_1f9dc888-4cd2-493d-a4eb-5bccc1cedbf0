"""
Billing URLs for Restaurant POS system.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    PaymentMethodViewSet, PaymentViewSet, PaymentRefundViewSet,
    ReceiptViewSet, InvoiceViewSet, billing_report
)

router = DefaultRouter()
router.register(r'payment-methods', PaymentMethodViewSet, basename='payment-methods')
router.register(r'payments', PaymentViewSet, basename='payments')
router.register(r'refunds', PaymentRefundViewSet, basename='payment-refunds')
router.register(r'receipts', ReceiptViewSet, basename='receipts')
router.register(r'invoices', InvoiceViewSet, basename='invoices')

app_name = 'billing'
urlpatterns = [
    path('', include(router.urls)),
    path('reports/', billing_report, name='billing-report'),
]

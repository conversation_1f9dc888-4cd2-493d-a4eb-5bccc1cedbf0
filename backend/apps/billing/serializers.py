"""
Billing serializers for Restaurant POS system.
"""

from rest_framework import serializers
from decimal import Decimal
from .models import PaymentMethod, Payment, PaymentRefund, Receipt, Invoice
from apps.orders.models import Order


class PaymentMethodSerializer(serializers.ModelSerializer):
    """
    Payment method serializer.
    """
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = PaymentMethod
        fields = [
            'id', 'name', 'method_type', 'display_name', 'is_active',
            'requires_authorization', 'processing_fee_percentage',
            'processing_fee_fixed', 'gateway_name', 'icon', 'sort_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class PaymentSerializer(serializers.ModelSerializer):
    """
    Payment serializer.
    """
    payment_method_name = serializers.Char<PERSON>ield(source='payment_method.name', read_only=True)
    payment_method_type = serializers.CharField(source='payment_method.method_type', read_only=True)
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'order', 'order_number', 'payment_method', 'payment_method_name',
            'payment_method_type', 'amount', 'processing_fee', 'net_amount',
            'status', 'transaction_id', 'authorization_code', 'reference_number',
            'gateway_transaction_id', 'processed_by', 'processed_by_name',
            'processed_at', 'notes', 'receipt_sent', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'processing_fee', 'net_amount', 'transaction_id',
            'authorization_code', 'gateway_transaction_id', 'processed_at',
            'created_at', 'updated_at'
        ]


class PaymentCreateSerializer(serializers.Serializer):
    """
    Serializer for creating payments.
    """
    order_id = serializers.UUIDField()
    payment_method_id = serializers.UUIDField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0.01)
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_order_id(self, value):
        try:
            from apps.core.middleware import get_current_restaurant
            restaurant = get_current_restaurant()
            order = Order.objects.get(id=value, restaurant=restaurant)
            
            if order.payment_status == Order.PaymentStatus.PAID:
                raise serializers.ValidationError("Order is already fully paid")
            
            return value
        except Order.DoesNotExist:
            raise serializers.ValidationError("Order not found")

    def validate_payment_method_id(self, value):
        try:
            from apps.core.middleware import get_current_restaurant
            restaurant = get_current_restaurant()
            payment_method = PaymentMethod.objects.get(id=value, restaurant=restaurant)
            
            if not payment_method.is_active:
                raise serializers.ValidationError("Payment method is not active")
            
            return value
        except PaymentMethod.DoesNotExist:
            raise serializers.ValidationError("Payment method not found")

    def validate(self, data):
        order = Order.objects.get(id=data['order_id'])
        amount = data['amount']
        
        if amount > order.remaining_amount:
            raise serializers.ValidationError(
                f"Payment amount (${amount}) exceeds remaining balance (${order.remaining_amount})"
            )
        
        return data

    def create(self, validated_data):
        order = Order.objects.get(id=validated_data['order_id'])
        payment_method = PaymentMethod.objects.get(id=validated_data['payment_method_id'])
        
        payment = Payment.objects.create(
            order=order,
            payment_method=payment_method,
            amount=validated_data['amount'],
            notes=validated_data.get('notes', ''),
            processed_by=self.context['request'].user
        )
        
        # Process the payment
        payment.process_payment()
        
        # Update order payment status
        order.paid_amount += payment.amount
        
        if order.paid_amount >= order.total_amount:
            order.payment_status = Order.PaymentStatus.PAID
        elif order.paid_amount > 0:
            order.payment_status = Order.PaymentStatus.PARTIAL
        
        order.save(update_fields=['paid_amount', 'payment_status'])
        
        return payment


class PaymentRefundSerializer(serializers.ModelSerializer):
    """
    Payment refund serializer.
    """
    payment_order_number = serializers.CharField(source='payment.order.order_number', read_only=True)
    payment_amount = serializers.DecimalField(source='payment.amount', max_digits=10, decimal_places=2, read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)
    
    class Meta:
        model = PaymentRefund
        fields = [
            'id', 'payment', 'payment_order_number', 'payment_amount',
            'amount', 'reason', 'status', 'refund_transaction_id',
            'processed_by', 'processed_by_name', 'processed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'refund_transaction_id', 'processed_at',
            'created_at', 'updated_at'
        ]


class PaymentRefundCreateSerializer(serializers.Serializer):
    """
    Serializer for creating payment refunds.
    """
    payment_id = serializers.UUIDField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0.01, required=False)
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_payment_id(self, value):
        try:
            payment = Payment.objects.get(id=value)
            
            if payment.status != Payment.Status.COMPLETED:
                raise serializers.ValidationError("Can only refund completed payments")
            
            return value
        except Payment.DoesNotExist:
            raise serializers.ValidationError("Payment not found")

    def validate(self, data):
        payment = Payment.objects.get(id=data['payment_id'])
        amount = data.get('amount', payment.amount)
        
        # Check if refund amount is valid
        total_refunded = sum(
            refund.amount for refund in payment.refunds.filter(
                status__in=[PaymentRefund.Status.COMPLETED, PaymentRefund.Status.PROCESSING]
            )
        )
        
        if amount > (payment.amount - total_refunded):
            raise serializers.ValidationError(
                f"Refund amount exceeds available refund balance"
            )
        
        data['amount'] = amount
        return data

    def create(self, validated_data):
        payment = Payment.objects.get(id=validated_data['payment_id'])
        
        refund = payment.refund(
            amount=validated_data['amount'],
            reason=validated_data.get('reason', '')
        )
        
        refund.processed_by = self.context['request'].user
        refund.save()
        
        return refund


class ReceiptSerializer(serializers.ModelSerializer):
    """
    Receipt serializer.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = Receipt
        fields = [
            'id', 'order', 'order_number', 'receipt_number', 'receipt_type',
            'receipt_data', 'printed', 'emailed', 'sms_sent',
            'generated_by', 'generated_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'receipt_number', 'receipt_data', 'created_at', 'updated_at'
        ]


class ReceiptCreateSerializer(serializers.Serializer):
    """
    Serializer for creating receipts.
    """
    order_id = serializers.UUIDField()
    receipt_type = serializers.ChoiceField(
        choices=Receipt.ReceiptType.choices,
        default=Receipt.ReceiptType.SALE
    )
    send_email = serializers.BooleanField(default=False)
    send_sms = serializers.BooleanField(default=False)
    print_receipt = serializers.BooleanField(default=True)

    def validate_order_id(self, value):
        try:
            from apps.core.middleware import get_current_restaurant
            restaurant = get_current_restaurant()
            order = Order.objects.get(id=value, restaurant=restaurant)
            return value
        except Order.DoesNotExist:
            raise serializers.ValidationError("Order not found")

    def create(self, validated_data):
        order = Order.objects.get(id=validated_data['order_id'])
        
        receipt = Receipt.objects.create(
            order=order,
            receipt_type=validated_data['receipt_type'],
            generated_by=self.context['request'].user
        )
        
        # Generate receipt data
        receipt.generate_receipt_data()
        
        # Handle delivery options
        if validated_data.get('print_receipt'):
            receipt.printed = True
        
        if validated_data.get('send_email') and order.customer_email:
            # Here you would integrate with email service
            receipt.emailed = True
        
        if validated_data.get('send_sms') and order.customer_phone:
            # Here you would integrate with SMS service
            receipt.sms_sent = True
        
        receipt.save()
        return receipt


class InvoiceSerializer(serializers.ModelSerializer):
    """
    Invoice serializer.
    """
    order_number = serializers.CharField(source='order.order_number', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'order', 'order_number', 'invoice_number', 'status',
            'issue_date', 'due_date', 'bill_to_name', 'bill_to_address',
            'bill_to_email', 'bill_to_phone', 'bill_to_tax_id',
            'payment_terms', 'notes', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'invoice_number', 'issue_date', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class InvoiceCreateSerializer(serializers.Serializer):
    """
    Serializer for creating invoices.
    """
    order_id = serializers.UUIDField()
    due_date = serializers.DateField()
    bill_to_name = serializers.CharField(max_length=200)
    bill_to_address = serializers.CharField()
    bill_to_email = serializers.EmailField(required=False, allow_blank=True)
    bill_to_phone = serializers.CharField(max_length=20, required=False, allow_blank=True)
    bill_to_tax_id = serializers.CharField(max_length=50, required=False, allow_blank=True)
    payment_terms = serializers.CharField(max_length=100, default='Net 30')
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate_order_id(self, value):
        try:
            from apps.core.middleware import get_current_restaurant
            restaurant = get_current_restaurant()
            order = Order.objects.get(id=value, restaurant=restaurant)
            
            # Check if invoice already exists
            if hasattr(order, 'invoice'):
                raise serializers.ValidationError("Invoice already exists for this order")
            
            return value
        except Order.DoesNotExist:
            raise serializers.ValidationError("Order not found")

    def create(self, validated_data):
        order = Order.objects.get(id=validated_data.pop('order_id'))
        
        invoice = Invoice.objects.create(
            order=order,
            **validated_data,
            created_by=self.context['request'].user
        )
        
        return invoice


class BillingReportSerializer(serializers.Serializer):
    """
    Serializer for billing reports.
    """
    total_sales = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_payments = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_refunds = serializers.DecimalField(max_digits=12, decimal_places=2)
    net_sales = serializers.DecimalField(max_digits=12, decimal_places=2)
    payment_methods_breakdown = serializers.ListField()
    daily_sales = serializers.ListField()
    top_payment_methods = serializers.ListField()
    pending_payments = serializers.IntegerField()
    failed_payments = serializers.IntegerField()

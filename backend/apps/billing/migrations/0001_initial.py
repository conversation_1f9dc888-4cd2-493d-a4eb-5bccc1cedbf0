# Generated by Django 5.2.6 on 2025-09-11 07:54

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("invoice_number", models.CharField(max_length=50, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("sent", "Sent"),
                            ("paid", "Paid"),
                            ("overdue", "Overdue"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("issue_date", models.Date<PERSON>ield(auto_now_add=True)),
                ("due_date", models.DateField()),
                ("bill_to_name", models.CharField(max_length=200)),
                ("bill_to_address", models.TextField()),
                ("bill_to_email", models.EmailField(blank=True, max_length=254)),
                ("bill_to_phone", models.CharField(blank=True, max_length=20)),
                ("bill_to_tax_id", models.CharField(blank=True, max_length=50)),
                ("invoice_data", models.JSONField(default=dict)),
                ("invoice_pdf", models.FileField(blank=True, upload_to="invoices/")),
                ("payment_terms", models.CharField(default="Net 30", max_length=100)),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Invoice",
                "verbose_name_plural": "Invoices",
                "db_table": "invoices",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "processing_fee",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("net_amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                            ("refunded", "Refunded"),
                            ("partially_refunded", "Partially Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("transaction_id", models.CharField(blank=True, max_length=100)),
                ("authorization_code", models.CharField(blank=True, max_length=50)),
                ("reference_number", models.CharField(blank=True, max_length=100)),
                ("gateway_response", models.JSONField(blank=True, default=dict)),
                (
                    "gateway_transaction_id",
                    models.CharField(blank=True, max_length=100),
                ),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("receipt_sent", models.BooleanField(default=False)),
            ],
            options={
                "verbose_name": "Payment",
                "verbose_name_plural": "Payments",
                "db_table": "payments",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "method_type",
                    models.CharField(
                        choices=[
                            ("cash", "Cash"),
                            ("credit_card", "Credit Card"),
                            ("debit_card", "Debit Card"),
                            ("mobile_payment", "Mobile Payment"),
                            ("gift_card", "Gift Card"),
                            ("loyalty_points", "Loyalty Points"),
                            ("bank_transfer", "Bank Transfer"),
                            ("check", "Check"),
                            ("voucher", "Voucher"),
                            ("split_payment", "Split Payment"),
                        ],
                        default="cash",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("requires_authorization", models.BooleanField(default=False)),
                (
                    "processing_fee_percentage",
                    models.DecimalField(
                        decimal_places=4,
                        default=0,
                        help_text="Processing fee as percentage (e.g., 2.5 for 2.5%)",
                        max_digits=5,
                    ),
                ),
                (
                    "processing_fee_fixed",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Fixed processing fee amount",
                        max_digits=10,
                    ),
                ),
                ("gateway_name", models.CharField(blank=True, max_length=50)),
                ("gateway_config", models.JSONField(blank=True, default=dict)),
                ("icon", models.CharField(blank=True, max_length=50)),
                ("sort_order", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "Payment Method",
                "verbose_name_plural": "Payment Methods",
                "db_table": "payment_methods",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="PaymentRefund",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("reason", models.TextField(blank=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("refund_transaction_id", models.CharField(blank=True, max_length=100)),
                ("gateway_response", models.JSONField(blank=True, default=dict)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Payment Refund",
                "verbose_name_plural": "Payment Refunds",
                "db_table": "payment_refunds",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Receipt",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("receipt_number", models.CharField(max_length=50, unique=True)),
                (
                    "receipt_type",
                    models.CharField(
                        choices=[
                            ("sale", "Sale Receipt"),
                            ("refund", "Refund Receipt"),
                            ("void", "Void Receipt"),
                            ("reprint", "Reprint"),
                        ],
                        default="sale",
                        max_length=20,
                    ),
                ),
                ("receipt_data", models.JSONField(default=dict)),
                ("receipt_html", models.TextField(blank=True)),
                ("receipt_pdf", models.FileField(blank=True, upload_to="receipts/")),
                ("printed", models.BooleanField(default=False)),
                ("emailed", models.BooleanField(default=False)),
                ("sms_sent", models.BooleanField(default=False)),
            ],
            options={
                "verbose_name": "Receipt",
                "verbose_name_plural": "Receipts",
                "db_table": "receipts",
                "ordering": ["-created_at"],
            },
        ),
    ]

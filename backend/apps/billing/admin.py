"""
Billing admin configuration.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import PaymentMethod, Payment, PaymentRefund, Receipt, Invoice


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'restaurant', 'method_type', 'is_active', 'processing_fee_percentage',
        'processing_fee_fixed', 'sort_order', 'created_at'
    ]
    list_filter = ['restaurant', 'method_type', 'is_active', 'created_at']
    search_fields = ['name', 'restaurant__name']
    ordering = ['restaurant', 'sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'method_type', 'is_active', 'sort_order', 'icon')
        }),
        ('Processing Fees', {
            'fields': ('processing_fee_percentage', 'processing_fee_fixed')
        }),
        ('Gateway Integration', {
            'fields': ('gateway_name', 'gateway_config', 'requires_authorization'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_id', 'order', 'payment_method', 'amount', 'status',
        'processed_by', 'processed_at', 'created_at'
    ]
    list_filter = ['status', 'payment_method', 'processed_at', 'created_at']
    search_fields = [
        'transaction_id', 'order__order_number', 'authorization_code', 'reference_number'
    ]
    ordering = ['-created_at']
    readonly_fields = [
        'processing_fee', 'net_amount', 'transaction_id', 'processed_at',
        'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Payment Information', {
            'fields': ('order', 'payment_method', 'amount', 'processing_fee', 'net_amount', 'status')
        }),
        ('Transaction Details', {
            'fields': ('transaction_id', 'authorization_code', 'reference_number', 'gateway_transaction_id')
        }),
        ('Processing', {
            'fields': ('processed_by', 'processed_at', 'receipt_sent')
        }),
        ('Gateway Response', {
            'fields': ('gateway_response',),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PaymentRefund)
class PaymentRefundAdmin(admin.ModelAdmin):
    list_display = [
        'payment', 'amount', 'status', 'processed_by', 'processed_at', 'created_at'
    ]
    list_filter = ['status', 'processed_at', 'created_at']
    search_fields = ['payment__transaction_id', 'payment__order__order_number', 'reason']
    ordering = ['-created_at']
    readonly_fields = ['refund_transaction_id', 'processed_at', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Refund Information', {
            'fields': ('payment', 'amount', 'reason', 'status')
        }),
        ('Processing', {
            'fields': ('processed_by', 'processed_at', 'refund_transaction_id')
        }),
        ('Gateway Response', {
            'fields': ('gateway_response',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Receipt)
class ReceiptAdmin(admin.ModelAdmin):
    list_display = [
        'receipt_number', 'order', 'receipt_type', 'printed', 'emailed',
        'sms_sent', 'generated_by', 'created_at'
    ]
    list_filter = ['receipt_type', 'printed', 'emailed', 'sms_sent', 'created_at']
    search_fields = ['receipt_number', 'order__order_number']
    ordering = ['-created_at']
    readonly_fields = ['receipt_number', 'receipt_data', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Receipt Information', {
            'fields': ('receipt_number', 'order', 'receipt_type', 'generated_by')
        }),
        ('Delivery Status', {
            'fields': ('printed', 'emailed', 'sms_sent')
        }),
        ('Receipt Data', {
            'fields': ('receipt_data',),
            'classes': ('collapse',)
        }),
        ('Files', {
            'fields': ('receipt_html', 'receipt_pdf'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = [
        'invoice_number', 'order', 'status', 'issue_date', 'due_date',
        'bill_to_name', 'created_by', 'created_at'
    ]
    list_filter = ['status', 'issue_date', 'due_date', 'created_at']
    search_fields = ['invoice_number', 'order__order_number', 'bill_to_name', 'bill_to_email']
    ordering = ['-created_at']
    readonly_fields = ['invoice_number', 'issue_date', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Invoice Information', {
            'fields': ('invoice_number', 'order', 'status', 'issue_date', 'due_date', 'created_by')
        }),
        ('Bill To', {
            'fields': ('bill_to_name', 'bill_to_address', 'bill_to_email', 'bill_to_phone', 'bill_to_tax_id')
        }),
        ('Terms & Notes', {
            'fields': ('payment_terms', 'notes')
        }),
        ('Invoice Data', {
            'fields': ('invoice_data',),
            'classes': ('collapse',)
        }),
        ('Files', {
            'fields': ('invoice_pdf',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

"""
Billing views for Restaurant POS system.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, F
from django.utils import timezone
from decimal import Decimal

from .models import PaymentMethod, Payment, PaymentRefund, Receipt, Invoice
from .serializers import (
    PaymentMethodSerializer,
    PaymentSerializer,
    PaymentCreateSerializer,
    PaymentRefundSerializer,
    PaymentRefundCreateSerializer,
    ReceiptSerializer,
    ReceiptCreateSerializer,
    InvoiceSerializer,
    InvoiceCreateSerializer,
    BillingReportSerializer,
)
from apps.core.permissions import IsRestaurantStaff
from apps.core.middleware import get_current_restaurant
from apps.orders.models import Order


class PaymentMethodViewSet(viewsets.ModelViewSet):
    """
    Payment method management viewset.
    """

    serializer_class = PaymentMethodSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "method_type"]
    ordering_fields = ["sort_order", "name", "created_at"]
    ordering = ["sort_order", "name"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return PaymentMethod.objects.filter(restaurant=restaurant)
        return PaymentMethod.objects.none()

    @action(detail=True, methods=["post"])
    def toggle_active(self, request, pk=None):
        """Toggle payment method active status."""
        payment_method = self.get_object()
        payment_method.is_active = not payment_method.is_active
        payment_method.save()

        return Response({"status": f'Payment method {"activated" if payment_method.is_active else "deactivated"} successfully'})


class PaymentViewSet(viewsets.ModelViewSet):
    """
    Payment management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["transaction_id", "order__order_number", "reference_number"]
    ordering_fields = ["created_at", "amount", "processed_at"]
    ordering = ["-created_at"]
    filterset_fields = ["status", "payment_method", "order"]

    def get_serializer_class(self):
        if self.action == "create":
            return PaymentCreateSerializer
        return PaymentSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return Payment.objects.filter(order__restaurant=restaurant).select_related("order", "payment_method", "processed_by")
        return Payment.objects.none()

    def create(self, request, *args, **kwargs):
        """Create a new payment."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        payment = serializer.save()

        # Return full payment data
        response_serializer = PaymentSerializer(payment)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["post"])
    def process(self, request, pk=None):
        """Process a pending payment."""
        payment = self.get_object()

        if payment.status != Payment.Status.PENDING:
            return Response({"error": "Payment is not in pending status"}, status=status.HTTP_400_BAD_REQUEST)

        payment.process_payment()

        return Response({"status": "Payment processed successfully", "payment": PaymentSerializer(payment).data})

    @action(detail=True, methods=["post"])
    def refund(self, request, pk=None):
        """Create a refund for this payment."""
        payment = self.get_object()
        serializer = PaymentRefundCreateSerializer(data={**request.data, "payment_id": payment.id}, context={"request": request})

        if serializer.is_valid():
            refund = serializer.save()
            return Response({"status": "Refund created successfully", "refund": PaymentRefundSerializer(refund).data})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def daily_summary(self, request):
        """Get daily payment summary."""
        restaurant = get_current_restaurant()
        if not restaurant:
            return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

        today = timezone.now().date()
        payments = Payment.objects.filter(order__restaurant=restaurant, created_at__date=today, status=Payment.Status.COMPLETED)

        summary = {
            "total_payments": payments.count(),
            "total_amount": payments.aggregate(Sum("amount"))["amount__sum"] or 0,
            "total_processing_fees": payments.aggregate(Sum("processing_fee"))["processing_fee__sum"] or 0,
            "net_amount": payments.aggregate(Sum("net_amount"))["net_amount__sum"] or 0,
            "payment_methods": {},
        }

        # Payment methods breakdown
        for payment_method in PaymentMethod.objects.filter(restaurant=restaurant):
            method_payments = payments.filter(payment_method=payment_method)
            summary["payment_methods"][payment_method.name] = {"count": method_payments.count(), "amount": method_payments.aggregate(Sum("amount"))["amount__sum"] or 0}

        return Response(summary)


class PaymentRefundViewSet(viewsets.ModelViewSet):
    """
    Payment refund management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["payment__transaction_id", "payment__order__order_number"]
    ordering_fields = ["created_at", "amount", "processed_at"]
    ordering = ["-created_at"]
    filterset_fields = ["status", "payment"]

    def get_serializer_class(self):
        if self.action == "create":
            return PaymentRefundCreateSerializer
        return PaymentRefundSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return PaymentRefund.objects.filter(payment__order__restaurant=restaurant).select_related("payment__order", "processed_by")
        return PaymentRefund.objects.none()

    @action(detail=True, methods=["post"])
    def process(self, request, pk=None):
        """Process a pending refund."""
        refund = self.get_object()

        if refund.status != PaymentRefund.Status.PENDING:
            return Response({"error": "Refund is not in pending status"}, status=status.HTTP_400_BAD_REQUEST)

        # Process refund (integrate with payment gateway)
        refund.status = PaymentRefund.Status.COMPLETED
        refund.processed_at = timezone.now()
        refund.processed_by = request.user
        refund.save()

        return Response({"status": "Refund processed successfully", "refund": PaymentRefundSerializer(refund).data})


class ReceiptViewSet(viewsets.ModelViewSet):
    """
    Receipt management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["receipt_number", "order__order_number"]
    ordering_fields = ["created_at"]
    ordering = ["-created_at"]
    filterset_fields = ["receipt_type", "printed", "emailed"]

    def get_serializer_class(self):
        if self.action == "create":
            return ReceiptCreateSerializer
        return ReceiptSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return Receipt.objects.filter(order__restaurant=restaurant).select_related("order", "generated_by")
        return Receipt.objects.none()

    def create(self, request, *args, **kwargs):
        """Create a new receipt."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        receipt = serializer.save()

        # Return full receipt data
        response_serializer = ReceiptSerializer(receipt)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["post"])
    def reprint(self, request, pk=None):
        """Reprint a receipt."""
        receipt = self.get_object()

        # Create a new receipt with reprint type
        new_receipt = Receipt.objects.create(order=receipt.order, receipt_type=Receipt.ReceiptType.REPRINT, generated_by=request.user)
        new_receipt.generate_receipt_data()
        new_receipt.printed = True
        new_receipt.save()

        return Response({"status": "Receipt reprinted successfully", "receipt": ReceiptSerializer(new_receipt).data})

    @action(detail=True, methods=["post"])
    def send_email(self, request, pk=None):
        """Send receipt via email."""
        receipt = self.get_object()
        email = request.data.get("email") or receipt.order.customer_email

        if not email:
            return Response({"error": "Email address is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Here you would integrate with email service
        # For now, we'll just mark it as emailed
        receipt.emailed = True
        receipt.save()

        return Response({"status": f"Receipt sent to {email} successfully"})

    @action(detail=True, methods=["get"])
    def download_pdf(self, request, pk=None):
        """Download receipt as PDF."""
        receipt = self.get_object()

        # Here you would generate and return PDF
        # For now, return receipt data
        return Response({"receipt_data": receipt.receipt_data, "download_url": f"/api/billing/receipts/{receipt.id}/pdf/"})


class InvoiceViewSet(viewsets.ModelViewSet):
    """
    Invoice management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["invoice_number", "order__order_number", "bill_to_name"]
    ordering_fields = ["created_at", "issue_date", "due_date"]
    ordering = ["-created_at"]
    filterset_fields = ["status"]

    def get_serializer_class(self):
        if self.action == "create":
            return InvoiceCreateSerializer
        return InvoiceSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return Invoice.objects.filter(restaurant=restaurant).select_related("order", "created_by")
        return Invoice.objects.none()

    @action(detail=True, methods=["post"])
    def send(self, request, pk=None):
        """Send invoice to customer."""
        invoice = self.get_object()

        if invoice.status != Invoice.Status.DRAFT:
            return Response({"error": "Only draft invoices can be sent"}, status=status.HTTP_400_BAD_REQUEST)

        # Here you would integrate with email service
        invoice.status = Invoice.Status.SENT
        invoice.save()

        return Response({"status": "Invoice sent successfully"})

    @action(detail=True, methods=["post"])
    def mark_paid(self, request, pk=None):
        """Mark invoice as paid."""
        invoice = self.get_object()

        if invoice.status == Invoice.Status.PAID:
            return Response({"error": "Invoice is already marked as paid"}, status=status.HTTP_400_BAD_REQUEST)

        invoice.status = Invoice.Status.PAID
        invoice.save()

        return Response({"status": "Invoice marked as paid successfully"})

    @action(detail=False, methods=["get"])
    def overdue(self, request):
        """Get overdue invoices."""
        restaurant = get_current_restaurant()
        if not restaurant:
            return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

        today = timezone.now().date()
        overdue_invoices = Invoice.objects.filter(restaurant=restaurant, status__in=[Invoice.Status.SENT], due_date__lt=today)

        # Update status to overdue
        overdue_invoices.update(status=Invoice.Status.OVERDUE)

        serializer = InvoiceSerializer(overdue_invoices, many=True)
        return Response(serializer.data)


@action(detail=False, methods=["get"])
def billing_report(request):
    """
    Generate comprehensive billing report.
    """
    restaurant = get_current_restaurant()
    if not restaurant:
        return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

    # Date range
    date_from = request.query_params.get("date_from")
    date_to = request.query_params.get("date_to")

    if not date_from or not date_to:
        # Default to current month
        today = timezone.now().date()
        date_from = today.replace(day=1)
        date_to = today

    # Get payments and refunds
    payments = Payment.objects.filter(order__restaurant=restaurant, status=Payment.Status.COMPLETED, created_at__date__range=[date_from, date_to])

    refunds = PaymentRefund.objects.filter(payment__order__restaurant=restaurant, status=PaymentRefund.Status.COMPLETED, created_at__date__range=[date_from, date_to])

    # Calculate totals
    total_sales = payments.aggregate(Sum("amount"))["amount__sum"] or 0
    total_refunds = refunds.aggregate(Sum("amount"))["amount__sum"] or 0
    net_sales = total_sales - total_refunds

    # Payment methods breakdown
    payment_methods_breakdown = []
    for method in PaymentMethod.objects.filter(restaurant=restaurant):
        method_payments = payments.filter(payment_method=method)
        if method_payments.exists():
            payment_methods_breakdown.append({"method": method.name, "count": method_payments.count(), "amount": method_payments.aggregate(Sum("amount"))["amount__sum"] or 0})

    # Daily sales
    daily_sales = []
    current_date = date_from
    while current_date <= date_to:
        daily_payments = payments.filter(created_at__date=current_date)
        daily_sales.append({"date": current_date.isoformat(), "amount": daily_payments.aggregate(Sum("amount"))["amount__sum"] or 0, "count": daily_payments.count()})
        current_date += timezone.timedelta(days=1)

    # Top payment methods
    top_payment_methods = sorted(payment_methods_breakdown, key=lambda x: x["amount"], reverse=True)[:5]

    # Failed and pending payments
    pending_payments = Payment.objects.filter(order__restaurant=restaurant, status=Payment.Status.PENDING).count()

    failed_payments = Payment.objects.filter(order__restaurant=restaurant, status=Payment.Status.FAILED).count()

    report_data = {
        "total_sales": total_sales,
        "total_payments": total_sales,  # Same as total_sales for completed payments
        "total_refunds": total_refunds,
        "net_sales": net_sales,
        "payment_methods_breakdown": payment_methods_breakdown,
        "daily_sales": daily_sales,
        "top_payment_methods": top_payment_methods,
        "pending_payments": pending_payments,
        "failed_payments": failed_payments,
    }

    serializer = BillingReportSerializer(report_data)
    return Response(serializer.data)

"""
Order views for Restaurant POS system.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum
from django.utils import timezone
from decimal import Decimal

from .models import Order, OrderItem, OrderStatusLog
from .serializers import (
    OrderSerializer,
    OrderListSerializer,
    OrderCreateSerializer,
    OrderUpdateStatusSerializer,
    OrderDiscountSerializer,
    OrderItemSerializer,
    OrderStatusLogSerializer,
)
from apps.core.permissions import IsRestaurantStaff
from apps.core.middleware import get_current_restaurant


class OrderViewSet(viewsets.ModelViewSet):
    """
    Order management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["order_number", "customer_name", "customer_phone"]
    ordering_fields = ["order_time", "total_amount", "status"]
    ordering = ["-order_time"]
    filterset_fields = ["status", "order_type", "payment_status", "table", "waiter"]

    def get_serializer_class(self):
        if self.action == "list":
            return OrderListSerializer
        elif self.action == "create":
            return OrderCreateSerializer
        return OrderSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            queryset = (
                Order.objects.filter(restaurant=restaurant)
                .select_related("customer", "waiter", "cashier", "table")
                .prefetch_related("items__menu_item", "items__modifiers__modifier")
            )

            # Filter by date range
            date_from = self.request.query_params.get("date_from")
            date_to = self.request.query_params.get("date_to")

            if date_from:
                queryset = queryset.filter(order_time__date__gte=date_from)
            if date_to:
                queryset = queryset.filter(order_time__date__lte=date_to)

            # Filter by status
            status_filter = self.request.query_params.get("status")
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            return queryset
        return Order.objects.none()

    def create(self, request, *args, **kwargs):
        """Create a new order."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        order = serializer.save()

        # Return full order data
        response_serializer = OrderSerializer(order)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["post"])
    def update_status(self, request, pk=None):
        """Update order status."""
        order = self.get_object()
        serializer = OrderUpdateStatusSerializer(data=request.data)

        if serializer.is_valid():
            new_status = serializer.validated_data["status"]
            notes = serializer.validated_data.get("notes", "")

            # Validate status transition
            if not self._is_valid_status_transition(order.status, new_status):
                return Response({"error": f"Invalid status transition from {order.status} to {new_status}"}, status=status.HTTP_400_BAD_REQUEST)

            order.update_status(new_status, request.user)

            # Additional logic for specific status changes
            if new_status == Order.Status.COMPLETED and order.table:
                order.table.status = "available"
                order.table.save()

            return Response({"status": "Order status updated successfully", "order": OrderSerializer(order).data})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def apply_discount(self, request, pk=None):
        """Apply discount to order."""
        order = self.get_object()
        serializer = OrderDiscountSerializer(data=request.data)

        if serializer.is_valid():
            order.apply_discount(serializer.validated_data["discount_type"], serializer.validated_data["discount_value"], serializer.validated_data.get("discount_reason", ""))

            return Response({"status": "Discount applied successfully", "order": OrderSerializer(order).data})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def add_payment(self, request, pk=None):
        """Add payment to order."""
        order = self.get_object()
        amount = Decimal(str(request.data.get("amount", 0)))
        payment_method = request.data.get("payment_method", "cash")

        if amount <= 0:
            return Response({"error": "Payment amount must be greater than 0"}, status=status.HTTP_400_BAD_REQUEST)

        if amount > order.remaining_amount:
            return Response({"error": "Payment amount exceeds remaining balance"}, status=status.HTTP_400_BAD_REQUEST)

        # Update order payment
        order.paid_amount += amount

        if order.paid_amount >= order.total_amount:
            order.payment_status = Order.PaymentStatus.PAID
        elif order.paid_amount > 0:
            order.payment_status = Order.PaymentStatus.PARTIAL

        order.save()

        # Here you would create a Payment record (to be implemented in billing app)

        return Response({"status": "Payment added successfully", "order": OrderSerializer(order).data})

    @action(detail=True, methods=["post"])
    def cancel_order(self, request, pk=None):
        """Cancel order."""
        order = self.get_object()

        if order.status in [Order.Status.COMPLETED, Order.Status.CANCELLED]:
            return Response({"error": "Cannot cancel completed or already cancelled order"}, status=status.HTTP_400_BAD_REQUEST)

        order.update_status(Order.Status.CANCELLED, request.user)

        # Free up table if applicable
        if order.table:
            order.table.status = "available"
            order.table.save()

        return Response({"status": "Order cancelled successfully", "order": OrderSerializer(order).data})

    @action(detail=False, methods=["get"])
    def kitchen_display(self, request):
        """Get orders for kitchen display."""
        restaurant = get_current_restaurant()
        if not restaurant:
            return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

        # Get orders that need kitchen attention
        orders = (
            Order.objects.filter(restaurant=restaurant, status__in=[Order.Status.CONFIRMED, Order.Status.PREPARING])
            .select_related("table")
            .prefetch_related("items__menu_item", "items__modifiers__modifier")
            .order_by("confirmed_time")
        )

        serializer = OrderSerializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def active_orders(self, request):
        """Get active orders (not completed or cancelled)."""
        restaurant = get_current_restaurant()
        if not restaurant:
            return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

        orders = (
            Order.objects.filter(restaurant=restaurant, status__in=[Order.Status.PENDING, Order.Status.CONFIRMED, Order.Status.PREPARING, Order.Status.READY, Order.Status.SERVED])
            .select_related("table", "waiter")
            .order_by("-order_time")
        )

        serializer = OrderListSerializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def daily_summary(self, request):
        """Get daily order summary."""
        restaurant = get_current_restaurant()
        if not restaurant:
            return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

        today = timezone.now().date()
        orders = Order.objects.filter(restaurant=restaurant, order_time__date=today)

        summary = {
            "total_orders": orders.count(),
            "completed_orders": orders.filter(status=Order.Status.COMPLETED).count(),
            "cancelled_orders": orders.filter(status=Order.Status.CANCELLED).count(),
            "total_revenue": orders.filter(status=Order.Status.COMPLETED).aggregate(Sum("total_amount"))["total_amount__sum"] or 0,
            "average_order_value": 0,
            "order_types": {},
        }

        if summary["completed_orders"] > 0:
            summary["average_order_value"] = summary["total_revenue"] / summary["completed_orders"]

        # Order types breakdown
        for order_type in Order.OrderType.choices:
            count = orders.filter(order_type=order_type[0]).count()
            summary["order_types"][order_type[1]] = count

        return Response(summary)

    def _is_valid_status_transition(self, current_status, new_status):
        """Validate if status transition is allowed."""
        valid_transitions = {
            Order.Status.PENDING: [Order.Status.CONFIRMED, Order.Status.CANCELLED],
            Order.Status.CONFIRMED: [Order.Status.PREPARING, Order.Status.CANCELLED],
            Order.Status.PREPARING: [Order.Status.READY, Order.Status.CANCELLED],
            Order.Status.READY: [Order.Status.SERVED, Order.Status.CANCELLED],
            Order.Status.SERVED: [Order.Status.COMPLETED],
            Order.Status.COMPLETED: [],  # Final state
            Order.Status.CANCELLED: [],  # Final state
            Order.Status.REFUNDED: [],  # Final state
        }

        return new_status in valid_transitions.get(current_status, [])


class OrderItemViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Order item viewset (read-only).
    """

    serializer_class = OrderItemSerializer
    permission_classes = [IsRestaurantStaff]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return OrderItem.objects.filter(order__restaurant=restaurant).select_related("menu_item", "order").prefetch_related("modifiers__modifier")
        return OrderItem.objects.none()

    @action(detail=True, methods=["post"])
    def update_kitchen_status(self, request, pk=None):
        """Update kitchen status for order item."""
        order_item = self.get_object()
        new_status = request.data.get("kitchen_status")

        if new_status not in ["pending", "preparing", "ready", "served"]:
            return Response({"error": "Invalid kitchen status"}, status=status.HTTP_400_BAD_REQUEST)

        order_item.kitchen_status = new_status
        order_item.save()

        # Update order status based on item statuses
        order = order_item.order
        all_items = order.items.all()

        if all(item.kitchen_status == "ready" for item in all_items):
            order.update_status(Order.Status.READY, request.user)
        elif any(item.kitchen_status == "preparing" for item in all_items):
            if order.status == Order.Status.CONFIRMED:
                order.update_status(Order.Status.PREPARING, request.user)

        return Response({"status": "Kitchen status updated successfully", "order_item": OrderItemSerializer(order_item).data})

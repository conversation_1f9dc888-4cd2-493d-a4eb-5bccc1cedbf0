# Generated by Django 5.2.6 on 2025-09-11 07:54

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Order",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("order_number", models.CharField(max_length=50, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("confirmed", "Confirmed"),
                            ("preparing", "Preparing"),
                            ("ready", "Ready"),
                            ("served", "Served"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "order_type",
                    models.CharField(
                        choices=[
                            ("dine_in", "Dine In"),
                            ("takeaway", "Takeaway"),
                            ("delivery", "Delivery"),
                            ("online", "Online Order"),
                        ],
                        default="dine_in",
                        max_length=20,
                    ),
                ),
                ("customer_name", models.CharField(blank=True, max_length=100)),
                ("customer_phone", models.CharField(blank=True, max_length=20)),
                ("customer_email", models.EmailField(blank=True, max_length=254)),
                ("delivery_address", models.TextField(blank=True)),
                ("delivery_phone", models.CharField(blank=True, max_length=20)),
                ("delivery_instructions", models.TextField(blank=True)),
                (
                    "estimated_delivery_time",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("order_time", models.DateTimeField(auto_now_add=True)),
                ("confirmed_time", models.DateTimeField(blank=True, null=True)),
                ("ready_time", models.DateTimeField(blank=True, null=True)),
                ("served_time", models.DateTimeField(blank=True, null=True)),
                ("completed_time", models.DateTimeField(blank=True, null=True)),
                (
                    "subtotal",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "service_charge",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "discount_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "tip_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "total_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "payment_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("partial", "Partially Paid"),
                            ("paid", "Paid"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "paid_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("special_instructions", models.TextField(blank=True)),
                ("kitchen_notes", models.TextField(blank=True)),
                ("internal_notes", models.TextField(blank=True)),
                (
                    "discount_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("percentage", "Percentage"),
                            ("fixed", "Fixed Amount"),
                            ("coupon", "Coupon"),
                            ("loyalty", "Loyalty Points"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "discount_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("discount_reason", models.CharField(blank=True, max_length=200)),
            ],
            options={
                "verbose_name": "Order",
                "verbose_name_plural": "Orders",
                "db_table": "orders",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="OrderItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("quantity", models.PositiveIntegerField(default=1)),
                ("unit_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("total_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("special_instructions", models.TextField(blank=True)),
                (
                    "kitchen_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("preparing", "Preparing"),
                            ("ready", "Ready"),
                            ("served", "Served"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "verbose_name": "Order Item",
                "verbose_name_plural": "Order Items",
                "db_table": "order_items",
            },
        ),
        migrations.CreateModel(
            name="OrderItemModifier",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("quantity", models.PositiveIntegerField(default=1)),
                ("unit_price", models.DecimalField(decimal_places=2, max_digits=10)),
            ],
            options={
                "db_table": "order_item_modifiers",
            },
        ),
        migrations.CreateModel(
            name="OrderStatusLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("old_status", models.CharField(max_length=20)),
                ("new_status", models.CharField(max_length=20)),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Order Status Log",
                "verbose_name_plural": "Order Status Logs",
                "db_table": "order_status_logs",
                "ordering": ["-created_at"],
            },
        ),
    ]

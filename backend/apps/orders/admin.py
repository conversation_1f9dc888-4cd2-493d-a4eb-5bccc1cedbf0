"""
Orders admin configuration.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import Order, OrderItem, OrderItemModifier, OrderStatusLog


class OrderItemModifierInline(admin.TabularInline):
    model = OrderItemModifier
    extra = 0
    readonly_fields = ['unit_price']


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['total_price', 'base_total']
    inlines = [OrderItemModifierInline]


class OrderStatusLogInline(admin.TabularInline):
    model = OrderStatusLog
    extra = 0
    readonly_fields = ['created_at']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = [
        'order_number', 'restaurant', 'status', 'order_type', 'customer_display',
        'table', 'total_amount', 'payment_status', 'order_time'
    ]
    list_filter = [
        'restaurant', 'status', 'order_type', 'payment_status', 'order_time'
    ]
    search_fields = [
        'order_number', 'customer_name', 'customer_phone', 'customer_email'
    ]
    ordering = ['-order_time']
    readonly_fields = [
        'order_number', 'order_time', 'confirmed_time', 'ready_time',
        'served_time', 'completed_time', 'subtotal', 'tax_amount',
        'service_charge', 'total_amount', 'is_paid', 'remaining_amount',
        'preparation_time_minutes', 'created_at', 'updated_at'
    ]
    inlines = [OrderItemInline, OrderStatusLogInline]
    
    fieldsets = (
        ('Order Information', {
            'fields': (
                'order_number', 'status', 'order_type', 'payment_status'
            )
        }),
        ('Customer Information', {
            'fields': (
                'customer', 'customer_name', 'customer_phone', 'customer_email'
            )
        }),
        ('Staff & Table', {
            'fields': ('waiter', 'cashier', 'table')
        }),
        ('Delivery Information', {
            'fields': (
                'delivery_address', 'delivery_phone', 'delivery_instructions',
                'estimated_delivery_time'
            ),
            'classes': ('collapse',)
        }),
        ('Timing', {
            'fields': (
                'order_time', 'confirmed_time', 'ready_time', 'served_time',
                'completed_time', 'preparation_time_minutes'
            ),
            'classes': ('collapse',)
        }),
        ('Pricing', {
            'fields': (
                'subtotal', 'tax_amount', 'service_charge', 'discount_amount',
                'tip_amount', 'total_amount', 'paid_amount', 'remaining_amount',
                'is_paid'
            )
        }),
        ('Discount', {
            'fields': ('discount_type', 'discount_value', 'discount_reason'),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('special_instructions', 'kitchen_notes', 'internal_notes')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_display(self, obj):
        if obj.customer:
            return obj.customer.get_full_name()
        return obj.customer_name or 'Walk-in Customer'
    customer_display.short_description = 'Customer'


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = [
        'menu_item', 'order', 'quantity', 'unit_price', 'total_price',
        'kitchen_status', 'created_at'
    ]
    list_filter = ['kitchen_status', 'order__restaurant', 'created_at']
    search_fields = ['menu_item__name', 'order__order_number']
    ordering = ['-created_at']
    readonly_fields = ['total_price', 'base_total', 'created_at', 'updated_at']
    inlines = [OrderItemModifierInline]
    
    fieldsets = (
        ('Item Information', {
            'fields': ('order', 'menu_item', 'quantity', 'unit_price', 'total_price')
        }),
        ('Kitchen', {
            'fields': ('kitchen_status', 'special_instructions')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(OrderStatusLog)
class OrderStatusLogAdmin(admin.ModelAdmin):
    list_display = [
        'order', 'old_status', 'new_status', 'changed_by', 'created_at'
    ]
    list_filter = ['old_status', 'new_status', 'created_at']
    search_fields = ['order__order_number', 'notes']
    ordering = ['-created_at']
    readonly_fields = ['created_at']

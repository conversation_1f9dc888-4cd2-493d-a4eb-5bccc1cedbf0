"""
Order serializers for Restaurant POS system.
"""

from rest_framework import serializers
from decimal import Decimal
from .models import Order, OrderItem, OrderItemModifier, OrderStatusLog
from apps.menu.models import MenuItem, MenuModifier
from apps.restaurants.models import Table
from apps.users.models import User


class OrderItemModifierSerializer(serializers.ModelSerializer):
    """
    Order item modifier serializer.
    """
    modifier_name = serializers.CharField(source='modifier.name', read_only=True)
    
    class Meta:
        model = OrderItemModifier
        fields = [
            'id', 'modifier', 'modifier_name', 'quantity', 'unit_price',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class OrderItemSerializer(serializers.ModelSerializer):
    """
    Order item serializer.
    """
    menu_item_name = serializers.CharField(source='menu_item.name', read_only=True)
    menu_item_price = serializers.DecimalField(source='menu_item.price', max_digits=10, decimal_places=2, read_only=True)
    modifiers = OrderItemModifierSerializer(many=True, read_only=True)
    base_total = serializers.ReadOnlyField()
    
    class Meta:
        model = OrderItem
        fields = [
            'id', 'menu_item', 'menu_item_name', 'menu_item_price', 'quantity',
            'unit_price', 'total_price', 'special_instructions', 'kitchen_status',
            'modifiers', 'base_total', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_price', 'created_at', 'updated_at']


class OrderItemCreateSerializer(serializers.Serializer):
    """
    Serializer for creating order items with modifiers.
    """
    menu_item_id = serializers.UUIDField()
    quantity = serializers.IntegerField(min_value=1, default=1)
    special_instructions = serializers.CharField(max_length=500, required=False, allow_blank=True)
    modifiers = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True
    )

    def validate_menu_item_id(self, value):
        try:
            menu_item = MenuItem.objects.get(id=value)
            if not menu_item.is_available:
                raise serializers.ValidationError("Menu item is not available")
            return value
        except MenuItem.DoesNotExist:
            raise serializers.ValidationError("Menu item not found")

    def validate_modifiers(self, value):
        validated_modifiers = []
        for modifier_data in value:
            try:
                modifier_id = modifier_data.get('modifier_id')
                quantity = modifier_data.get('quantity', 1)
                
                modifier = MenuModifier.objects.get(id=modifier_id)
                if not modifier.is_available:
                    raise serializers.ValidationError(f"Modifier {modifier.name} is not available")
                
                validated_modifiers.append({
                    'modifier': modifier,
                    'quantity': quantity
                })
            except (MenuModifier.DoesNotExist, KeyError):
                raise serializers.ValidationError("Invalid modifier data")
        
        return validated_modifiers


class OrderStatusLogSerializer(serializers.ModelSerializer):
    """
    Order status log serializer.
    """
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)
    
    class Meta:
        model = OrderStatusLog
        fields = [
            'id', 'old_status', 'new_status', 'changed_by', 'changed_by_name',
            'notes', 'created_at'
        ]


class OrderSerializer(serializers.ModelSerializer):
    """
    Order serializer.
    """
    items = OrderItemSerializer(many=True, read_only=True)
    status_logs = OrderStatusLogSerializer(many=True, read_only=True)
    customer_name_display = serializers.SerializerMethodField()
    waiter_name = serializers.CharField(source='waiter.get_full_name', read_only=True)
    cashier_name = serializers.CharField(source='cashier.get_full_name', read_only=True)
    table_name = serializers.CharField(source='table.display_name', read_only=True)
    is_paid = serializers.ReadOnlyField()
    remaining_amount = serializers.ReadOnlyField()
    preparation_time_minutes = serializers.ReadOnlyField()
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'status', 'order_type', 'customer', 'customer_name',
            'customer_phone', 'customer_email', 'customer_name_display', 'waiter',
            'waiter_name', 'cashier', 'cashier_name', 'table', 'table_name',
            'delivery_address', 'delivery_phone', 'delivery_instructions',
            'estimated_delivery_time', 'order_time', 'confirmed_time', 'ready_time',
            'served_time', 'completed_time', 'subtotal', 'tax_amount', 'service_charge',
            'discount_amount', 'tip_amount', 'total_amount', 'payment_status',
            'paid_amount', 'special_instructions', 'kitchen_notes', 'internal_notes',
            'discount_type', 'discount_value', 'discount_reason', 'is_paid',
            'remaining_amount', 'preparation_time_minutes', 'items', 'status_logs',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'order_number', 'order_time', 'confirmed_time', 'ready_time',
            'served_time', 'completed_time', 'subtotal', 'tax_amount', 'service_charge',
            'total_amount', 'created_at', 'updated_at'
        ]

    def get_customer_name_display(self, obj):
        if obj.customer:
            return obj.customer.get_full_name()
        return obj.customer_name or 'Walk-in Customer'

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class OrderCreateSerializer(serializers.Serializer):
    """
    Serializer for creating orders with items.
    """
    order_type = serializers.ChoiceField(choices=Order.OrderType.choices, default=Order.OrderType.DINE_IN)
    table_id = serializers.UUIDField(required=False, allow_null=True)
    customer_name = serializers.CharField(max_length=100, required=False, allow_blank=True)
    customer_phone = serializers.CharField(max_length=20, required=False, allow_blank=True)
    customer_email = serializers.EmailField(required=False, allow_blank=True)
    delivery_address = serializers.CharField(required=False, allow_blank=True)
    delivery_phone = serializers.CharField(max_length=20, required=False, allow_blank=True)
    delivery_instructions = serializers.CharField(required=False, allow_blank=True)
    special_instructions = serializers.CharField(required=False, allow_blank=True)
    items = OrderItemCreateSerializer(many=True, allow_empty=False)

    def validate_table_id(self, value):
        if value:
            try:
                from apps.core.middleware import get_current_restaurant
                restaurant = get_current_restaurant()
                table = Table.objects.get(id=value, restaurant=restaurant)
                if table.status != Table.Status.AVAILABLE:
                    raise serializers.ValidationError("Table is not available")
                return value
            except Table.DoesNotExist:
                raise serializers.ValidationError("Table not found")
        return value

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        
        items_data = validated_data.pop('items')
        restaurant = get_current_restaurant()
        
        # Create order
        order = Order.objects.create(
            restaurant=restaurant,
            **validated_data
        )
        
        # Create order items
        for item_data in items_data:
            menu_item = MenuItem.objects.get(id=item_data['menu_item_id'])
            
            order_item = OrderItem.objects.create(
                order=order,
                menu_item=menu_item,
                quantity=item_data['quantity'],
                unit_price=menu_item.price,
                special_instructions=item_data.get('special_instructions', '')
            )
            
            # Add modifiers
            for modifier_data in item_data.get('modifiers', []):
                OrderItemModifier.objects.create(
                    order_item=order_item,
                    modifier=modifier_data['modifier'],
                    quantity=modifier_data['quantity'],
                    unit_price=modifier_data['modifier'].price_adjustment
                )
            
            # Recalculate order item total
            order_item.save()
        
        # Calculate order totals
        order.calculate_totals()
        
        # Update table status if dine-in
        if order.table:
            order.table.status = Table.Status.OCCUPIED
            order.table.save()
        
        return order


class OrderListSerializer(serializers.ModelSerializer):
    """
    Simplified order serializer for list views.
    """
    customer_name_display = serializers.SerializerMethodField()
    table_name = serializers.CharField(source='table.display_name', read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'status', 'order_type', 'customer_name_display',
            'table_name', 'total_amount', 'payment_status', 'items_count',
            'order_time', 'estimated_delivery_time'
        ]

    def get_customer_name_display(self, obj):
        if obj.customer:
            return obj.customer.get_full_name()
        return obj.customer_name or 'Walk-in Customer'

    def get_items_count(self, obj):
        return obj.items.count()


class OrderUpdateStatusSerializer(serializers.Serializer):
    """
    Serializer for updating order status.
    """
    status = serializers.ChoiceField(choices=Order.Status.choices)
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)


class OrderDiscountSerializer(serializers.Serializer):
    """
    Serializer for applying discounts to orders.
    """
    discount_type = serializers.ChoiceField(choices=[
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount'),
        ('coupon', 'Coupon'),
        ('loyalty', 'Loyalty Points'),
    ])
    discount_value = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0)
    discount_reason = serializers.CharField(max_length=200, required=False, allow_blank=True)

    def validate(self, data):
        if data['discount_type'] == 'percentage' and data['discount_value'] > 100:
            raise serializers.ValidationError("Percentage discount cannot exceed 100%")
        return data

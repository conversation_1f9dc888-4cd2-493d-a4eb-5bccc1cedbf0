"""
Order models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from decimal import Decimal
from apps.core.models import BaseModel, TenantAwareModel, SoftDeleteModel
from apps.restaurants.models import Restaurant, Table
from apps.menu.models import MenuItem, MenuModifier
from apps.users.models import User


class Order(TenantAwareModel, SoftDeleteModel):
    """
    Main order model for restaurant orders.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        CONFIRMED = 'confirmed', _('Confirmed')
        PREPARING = 'preparing', _('Preparing')
        READY = 'ready', _('Ready')
        SERVED = 'served', _('Served')
        COMPLETED = 'completed', _('Completed')
        CANCELLED = 'cancelled', _('Cancelled')
        REFUNDED = 'refunded', _('Refunded')

    class OrderType(models.TextChoices):
        DINE_IN = 'dine_in', _('Dine In')
        TAKEAWAY = 'takeaway', _('Takeaway')
        DELIVERY = 'delivery', _('Delivery')
        ONLINE = 'online', _('Online Order')

    class PaymentStatus(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PARTIAL = 'partial', _('Partially Paid')
        PAID = 'paid', _('Paid')
        REFUNDED = 'refunded', _('Refunded')

    # Order identification
    order_number = models.CharField(max_length=50, unique=True)
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    order_type = models.CharField(
        max_length=20,
        choices=OrderType.choices,
        default=OrderType.DINE_IN
    )
    
    # Customer and staff information
    customer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_orders'
    )
    customer_name = models.CharField(max_length=100, blank=True)
    customer_phone = models.CharField(max_length=20, blank=True)
    customer_email = models.EmailField(blank=True)
    
    # Staff assignments
    waiter = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='waiter_orders'
    )
    cashier = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cashier_orders'
    )
    
    # Table assignment (for dine-in orders)
    table = models.ForeignKey(
        Table,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='orders'
    )
    
    # Delivery information (for delivery orders)
    delivery_address = models.TextField(blank=True)
    delivery_phone = models.CharField(max_length=20, blank=True)
    delivery_instructions = models.TextField(blank=True)
    estimated_delivery_time = models.DateTimeField(null=True, blank=True)
    
    # Order timing
    order_time = models.DateTimeField(auto_now_add=True)
    confirmed_time = models.DateTimeField(null=True, blank=True)
    ready_time = models.DateTimeField(null=True, blank=True)
    served_time = models.DateTimeField(null=True, blank=True)
    completed_time = models.DateTimeField(null=True, blank=True)
    
    # Pricing
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    service_charge = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tip_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Payment information
    payment_status = models.CharField(
        max_length=20,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING
    )
    paid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Special instructions and notes
    special_instructions = models.TextField(blank=True)
    kitchen_notes = models.TextField(blank=True)
    internal_notes = models.TextField(blank=True)
    
    # Discount information
    discount_type = models.CharField(
        max_length=20,
        choices=[
            ('percentage', _('Percentage')),
            ('fixed', _('Fixed Amount')),
            ('coupon', _('Coupon')),
            ('loyalty', _('Loyalty Points')),
        ],
        blank=True
    )
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_reason = models.CharField(max_length=200, blank=True)
    
    class Meta:
        db_table = 'orders'
        verbose_name = _('Order')
        verbose_name_plural = _('Orders')
        ordering = ['-created_at']

    def __str__(self):
        return f"Order {self.order_number} - {self.restaurant.name}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            # Generate order number
            from django.utils import timezone
            today = timezone.now().date()
            
            # Get restaurant settings for order prefix
            try:
                settings = self.restaurant.settings
                prefix = settings.order_number_prefix
            except:
                prefix = 'ORD'
            
            # Find last order number for today
            last_order = Order.objects.filter(
                restaurant=self.restaurant,
                created_at__date=today
            ).order_by('-created_at').first()
            
            if last_order and last_order.order_number.startswith(prefix):
                try:
                    last_number = int(last_order.order_number.split('-')[-1])
                    self.order_number = f"{prefix}-{today.strftime('%Y%m%d')}-{last_number + 1:04d}"
                except (IndexError, ValueError):
                    self.order_number = f"{prefix}-{today.strftime('%Y%m%d')}-0001"
            else:
                self.order_number = f"{prefix}-{today.strftime('%Y%m%d')}-0001"
        
        super().save(*args, **kwargs)

    def calculate_totals(self):
        """Calculate and update order totals."""
        # Calculate subtotal from order items
        self.subtotal = sum(item.total_price for item in self.items.all())
        
        # Calculate tax (get rate from restaurant settings)
        tax_rate = self.restaurant.tax_rate or Decimal('0.0875')
        self.tax_amount = self.subtotal * tax_rate
        
        # Calculate service charge (for dine-in orders)
        if self.order_type == self.OrderType.DINE_IN:
            service_rate = self.restaurant.service_charge_rate or Decimal('0.0')
            self.service_charge = self.subtotal * service_rate
        else:
            self.service_charge = Decimal('0.0')
        
        # Calculate total
        self.total_amount = (
            self.subtotal + 
            self.tax_amount + 
            self.service_charge + 
            self.tip_amount - 
            self.discount_amount
        )
        
        self.save(update_fields=[
            'subtotal', 'tax_amount', 'service_charge', 'total_amount'
        ])

    @property
    def is_paid(self):
        """Check if order is fully paid."""
        return self.payment_status == self.PaymentStatus.PAID

    @property
    def remaining_amount(self):
        """Calculate remaining amount to be paid."""
        return max(self.total_amount - self.paid_amount, Decimal('0.0'))

    @property
    def preparation_time_minutes(self):
        """Calculate total preparation time for all items."""
        return sum(item.menu_item.prep_time_minutes * item.quantity for item in self.items.all())

    def update_status(self, new_status, user=None):
        """Update order status with timestamp."""
        from django.utils import timezone
        
        old_status = self.status
        self.status = new_status
        
        # Update relevant timestamps
        now = timezone.now()
        if new_status == self.Status.CONFIRMED:
            self.confirmed_time = now
        elif new_status == self.Status.READY:
            self.ready_time = now
        elif new_status == self.Status.SERVED:
            self.served_time = now
        elif new_status == self.Status.COMPLETED:
            self.completed_time = now
        
        self.save()
        
        # Create status change log
        OrderStatusLog.objects.create(
            order=self,
            old_status=old_status,
            new_status=new_status,
            changed_by=user,
            notes=f'Status changed from {old_status} to {new_status}'
        )

    def apply_discount(self, discount_type, discount_value, reason=''):
        """Apply discount to order."""
        self.discount_type = discount_type
        self.discount_value = discount_value
        self.discount_reason = reason
        
        if discount_type == 'percentage':
            self.discount_amount = self.subtotal * (discount_value / 100)
        elif discount_type == 'fixed':
            self.discount_amount = min(discount_value, self.subtotal)
        
        self.calculate_totals()


class OrderItem(BaseModel):
    """
    Individual items within an order.
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='items'
    )
    menu_item = models.ForeignKey(
        MenuItem,
        on_delete=models.CASCADE,
        related_name='order_items'
    )
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Item customization
    special_instructions = models.TextField(blank=True)
    
    # Kitchen status
    kitchen_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', _('Pending')),
            ('preparing', _('Preparing')),
            ('ready', _('Ready')),
            ('served', _('Served')),
        ],
        default='pending'
    )
    
    class Meta:
        db_table = 'order_items'
        verbose_name = _('Order Item')
        verbose_name_plural = _('Order Items')

    def __str__(self):
        return f"{self.menu_item.name} x{self.quantity} - Order {self.order.order_number}"

    def save(self, *args, **kwargs):
        # Calculate total price including modifiers
        modifier_total = sum(
            modifier.modifier.price_adjustment * self.quantity 
            for modifier in self.modifiers.all()
        )
        self.total_price = (self.unit_price + modifier_total) * self.quantity
        super().save(*args, **kwargs)

    @property
    def base_total(self):
        """Calculate base total without modifiers."""
        return self.unit_price * self.quantity


class OrderItemModifier(BaseModel):
    """
    Modifiers applied to order items.
    """
    order_item = models.ForeignKey(
        OrderItem,
        on_delete=models.CASCADE,
        related_name='modifiers'
    )
    modifier = models.ForeignKey(
        MenuModifier,
        on_delete=models.CASCADE,
        related_name='order_item_modifiers'
    )
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    class Meta:
        db_table = 'order_item_modifiers'
        unique_together = ['order_item', 'modifier']

    def __str__(self):
        return f"{self.modifier.name} - {self.order_item.menu_item.name}"

    def save(self, *args, **kwargs):
        if not self.unit_price:
            self.unit_price = self.modifier.price_adjustment
        super().save(*args, **kwargs)


class OrderStatusLog(BaseModel):
    """
    Log of order status changes for audit trail.
    """
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='status_logs'
    )
    old_status = models.CharField(max_length=20)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='order_status_changes'
    )
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'order_status_logs'
        verbose_name = _('Order Status Log')
        verbose_name_plural = _('Order Status Logs')
        ordering = ['-created_at']

    def __str__(self):
        return f"Order {self.order.order_number}: {self.old_status} → {self.new_status}"

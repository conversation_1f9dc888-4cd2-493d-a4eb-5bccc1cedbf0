"""
User models for Restaurant POS system.
"""

import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.core.models import BaseModel, TenantAwareModel


class User(AbstractUser):
    """
    Custom User model with additional fields for Restaurant POS.
    """

    class UserType(models.TextChoices):
        SUPER_ADMIN = "super_admin", _("Super Admin")
        RESTAURANT_OWNER = "restaurant_owner", _("Restaurant Owner")
        MANAGER = "manager", _("Manager")
        CASHIER = "cashier", _("Cashier")
        WAITER = "waiter", _("Waiter")
        KITCHEN_STAFF = "kitchen_staff", _("Kitchen Staff")
        CUSTOMER = "customer", _("Customer")

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(_("email address"), unique=True)
    phone = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    user_type = models.CharField(max_length=20, choices=UserType.choices, default=UserType.CUSTOMER)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # For restaurant owners and staff
    restaurant = models.ForeignKey("restaurants.Restaurant", on_delete=models.CASCADE, null=True, blank=True, related_name="users")

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username", "first_name", "last_name"]

    class Meta:
        db_table = "users"
        verbose_name = _("User")
        verbose_name_plural = _("Users")

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def has_restaurant_access(self, restaurant):
        """Check if user has access to a specific restaurant."""
        if self.user_type == self.UserType.SUPER_ADMIN:
            return True
        return self.restaurant == restaurant


class StaffProfile(TenantAwareModel):
    """
    Extended profile for restaurant staff members.
    """

    class ShiftType(models.TextChoices):
        MORNING = "morning", _("Morning")
        AFTERNOON = "afternoon", _("Afternoon")
        EVENING = "evening", _("Evening")
        NIGHT = "night", _("Night")
        FULL_DAY = "full_day", _("Full Day")

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="staff_profile")
    employee_id = models.CharField(max_length=20, unique=True)
    position = models.CharField(max_length=100)
    department = models.CharField(max_length=100, blank=True)
    hire_date = models.DateField()
    salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    hourly_rate = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)

    # Shift preferences
    preferred_shift = models.CharField(max_length=20, choices=ShiftType.choices, default=ShiftType.FULL_DAY)

    # Permissions
    can_process_refunds = models.BooleanField(default=False)
    can_modify_prices = models.BooleanField(default=False)
    can_access_reports = models.BooleanField(default=False)
    can_manage_inventory = models.BooleanField(default=False)

    # Contact info
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)

    class Meta:
        db_table = "staff_profiles"
        verbose_name = _("Staff Profile")
        verbose_name_plural = _("Staff Profiles")
        unique_together = ["restaurant", "employee_id"]

    def __str__(self):
        return f"{self.user.full_name} - {self.position}"


class CustomerProfile(BaseModel):
    """
    Profile for customers with loyalty and preferences.
    """

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="customer_profile")
    date_of_birth = models.DateField(null=True, blank=True)
    loyalty_points = models.PositiveIntegerField(default=0)
    total_orders = models.PositiveIntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Preferences
    dietary_restrictions = models.TextField(blank=True)
    favorite_items = models.ManyToManyField("menu.MenuItem", blank=True, related_name="favorited_by")

    # Marketing preferences
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    promotional_emails = models.BooleanField(default=True)

    class Meta:
        db_table = "customer_profiles"
        verbose_name = _("Customer Profile")
        verbose_name_plural = _("Customer Profiles")

    def __str__(self):
        return f"{self.user.full_name} - {self.loyalty_points} points"

    def add_loyalty_points(self, points):
        """Add loyalty points to customer."""
        self.loyalty_points += points
        self.save(update_fields=["loyalty_points"])

    def redeem_loyalty_points(self, points):
        """Redeem loyalty points."""
        if self.loyalty_points >= points:
            self.loyalty_points -= points
            self.save(update_fields=["loyalty_points"])
            return True
        return False


class UserSession(BaseModel):
    """
    Track user sessions for security and analytics.
    """

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sessions")
    session_key = models.CharField(max_length=400, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    login_time = models.DateTimeField(auto_now_add=True)
    logout_time = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # For staff - track which terminal/device
    terminal_id = models.CharField(max_length=50, blank=True)
    location = models.CharField(max_length=100, blank=True)

    class Meta:
        db_table = "user_sessions"
        verbose_name = _("User Session")
        verbose_name_plural = _("User Sessions")

    def __str__(self):
        return f"{self.user.email} - {self.login_time}"

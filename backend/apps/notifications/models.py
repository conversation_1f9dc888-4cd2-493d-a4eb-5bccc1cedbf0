"""
Notifications & Integrations models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.fields import JSONField
from apps.core.models import BaseModel, TenantAwareModel
from apps.restaurants.models import Restaurant
from apps.users.models import User


class NotificationTemplate(TenantAwareModel):
    """
    Notification template for different types of notifications.
    """
    
    class NotificationType(models.TextChoices):
        ORDER_CONFIRMED = 'order_confirmed', _('Order Confirmed')
        ORDER_READY = 'order_ready', _('Order Ready')
        ORDER_DELIVERED = 'order_delivered', _('Order Delivered')
        PAYMENT_RECEIVED = 'payment_received', _('Payment Received')
        LOW_STOCK = 'low_stock', _('Low Stock Alert')
        STAFF_SHIFT = 'staff_shift', _('Staff Shift Reminder')
        CUSTOMER_BIRTHDAY = 'customer_birthday', _('Customer Birthday')
        MARKETING_PROMO = 'marketing_promo', _('Marketing Promotion')
        SYSTEM_ALERT = 'system_alert', _('System Alert')

    class Channel(models.TextChoices):
        EMAIL = 'email', _('Email')
        SMS = 'sms', _('SMS')
        PUSH = 'push', _('Push Notification')
        IN_APP = 'in_app', _('In-App Notification')
        WEBHOOK = 'webhook', _('Webhook')

    name = models.CharField(max_length=200)
    notification_type = models.CharField(
        max_length=30,
        choices=NotificationType.choices
    )
    channel = models.CharField(
        max_length=20,
        choices=Channel.choices
    )
    
    # Template content
    subject = models.CharField(max_length=200, blank=True)
    email_template = models.TextField(blank=True)
    sms_template = models.TextField(blank=True)
    push_template = models.TextField(blank=True)
    
    # Template variables (JSON schema)
    template_variables = models.JSONField(
        default=dict,
        help_text="Available variables for template substitution"
    )
    
    is_active = models.BooleanField(default=True)
    
    # Delivery settings
    send_immediately = models.BooleanField(default=True)
    delay_minutes = models.PositiveIntegerField(
        default=0,
        help_text="Delay in minutes before sending"
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_notification_templates'
    )
    
    class Meta:
        db_table = 'notification_templates'
        verbose_name = _('Notification Template')
        verbose_name_plural = _('Notification Templates')
        ordering = ['name']
        unique_together = ['restaurant', 'notification_type', 'channel']

    def __str__(self):
        return f"{self.name} - {self.get_channel_display()}"


class Notification(TenantAwareModel):
    """
    Individual notification record.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        SENT = 'sent', _('Sent')
        DELIVERED = 'delivered', _('Delivered')
        FAILED = 'failed', _('Failed')
        READ = 'read', _('Read')

    template = models.ForeignKey(
        NotificationTemplate,
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    
    # Recipient information
    recipient_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='received_notifications'
    )
    recipient_email = models.EmailField(blank=True)
    recipient_phone = models.CharField(max_length=20, blank=True)
    
    # Notification content (after template processing)
    subject = models.CharField(max_length=200, blank=True)
    content = models.TextField()
    
    # Delivery information
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Error information
    error_message = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    
    # Related objects
    related_order = models.ForeignKey(
        'orders.Order',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='notifications'
    )
    related_customer = models.ForeignKey(
        'customers.Customer',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='notifications'
    )
    
    # Metadata
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'notifications'
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.template.name} - {self.recipient_email or self.recipient_phone or self.recipient_user}"

    def mark_as_read(self):
        """Mark notification as read."""
        if self.status == self.Status.DELIVERED:
            self.status = self.Status.READ
            self.read_at = timezone.now()
            self.save()


class WebhookEndpoint(TenantAwareModel):
    """
    Webhook endpoint configuration for third-party integrations.
    """
    
    class EventType(models.TextChoices):
        ORDER_CREATED = 'order.created', _('Order Created')
        ORDER_UPDATED = 'order.updated', _('Order Updated')
        ORDER_COMPLETED = 'order.completed', _('Order Completed')
        PAYMENT_RECEIVED = 'payment.received', _('Payment Received')
        INVENTORY_LOW = 'inventory.low', _('Inventory Low')
        CUSTOMER_CREATED = 'customer.created', _('Customer Created')

    name = models.CharField(max_length=200)
    url = models.URLField()
    
    # Events to send
    event_types = models.JSONField(
        default=list,
        help_text="List of event types to send to this endpoint"
    )
    
    # Authentication
    secret_key = models.CharField(max_length=200, blank=True)
    headers = models.JSONField(
        default=dict,
        help_text="Additional headers to send with webhook"
    )
    
    # Configuration
    is_active = models.BooleanField(default=True)
    timeout_seconds = models.PositiveIntegerField(default=30)
    max_retries = models.PositiveIntegerField(default=3)
    
    # Statistics
    total_sent = models.PositiveIntegerField(default=0)
    total_failed = models.PositiveIntegerField(default=0)
    last_sent_at = models.DateTimeField(null=True, blank=True)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_webhook_endpoints'
    )
    
    class Meta:
        db_table = 'webhook_endpoints'
        verbose_name = _('Webhook Endpoint')
        verbose_name_plural = _('Webhook Endpoints')
        ordering = ['name']

    def __str__(self):
        return self.name


class WebhookDelivery(TenantAwareModel):
    """
    Webhook delivery log.
    """
    
    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        SUCCESS = 'success', _('Success')
        FAILED = 'failed', _('Failed')
        RETRYING = 'retrying', _('Retrying')

    endpoint = models.ForeignKey(
        WebhookEndpoint,
        on_delete=models.CASCADE,
        related_name='deliveries'
    )
    
    event_type = models.CharField(max_length=50)
    payload = models.JSONField()
    
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    
    # Response information
    response_status_code = models.PositiveIntegerField(null=True, blank=True)
    response_body = models.TextField(blank=True)
    response_headers = models.JSONField(default=dict, blank=True)
    
    # Timing
    sent_at = models.DateTimeField(null=True, blank=True)
    response_time_ms = models.PositiveIntegerField(null=True, blank=True)
    
    # Error information
    error_message = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    next_retry_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'webhook_deliveries'
        verbose_name = _('Webhook Delivery')
        verbose_name_plural = _('Webhook Deliveries')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.endpoint.name} - {self.event_type} - {self.status}"


class EmailProvider(TenantAwareModel):
    """
    Email service provider configuration.
    """
    
    class ProviderType(models.TextChoices):
        SMTP = 'smtp', _('SMTP')
        SENDGRID = 'sendgrid', _('SendGrid')
        MAILGUN = 'mailgun', _('Mailgun')
        SES = 'ses', _('Amazon SES')
        POSTMARK = 'postmark', _('Postmark')

    name = models.CharField(max_length=200)
    provider_type = models.CharField(
        max_length=20,
        choices=ProviderType.choices,
        default=ProviderType.SMTP
    )
    
    # Configuration
    config = models.JSONField(
        default=dict,
        help_text="Provider-specific configuration"
    )
    
    # Default sender information
    from_email = models.EmailField()
    from_name = models.CharField(max_length=200)
    reply_to_email = models.EmailField(blank=True)
    
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    
    # Statistics
    emails_sent = models.PositiveIntegerField(default=0)
    emails_delivered = models.PositiveIntegerField(default=0)
    emails_bounced = models.PositiveIntegerField(default=0)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_email_providers'
    )
    
    class Meta:
        db_table = 'email_providers'
        verbose_name = _('Email Provider')
        verbose_name_plural = _('Email Providers')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_provider_type_display()})"

    def save(self, *args, **kwargs):
        # Ensure only one default provider per restaurant
        if self.is_default:
            EmailProvider.objects.filter(
                restaurant=self.restaurant,
                is_default=True
            ).exclude(id=self.id).update(is_default=False)
        
        super().save(*args, **kwargs)


class SMSProvider(TenantAwareModel):
    """
    SMS service provider configuration.
    """
    
    class ProviderType(models.TextChoices):
        TWILIO = 'twilio', _('Twilio')
        NEXMO = 'nexmo', _('Vonage (Nexmo)')
        AWS_SNS = 'aws_sns', _('AWS SNS')
        CLICKSEND = 'clicksend', _('ClickSend')

    name = models.CharField(max_length=200)
    provider_type = models.CharField(
        max_length=20,
        choices=ProviderType.choices,
        default=ProviderType.TWILIO
    )
    
    # Configuration
    config = models.JSONField(
        default=dict,
        help_text="Provider-specific configuration"
    )
    
    # Default sender information
    from_number = models.CharField(max_length=20)
    
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    
    # Statistics
    sms_sent = models.PositiveIntegerField(default=0)
    sms_delivered = models.PositiveIntegerField(default=0)
    sms_failed = models.PositiveIntegerField(default=0)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_sms_providers'
    )
    
    class Meta:
        db_table = 'sms_providers'
        verbose_name = _('SMS Provider')
        verbose_name_plural = _('SMS Providers')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_provider_type_display()})"

    def save(self, *args, **kwargs):
        # Ensure only one default provider per restaurant
        if self.is_default:
            SMSProvider.objects.filter(
                restaurant=self.restaurant,
                is_default=True
            ).exclude(id=self.id).update(is_default=False)
        
        super().save(*args, **kwargs)


class Integration(TenantAwareModel):
    """
    Third-party service integrations.
    """
    
    class IntegrationType(models.TextChoices):
        DELIVERY = 'delivery', _('Delivery Platform')
        PAYMENT = 'payment', _('Payment Gateway')
        ACCOUNTING = 'accounting', _('Accounting Software')
        INVENTORY = 'inventory', _('Inventory Management')
        MARKETING = 'marketing', _('Marketing Platform')
        ANALYTICS = 'analytics', _('Analytics Platform')

    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        ERROR = 'error', _('Error')
        PENDING = 'pending', _('Pending Setup')

    name = models.CharField(max_length=200)
    integration_type = models.CharField(
        max_length=20,
        choices=IntegrationType.choices
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    
    # Integration configuration
    config = models.JSONField(
        default=dict,
        help_text="Integration-specific configuration and credentials"
    )
    
    # Sync settings
    auto_sync = models.BooleanField(default=True)
    sync_frequency_minutes = models.PositiveIntegerField(default=60)
    last_sync_at = models.DateTimeField(null=True, blank=True)
    next_sync_at = models.DateTimeField(null=True, blank=True)
    
    # Error tracking
    error_count = models.PositiveIntegerField(default=0)
    last_error = models.TextField(blank=True)
    last_error_at = models.DateTimeField(null=True, blank=True)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_integrations'
    )
    
    class Meta:
        db_table = 'integrations'
        verbose_name = _('Integration')
        verbose_name_plural = _('Integrations')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.get_integration_type_display()}"

"""
Notification services for Restaurant POS system.
"""

import logging
import requests
from django.core.mail import send_mail
from django.template import Template, Context
from django.utils import timezone
from celery import shared_task
from .models import (
    NotificationTemplate, Notification, WebhookEndpoint, WebhookDelivery,
    EmailProvider, SMSProvider
)

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Service for sending notifications.
    """
    
    @staticmethod
    def send_notification(template_id, recipient_data, context_data=None):
        """
        Send a notification using a template.
        
        Args:
            template_id: ID of the notification template
            recipient_data: Dict with recipient info (user, email, phone)
            context_data: Dict with template context variables
        """
        try:
            template = NotificationTemplate.objects.get(id=template_id)
            
            # Create notification record
            notification = Notification.objects.create(
                restaurant=template.restaurant,
                template=template,
                recipient_user=recipient_data.get('user'),
                recipient_email=recipient_data.get('email', ''),
                recipient_phone=recipient_data.get('phone', ''),
                related_order=context_data.get('order') if context_data else None,
                related_customer=context_data.get('customer') if context_data else None,
                metadata=context_data or {}
            )
            
            # Process template
            content = NotificationService._process_template(template, context_data or {})
            notification.subject = content.get('subject', '')
            notification.content = content.get('content', '')
            notification.save()
            
            # Send based on channel
            if template.send_immediately:
                NotificationService._send_notification_now(notification)
            else:
                # Schedule for later (would use Celery in production)
                send_notification_task.apply_async(
                    args=[notification.id],
                    countdown=template.delay_minutes * 60
                )
            
            return notification
            
        except NotificationTemplate.DoesNotExist:
            logger.error(f"Notification template {template_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")
            return None
    
    @staticmethod
    def _process_template(template, context_data):
        """Process template with context data."""
        context = Context(context_data)
        
        content = {}
        
        if template.subject:
            subject_template = Template(template.subject)
            content['subject'] = subject_template.render(context)
        
        if template.channel == NotificationTemplate.Channel.EMAIL and template.email_template:
            email_template = Template(template.email_template)
            content['content'] = email_template.render(context)
        elif template.channel == NotificationTemplate.Channel.SMS and template.sms_template:
            sms_template = Template(template.sms_template)
            content['content'] = sms_template.render(context)
        elif template.channel == NotificationTemplate.Channel.PUSH and template.push_template:
            push_template = Template(template.push_template)
            content['content'] = push_template.render(context)
        
        return content
    
    @staticmethod
    def _send_notification_now(notification):
        """Send notification immediately."""
        try:
            if notification.template.channel == NotificationTemplate.Channel.EMAIL:
                EmailService.send_email(notification)
            elif notification.template.channel == NotificationTemplate.Channel.SMS:
                SMSService.send_sms(notification)
            elif notification.template.channel == NotificationTemplate.Channel.PUSH:
                PushService.send_push(notification)
            elif notification.template.channel == NotificationTemplate.Channel.IN_APP:
                # In-app notifications are just stored in database
                notification.status = Notification.Status.DELIVERED
                notification.delivered_at = timezone.now()
                notification.save()
            
        except Exception as e:
            notification.status = Notification.Status.FAILED
            notification.error_message = str(e)
            notification.save()
            logger.error(f"Failed to send notification {notification.id}: {str(e)}")


class EmailService:
    """
    Service for sending emails.
    """
    
    @staticmethod
    def send_email(notification):
        """Send email notification."""
        try:
            # Get email provider
            provider = EmailProvider.objects.filter(
                restaurant=notification.restaurant,
                is_active=True,
                is_default=True
            ).first()
            
            if not provider:
                raise Exception("No active email provider configured")
            
            # Send email (simplified - would use actual provider API)
            send_mail(
                subject=notification.subject,
                message=notification.content,
                from_email=f"{provider.from_name} <{provider.from_email}>",
                recipient_list=[notification.recipient_email],
                fail_silently=False
            )
            
            notification.status = Notification.Status.SENT
            notification.sent_at = timezone.now()
            notification.save()
            
            # Update provider stats
            provider.emails_sent += 1
            provider.save()
            
        except Exception as e:
            notification.status = Notification.Status.FAILED
            notification.error_message = str(e)
            notification.save()
            raise


class SMSService:
    """
    Service for sending SMS.
    """
    
    @staticmethod
    def send_sms(notification):
        """Send SMS notification."""
        try:
            # Get SMS provider
            provider = SMSProvider.objects.filter(
                restaurant=notification.restaurant,
                is_active=True,
                is_default=True
            ).first()
            
            if not provider:
                raise Exception("No active SMS provider configured")
            
            # Send SMS (simplified - would use actual provider API like Twilio)
            # This is a placeholder implementation
            logger.info(f"Sending SMS to {notification.recipient_phone}: {notification.content}")
            
            notification.status = Notification.Status.SENT
            notification.sent_at = timezone.now()
            notification.save()
            
            # Update provider stats
            provider.sms_sent += 1
            provider.save()
            
        except Exception as e:
            notification.status = Notification.Status.FAILED
            notification.error_message = str(e)
            notification.save()
            raise


class PushService:
    """
    Service for sending push notifications.
    """
    
    @staticmethod
    def send_push(notification):
        """Send push notification."""
        try:
            # Send push notification (would integrate with FCM, APNs, etc.)
            logger.info(f"Sending push notification: {notification.content}")
            
            notification.status = Notification.Status.SENT
            notification.sent_at = timezone.now()
            notification.save()
            
        except Exception as e:
            notification.status = Notification.Status.FAILED
            notification.error_message = str(e)
            notification.save()
            raise


class WebhookService:
    """
    Service for sending webhooks.
    """
    
    @staticmethod
    def send_webhook(event_type, payload, restaurant):
        """Send webhook for an event."""
        endpoints = WebhookEndpoint.objects.filter(
            restaurant=restaurant,
            is_active=True,
            event_types__contains=[event_type]
        )
        
        for endpoint in endpoints:
            WebhookService._send_to_endpoint(endpoint, event_type, payload)
    
    @staticmethod
    def _send_to_endpoint(endpoint, event_type, payload):
        """Send webhook to specific endpoint."""
        delivery = WebhookDelivery.objects.create(
            restaurant=endpoint.restaurant,
            endpoint=endpoint,
            event_type=event_type,
            payload=payload
        )
        
        try:
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'RestaurantPOS-Webhook/1.0',
                **endpoint.headers
            }
            
            if endpoint.secret_key:
                # Add signature header (simplified)
                headers['X-Webhook-Signature'] = endpoint.secret_key
            
            response = requests.post(
                endpoint.url,
                json=payload,
                headers=headers,
                timeout=endpoint.timeout_seconds
            )
            
            delivery.status = WebhookDelivery.Status.SUCCESS if response.status_code < 400 else WebhookDelivery.Status.FAILED
            delivery.response_status_code = response.status_code
            delivery.response_body = response.text[:1000]  # Limit response body size
            delivery.response_headers = dict(response.headers)
            delivery.sent_at = timezone.now()
            delivery.save()
            
            # Update endpoint stats
            endpoint.total_sent += 1
            endpoint.last_sent_at = timezone.now()
            if delivery.status == WebhookDelivery.Status.FAILED:
                endpoint.total_failed += 1
            endpoint.save()
            
        except Exception as e:
            delivery.status = WebhookDelivery.Status.FAILED
            delivery.error_message = str(e)
            delivery.save()
            
            endpoint.total_failed += 1
            endpoint.save()


# Celery tasks
@shared_task
def send_notification_task(notification_id):
    """Celery task to send delayed notifications."""
    try:
        notification = Notification.objects.get(id=notification_id)
        NotificationService._send_notification_now(notification)
    except Notification.DoesNotExist:
        logger.error(f"Notification {notification_id} not found")


@shared_task
def send_webhook_task(event_type, payload, restaurant_id):
    """Celery task to send webhooks."""
    try:
        from apps.restaurants.models import Restaurant
        restaurant = Restaurant.objects.get(id=restaurant_id)
        WebhookService.send_webhook(event_type, payload, restaurant)
    except Restaurant.DoesNotExist:
        logger.error(f"Restaurant {restaurant_id} not found")


@shared_task
def retry_failed_notifications():
    """Celery task to retry failed notifications."""
    failed_notifications = Notification.objects.filter(
        status=Notification.Status.FAILED,
        retry_count__lt=F('max_retries')
    )
    
    for notification in failed_notifications:
        notification.retry_count += 1
        notification.save()
        
        try:
            NotificationService._send_notification_now(notification)
        except Exception as e:
            logger.error(f"Retry failed for notification {notification.id}: {str(e)}")


@shared_task
def cleanup_old_notifications():
    """Celery task to cleanup old notifications."""
    from datetime import timedelta
    
    # Delete notifications older than 90 days
    cutoff_date = timezone.now() - timedelta(days=90)
    
    deleted_count = Notification.objects.filter(
        created_at__lt=cutoff_date
    ).delete()[0]
    
    logger.info(f"Cleaned up {deleted_count} old notifications")

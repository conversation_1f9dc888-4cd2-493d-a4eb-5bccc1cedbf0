# Generated by Django 5.2.6 on 2025-09-11 07:54

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="EmailProvider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "provider_type",
                    models.CharField(
                        choices=[
                            ("smtp", "SMTP"),
                            ("sendgrid", "SendGrid"),
                            ("mailgun", "Mailgun"),
                            ("ses", "Amazon SES"),
                            ("postmark", "Postmark"),
                        ],
                        default="smtp",
                        max_length=20,
                    ),
                ),
                (
                    "config",
                    models.JSONField(
                        default=dict, help_text="Provider-specific configuration"
                    ),
                ),
                ("from_email", models.EmailField(max_length=254)),
                ("from_name", models.CharField(max_length=200)),
                ("reply_to_email", models.EmailField(blank=True, max_length=254)),
                ("is_active", models.BooleanField(default=True)),
                ("is_default", models.BooleanField(default=False)),
                ("emails_sent", models.PositiveIntegerField(default=0)),
                ("emails_delivered", models.PositiveIntegerField(default=0)),
                ("emails_bounced", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "Email Provider",
                "verbose_name_plural": "Email Providers",
                "db_table": "email_providers",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Integration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "integration_type",
                    models.CharField(
                        choices=[
                            ("delivery", "Delivery Platform"),
                            ("payment", "Payment Gateway"),
                            ("accounting", "Accounting Software"),
                            ("inventory", "Inventory Management"),
                            ("marketing", "Marketing Platform"),
                            ("analytics", "Analytics Platform"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("error", "Error"),
                            ("pending", "Pending Setup"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "config",
                    models.JSONField(
                        default=dict,
                        help_text="Integration-specific configuration and credentials",
                    ),
                ),
                ("auto_sync", models.BooleanField(default=True)),
                ("sync_frequency_minutes", models.PositiveIntegerField(default=60)),
                ("last_sync_at", models.DateTimeField(blank=True, null=True)),
                ("next_sync_at", models.DateTimeField(blank=True, null=True)),
                ("error_count", models.PositiveIntegerField(default=0)),
                ("last_error", models.TextField(blank=True)),
                ("last_error_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Integration",
                "verbose_name_plural": "Integrations",
                "db_table": "integrations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("recipient_email", models.EmailField(blank=True, max_length=254)),
                ("recipient_phone", models.CharField(blank=True, max_length=20)),
                ("subject", models.CharField(blank=True, max_length=200)),
                ("content", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("read", "Read"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("delivered_at", models.DateTimeField(blank=True, null=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True)),
                ("retry_count", models.PositiveIntegerField(default=0)),
                ("max_retries", models.PositiveIntegerField(default=3)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "verbose_name": "Notification",
                "verbose_name_plural": "Notifications",
                "db_table": "notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("order_confirmed", "Order Confirmed"),
                            ("order_ready", "Order Ready"),
                            ("order_delivered", "Order Delivered"),
                            ("payment_received", "Payment Received"),
                            ("low_stock", "Low Stock Alert"),
                            ("staff_shift", "Staff Shift Reminder"),
                            ("customer_birthday", "Customer Birthday"),
                            ("marketing_promo", "Marketing Promotion"),
                            ("system_alert", "System Alert"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("push", "Push Notification"),
                            ("in_app", "In-App Notification"),
                            ("webhook", "Webhook"),
                        ],
                        max_length=20,
                    ),
                ),
                ("subject", models.CharField(blank=True, max_length=200)),
                ("email_template", models.TextField(blank=True)),
                ("sms_template", models.TextField(blank=True)),
                ("push_template", models.TextField(blank=True)),
                (
                    "template_variables",
                    models.JSONField(
                        default=dict,
                        help_text="Available variables for template substitution",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("send_immediately", models.BooleanField(default=True)),
                (
                    "delay_minutes",
                    models.PositiveIntegerField(
                        default=0, help_text="Delay in minutes before sending"
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Template",
                "verbose_name_plural": "Notification Templates",
                "db_table": "notification_templates",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="SMSProvider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "provider_type",
                    models.CharField(
                        choices=[
                            ("twilio", "Twilio"),
                            ("nexmo", "Vonage (Nexmo)"),
                            ("aws_sns", "AWS SNS"),
                            ("clicksend", "ClickSend"),
                        ],
                        default="twilio",
                        max_length=20,
                    ),
                ),
                (
                    "config",
                    models.JSONField(
                        default=dict, help_text="Provider-specific configuration"
                    ),
                ),
                ("from_number", models.CharField(max_length=20)),
                ("is_active", models.BooleanField(default=True)),
                ("is_default", models.BooleanField(default=False)),
                ("sms_sent", models.PositiveIntegerField(default=0)),
                ("sms_delivered", models.PositiveIntegerField(default=0)),
                ("sms_failed", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "SMS Provider",
                "verbose_name_plural": "SMS Providers",
                "db_table": "sms_providers",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="WebhookDelivery",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("event_type", models.CharField(max_length=50)),
                ("payload", models.JSONField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("success", "Success"),
                            ("failed", "Failed"),
                            ("retrying", "Retrying"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "response_status_code",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("response_body", models.TextField(blank=True)),
                ("response_headers", models.JSONField(blank=True, default=dict)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                (
                    "response_time_ms",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("error_message", models.TextField(blank=True)),
                ("retry_count", models.PositiveIntegerField(default=0)),
                ("next_retry_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Webhook Delivery",
                "verbose_name_plural": "Webhook Deliveries",
                "db_table": "webhook_deliveries",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WebhookEndpoint",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                ("url", models.URLField()),
                (
                    "event_types",
                    models.JSONField(
                        default=list,
                        help_text="List of event types to send to this endpoint",
                    ),
                ),
                ("secret_key", models.CharField(blank=True, max_length=200)),
                (
                    "headers",
                    models.JSONField(
                        default=dict,
                        help_text="Additional headers to send with webhook",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("timeout_seconds", models.PositiveIntegerField(default=30)),
                ("max_retries", models.PositiveIntegerField(default=3)),
                ("total_sent", models.PositiveIntegerField(default=0)),
                ("total_failed", models.PositiveIntegerField(default=0)),
                ("last_sent_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Webhook Endpoint",
                "verbose_name_plural": "Webhook Endpoints",
                "db_table": "webhook_endpoints",
                "ordering": ["name"],
            },
        ),
    ]

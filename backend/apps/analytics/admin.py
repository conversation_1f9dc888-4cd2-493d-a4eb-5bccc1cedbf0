"""
Analytics admin configuration.
"""

from django.contrib import admin
from .models import (
    SalesReport, InventoryReport, StaffPerformanceReport,
    CustomerAnalytics, MenuAnalytics, ReportSchedule, DashboardWidget
)


@admin.register(SalesReport)
class SalesReportAdmin(admin.ModelAdmin):
    list_display = [
        'report_type', 'restaurant', 'start_date', 'end_date', 'status',
        'total_orders', 'total_revenue', 'generated_by', 'created_at'
    ]
    list_filter = ['report_type', 'status', 'restaurant', 'created_at']
    search_fields = ['restaurant__name']
    ordering = ['-created_at']
    readonly_fields = [
        'report_data', 'total_orders', 'total_revenue', 'total_items_sold',
        'average_order_value', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Report Information', {
            'fields': ('report_type', 'status', 'start_date', 'end_date', 'generated_by')
        }),
        ('Metrics', {
            'fields': ('total_orders', 'total_revenue', 'total_items_sold', 'average_order_value')
        }),
        ('Report Data', {
            'fields': ('report_data',),
            'classes': ('collapse',)
        }),
        ('Files', {
            'fields': ('pdf_file', 'excel_file'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(InventoryReport)
class InventoryReportAdmin(admin.ModelAdmin):
    list_display = [
        'report_type', 'restaurant', 'start_date', 'end_date',
        'total_items', 'total_value', 'low_stock_items', 'generated_by', 'created_at'
    ]
    list_filter = ['report_type', 'restaurant', 'created_at']
    search_fields = ['restaurant__name']
    ordering = ['-created_at']
    readonly_fields = [
        'report_data', 'total_items', 'total_value',
        'low_stock_items', 'out_of_stock_items', 'created_at', 'updated_at'
    ]


@admin.register(StaffPerformanceReport)
class StaffPerformanceReportAdmin(admin.ModelAdmin):
    list_display = [
        'staff_member', 'restaurant', 'start_date', 'end_date',
        'total_orders_served', 'total_revenue_generated', 'generated_by', 'created_at'
    ]
    list_filter = ['restaurant', 'created_at']
    search_fields = ['staff_member__first_name', 'staff_member__last_name', 'restaurant__name']
    ordering = ['-created_at']
    readonly_fields = [
        'total_orders_served', 'total_revenue_generated', 'average_order_value',
        'total_hours_worked', 'performance_data', 'created_at', 'updated_at'
    ]


@admin.register(CustomerAnalytics)
class CustomerAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'restaurant', 'start_date', 'end_date', 'total_customers',
        'new_customers', 'returning_customers', 'generated_by', 'created_at'
    ]
    list_filter = ['restaurant', 'created_at']
    search_fields = ['restaurant__name']
    ordering = ['-created_at']
    readonly_fields = [
        'total_customers', 'new_customers', 'returning_customers',
        'average_customer_value', 'analytics_data', 'created_at', 'updated_at'
    ]


@admin.register(MenuAnalytics)
class MenuAnalyticsAdmin(admin.ModelAdmin):
    list_display = [
        'restaurant', 'start_date', 'end_date', 'total_items_sold',
        'total_revenue', 'most_popular_item', 'generated_by', 'created_at'
    ]
    list_filter = ['restaurant', 'created_at']
    search_fields = ['restaurant__name', 'most_popular_item', 'least_popular_item']
    ordering = ['-created_at']
    readonly_fields = [
        'analytics_data', 'total_items_sold', 'total_revenue',
        'most_popular_item', 'least_popular_item', 'created_at', 'updated_at'
    ]


@admin.register(ReportSchedule)
class ReportScheduleAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'restaurant', 'report_type', 'frequency', 'is_active',
        'last_run', 'next_run', 'created_by', 'created_at'
    ]
    list_filter = ['report_type', 'frequency', 'is_active', 'restaurant', 'created_at']
    search_fields = ['name', 'restaurant__name']
    ordering = ['name']
    readonly_fields = ['last_run', 'next_run', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Schedule Information', {
            'fields': ('name', 'report_type', 'frequency', 'is_active', 'created_by')
        }),
        ('Recipients', {
            'fields': ('email_recipients',)
        }),
        ('Configuration', {
            'fields': ('schedule_config',),
            'classes': ('collapse',)
        }),
        ('Execution', {
            'fields': ('last_run', 'next_run')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(DashboardWidget)
class DashboardWidgetAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'restaurant', 'widget_type', 'position_x', 'position_y',
        'width', 'height', 'is_active', 'created_by', 'created_at'
    ]
    list_filter = ['widget_type', 'is_active', 'restaurant', 'created_at']
    search_fields = ['name', 'restaurant__name', 'created_by__first_name', 'created_by__last_name']
    ordering = ['restaurant', 'position_y', 'position_x']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Widget Information', {
            'fields': ('name', 'widget_type', 'is_active', 'created_by')
        }),
        ('Position & Size', {
            'fields': ('position_x', 'position_y', 'width', 'height')
        }),
        ('Configuration', {
            'fields': ('config',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

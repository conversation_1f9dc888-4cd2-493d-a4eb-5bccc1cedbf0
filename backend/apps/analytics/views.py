"""
Analytics views for Restaurant POS system.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, Avg, F
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta
import pymongo
from django.conf import settings

from .models import SalesReport, InventoryReport, StaffPerformanceReport, CustomerAnalytics, MenuAnalytics, ReportSchedule, DashboardWidget
from .serializers import (
    SalesReportSerializer,
    InventoryReportSerializer,
    StaffPerformanceReportSerializer,
    CustomerAnalyticsSerializer,
    MenuAnalyticsSerializer,
    ReportScheduleSerializer,
    DashboardWidgetSerializer,
    DashboardDataSerializer,
    SalesAnalyticsSerializer,
    InventoryAnalyticsSerializer,
    MenuPerformanceSerializer,
)
from apps.core.permissions import IsRestaurantStaff
from apps.core.middleware import get_current_restaurant
from apps.orders.models import Order, OrderItem
from apps.inventory.models import InventoryItem, StockAdjustment
from apps.billing.models import Payment


class SalesReportViewSet(viewsets.ModelViewSet):
    """
    Sales report management viewset.
    """

    serializer_class = SalesReportSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    ordering_fields = ["created_at", "start_date", "end_date"]
    ordering = ["-created_at"]
    filterset_fields = ["report_type", "status"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return SalesReport.objects.filter(restaurant=restaurant)
        return SalesReport.objects.none()

    def create(self, request, *args, **kwargs):
        """Create and generate sales report."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        report = serializer.save()

        # Generate report data
        self._generate_sales_report_data(report)

        response_serializer = SalesReportSerializer(report)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def _generate_sales_report_data(self, report):
        """Generate sales report data."""
        restaurant = report.restaurant

        # Get orders in date range
        orders = Order.objects.filter(restaurant=restaurant, order_time__date__range=[report.start_date, report.end_date], status=Order.Status.COMPLETED)

        # Calculate metrics
        report.total_orders = orders.count()
        report.total_revenue = orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
        report.total_items_sold = OrderItem.objects.filter(order__in=orders).aggregate(Sum("quantity"))["quantity__sum"] or 0

        if report.total_orders > 0:
            report.average_order_value = report.total_revenue / report.total_orders

        # Generate detailed report data
        report.report_data = {
            "daily_breakdown": self._get_daily_sales_breakdown(orders),
            "payment_methods": self._get_payment_methods_breakdown(orders),
            "order_types": self._get_order_types_breakdown(orders),
            "hourly_breakdown": self._get_hourly_sales_breakdown(orders),
            "top_selling_items": self._get_top_selling_items(orders),
            "staff_performance": self._get_staff_performance(orders),
        }

        report.status = SalesReport.Status.COMPLETED
        report.save()

    def _get_daily_sales_breakdown(self, orders):
        """Get daily sales breakdown."""
        daily_data = []
        current_date = orders.first().order_time.date() if orders.exists() else timezone.now().date()
        end_date = orders.last().order_time.date() if orders.exists() else timezone.now().date()

        while current_date <= end_date:
            daily_orders = orders.filter(order_time__date=current_date)
            daily_data.append(
                {
                    "date": current_date.isoformat(),
                    "orders": daily_orders.count(),
                    "revenue": float(daily_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0),
                    "items_sold": OrderItem.objects.filter(order__in=daily_orders).aggregate(Sum("quantity"))["quantity__sum"] or 0,
                }
            )
            current_date += timedelta(days=1)

        return daily_data

    def _get_payment_methods_breakdown(self, orders):
        """Get payment methods breakdown."""
        payments = Payment.objects.filter(order__in=orders, status=Payment.Status.COMPLETED).values("payment_method__name").annotate(total_amount=Sum("amount"), count=Count("id"))

        return [{"method": payment["payment_method__name"], "amount": float(payment["total_amount"]), "count": payment["count"]} for payment in payments]

    def _get_order_types_breakdown(self, orders):
        """Get order types breakdown."""
        order_types = orders.values("order_type").annotate(total_amount=Sum("total_amount"), count=Count("id"))

        return [{"type": order_type["order_type"], "amount": float(order_type["total_amount"]), "count": order_type["count"]} for order_type in order_types]

    def _get_hourly_sales_breakdown(self, orders):
        """Get hourly sales breakdown."""
        hourly_data = []
        for hour in range(24):
            hour_orders = orders.filter(order_time__hour=hour)
            hourly_data.append({"hour": hour, "orders": hour_orders.count(), "revenue": float(hour_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0)})

        return hourly_data

    def _get_top_selling_items(self, orders):
        """Get top selling items."""
        items = (
            OrderItem.objects.filter(order__in=orders)
            .values("menu_item__name")
            .annotate(total_quantity=Sum("quantity"), total_revenue=Sum("total_price"))
            .order_by("-total_quantity")[:10]
        )

        return [{"item": item["menu_item__name"], "quantity": item["total_quantity"], "revenue": float(item["total_revenue"])} for item in items]

    def _get_staff_performance(self, orders):
        """Get staff performance data."""
        staff_data = (
            orders.filter(waiter__isnull=False)
            .values("waiter__first_name", "waiter__last_name")
            .annotate(total_orders=Count("id"), total_revenue=Sum("total_amount"))
            .order_by("-total_revenue")
        )

        return [
            {"staff_name": f"{staff['waiter__first_name']} {staff['waiter__last_name']}", "orders": staff["total_orders"], "revenue": float(staff["total_revenue"])}
            for staff in staff_data
        ]


class InventoryReportViewSet(viewsets.ModelViewSet):
    """
    Inventory report management viewset.
    """

    serializer_class = InventoryReportSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.OrderingFilter, DjangoFilterBackend]
    ordering_fields = ["created_at", "start_date", "end_date"]
    ordering = ["-created_at"]
    filterset_fields = ["report_type"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return InventoryReport.objects.filter(restaurant=restaurant)
        return InventoryReport.objects.none()


class DashboardWidgetViewSet(viewsets.ModelViewSet):
    """
    Dashboard widget management viewset.
    """

    serializer_class = DashboardWidgetSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.OrderingFilter]
    ordering = ["position_y", "position_x"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return DashboardWidget.objects.filter(restaurant=restaurant, created_by=self.request.user)
        return DashboardWidget.objects.none()


@action(detail=False, methods=["get"])
def dashboard_data(request):
    """
    Get dashboard data with real-time metrics.
    """
    restaurant = get_current_restaurant()
    if not restaurant:
        return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

    today = timezone.now().date()
    yesterday = today - timedelta(days=1)

    # Today's metrics
    today_orders = Order.objects.filter(restaurant=restaurant, order_time__date=today, status=Order.Status.COMPLETED)

    yesterday_orders = Order.objects.filter(restaurant=restaurant, order_time__date=yesterday, status=Order.Status.COMPLETED)

    # Calculate metrics
    total_revenue_today = today_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
    total_orders_today = today_orders.count()
    average_order_value = total_revenue_today / total_orders_today if total_orders_today > 0 else 0

    # Growth calculation
    yesterday_revenue = yesterday_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
    revenue_growth = ((total_revenue_today - yesterday_revenue) / yesterday_revenue * 100) if yesterday_revenue > 0 else 0

    # Sales chart data (last 7 days)
    sales_chart_data = []
    for i in range(7):
        date = today - timedelta(days=i)
        day_orders = Order.objects.filter(restaurant=restaurant, order_time__date=date, status=Order.Status.COMPLETED)
        sales_chart_data.append({"date": date.isoformat(), "revenue": float(day_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0), "orders": day_orders.count()})

    # Top selling items today
    top_selling_items = (
        OrderItem.objects.filter(order__restaurant=restaurant, order__order_time__date=today, order__status=Order.Status.COMPLETED)
        .values("menu_item__name")
        .annotate(total_quantity=Sum("quantity"))
        .order_by("-total_quantity")[:5]
    )

    # Order types breakdown
    order_types_breakdown = today_orders.values("order_type").annotate(count=Count("id"))
    order_types_dict = {ot["order_type"]: ot["count"] for ot in order_types_breakdown}

    # Hourly sales today
    hourly_sales = []
    for hour in range(24):
        hour_orders = today_orders.filter(order_time__hour=hour)
        hourly_sales.append({"hour": hour, "revenue": float(hour_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0), "orders": hour_orders.count()})

    # Low stock alerts
    low_stock_items = InventoryItem.objects.filter(restaurant=restaurant, current_stock__lte=F("reorder_point"), is_active=True)[:5]

    low_stock_alerts = [{"item": item.name, "current_stock": float(item.current_stock), "reorder_point": float(item.reorder_point)} for item in low_stock_items]

    # Pending orders
    pending_orders = Order.objects.filter(restaurant=restaurant, status__in=[Order.Status.PENDING, Order.Status.CONFIRMED, Order.Status.PREPARING]).count()

    # Staff on duty (simplified - would need shift management)
    staff_on_duty = 5  # Placeholder

    dashboard_data = {
        "total_revenue_today": total_revenue_today,
        "total_orders_today": total_orders_today,
        "average_order_value": average_order_value,
        "revenue_growth": revenue_growth,
        "sales_chart_data": sales_chart_data,
        "top_selling_items": [{"item": item["menu_item__name"], "quantity": item["total_quantity"]} for item in top_selling_items],
        "order_types_breakdown": order_types_dict,
        "hourly_sales": hourly_sales,
        "low_stock_alerts": low_stock_alerts,
        "pending_orders": pending_orders,
        "staff_on_duty": staff_on_duty,
    }

    serializer = DashboardDataSerializer(dashboard_data)
    return Response(serializer.data)


@action(detail=False, methods=["get"])
def sales_analytics(request):
    """
    Get comprehensive sales analytics.
    """
    restaurant = get_current_restaurant()
    if not restaurant:
        return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

    # Date range parameters
    period = request.query_params.get("period", "month")  # day, week, month, year
    date_from = request.query_params.get("date_from")
    date_to = request.query_params.get("date_to")

    # Set default date range based on period
    today = timezone.now().date()
    if period == "day":
        start_date = today
        end_date = today
    elif period == "week":
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == "month":
        start_date = today.replace(day=1)
        end_date = today
    elif period == "year":
        start_date = today.replace(month=1, day=1)
        end_date = today
    else:
        start_date = datetime.strptime(date_from, "%Y-%m-%d").date() if date_from else today
        end_date = datetime.strptime(date_to, "%Y-%m-%d").date() if date_to else today

    # Get orders in range
    orders = Order.objects.filter(restaurant=restaurant, order_time__date__range=[start_date, end_date], status=Order.Status.COMPLETED)

    # Calculate metrics
    total_revenue = orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
    total_orders = orders.count()
    average_order_value = total_revenue / total_orders if total_orders > 0 else 0

    # Previous period for comparison
    period_length = (end_date - start_date).days + 1
    prev_start = start_date - timedelta(days=period_length)
    prev_end = start_date - timedelta(days=1)

    prev_orders = Order.objects.filter(restaurant=restaurant, order_time__date__range=[prev_start, prev_end], status=Order.Status.COMPLETED)

    previous_period_revenue = prev_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
    previous_period_orders = prev_orders.count()

    growth_rate = ((total_revenue - previous_period_revenue) / previous_period_revenue * 100) if previous_period_revenue > 0 else 0

    # Daily sales breakdown
    daily_sales = []
    current_date = start_date
    while current_date <= end_date:
        day_orders = orders.filter(order_time__date=current_date)
        daily_sales.append({"date": current_date.isoformat(), "revenue": float(day_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0), "orders": day_orders.count()})
        current_date += timedelta(days=1)

    # Payment methods breakdown
    payments = Payment.objects.filter(order__in=orders, status=Payment.Status.COMPLETED).values("payment_method__name").annotate(total_amount=Sum("amount"))
    payment_methods = {p["payment_method__name"]: float(p["total_amount"]) for p in payments}

    # Order types breakdown
    order_types_data = orders.values("order_type").annotate(total_amount=Sum("total_amount"), count=Count("id"))
    order_types = {ot["order_type"]: {"amount": float(ot["total_amount"]), "count": ot["count"]} for ot in order_types_data}

    # Peak hours analysis
    peak_hours = []
    for hour in range(24):
        hour_orders = orders.filter(order_time__hour=hour)
        hour_revenue = hour_orders.aggregate(Sum("total_amount"))["total_amount__sum"] or 0
        if hour_revenue > 0:
            peak_hours.append({"hour": hour, "revenue": float(hour_revenue), "orders": hour_orders.count()})

    peak_hours = sorted(peak_hours, key=lambda x: x["revenue"], reverse=True)[:5]

    analytics_data = {
        "period": period,
        "total_revenue": total_revenue,
        "total_orders": total_orders,
        "average_order_value": average_order_value,
        "growth_rate": growth_rate,
        "daily_sales": daily_sales,
        "payment_methods": payment_methods,
        "order_types": order_types,
        "peak_hours": peak_hours,
        "previous_period_revenue": previous_period_revenue,
        "previous_period_orders": previous_period_orders,
    }

    serializer = SalesAnalyticsSerializer(analytics_data)
    return Response(serializer.data)

"""
Analytics models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.fields import J<PERSON>NField
from decimal import Decimal
from apps.core.models import BaseModel, TenantAwareModel
from apps.restaurants.models import Restaurant
from apps.users.models import User


class SalesReport(TenantAwareModel):
    """
    Sales report model for storing generated reports.
    """
    
    class ReportType(models.TextChoices):
        DAILY = 'daily', _('Daily Report')
        WEEKLY = 'weekly', _('Weekly Report')
        MONTHLY = 'monthly', _('Monthly Report')
        YEARLY = 'yearly', _('Yearly Report')
        CUSTOM = 'custom', _('Custom Date Range')

    class Status(models.TextChoices):
        GENERATING = 'generating', _('Generating')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')

    report_type = models.CharField(
        max_length=20,
        choices=ReportType.choices,
        default=ReportType.DAILY
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.GENERATING
    )
    
    # Date range
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Report data (stored as JSON for flexibility)
    report_data = models.JSONField(default=dict)
    
    # Summary metrics
    total_orders = models.PositiveIntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_items_sold = models.PositiveIntegerField(default=0)
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Staff who generated the report
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_reports'
    )
    
    # File exports
    pdf_file = models.FileField(upload_to='reports/pdf/', blank=True)
    excel_file = models.FileField(upload_to='reports/excel/', blank=True)
    
    class Meta:
        db_table = 'sales_reports'
        verbose_name = _('Sales Report')
        verbose_name_plural = _('Sales Reports')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_report_type_display()} Report - {self.start_date} to {self.end_date}"


class InventoryReport(TenantAwareModel):
    """
    Inventory analytics and reports.
    """
    
    class ReportType(models.TextChoices):
        STOCK_LEVELS = 'stock_levels', _('Stock Levels')
        LOW_STOCK = 'low_stock', _('Low Stock Items')
        STOCK_MOVEMENT = 'stock_movement', _('Stock Movement')
        SUPPLIER_PERFORMANCE = 'supplier_performance', _('Supplier Performance')
        WASTE_ANALYSIS = 'waste_analysis', _('Waste Analysis')

    report_type = models.CharField(
        max_length=30,
        choices=ReportType.choices,
        default=ReportType.STOCK_LEVELS
    )
    
    # Date range
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Report data
    report_data = models.JSONField(default=dict)
    
    # Summary metrics
    total_items = models.PositiveIntegerField(default=0)
    total_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    low_stock_items = models.PositiveIntegerField(default=0)
    out_of_stock_items = models.PositiveIntegerField(default=0)
    
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_inventory_reports'
    )
    
    class Meta:
        db_table = 'inventory_reports'
        verbose_name = _('Inventory Report')
        verbose_name_plural = _('Inventory Reports')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_report_type_display()} - {self.start_date} to {self.end_date}"


class StaffPerformanceReport(TenantAwareModel):
    """
    Staff performance analytics.
    """
    
    # Date range
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Staff member
    staff_member = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='performance_reports'
    )
    
    # Performance metrics
    total_orders_served = models.PositiveIntegerField(default=0)
    total_revenue_generated = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_hours_worked = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Detailed performance data
    performance_data = models.JSONField(default=dict)
    
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_staff_reports'
    )
    
    class Meta:
        db_table = 'staff_performance_reports'
        verbose_name = _('Staff Performance Report')
        verbose_name_plural = _('Staff Performance Reports')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.staff_member.get_full_name()} - {self.start_date} to {self.end_date}"


class CustomerAnalytics(TenantAwareModel):
    """
    Customer behavior analytics.
    """
    
    # Date range
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Customer metrics
    total_customers = models.PositiveIntegerField(default=0)
    new_customers = models.PositiveIntegerField(default=0)
    returning_customers = models.PositiveIntegerField(default=0)
    average_customer_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Customer behavior data
    analytics_data = models.JSONField(default=dict)
    
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_customer_analytics'
    )
    
    class Meta:
        db_table = 'customer_analytics'
        verbose_name = _('Customer Analytics')
        verbose_name_plural = _('Customer Analytics')
        ordering = ['-created_at']

    def __str__(self):
        return f"Customer Analytics - {self.start_date} to {self.end_date}"


class MenuAnalytics(TenantAwareModel):
    """
    Menu item performance analytics.
    """
    
    # Date range
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Menu performance data
    analytics_data = models.JSONField(default=dict)
    
    # Summary metrics
    total_items_sold = models.PositiveIntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    most_popular_item = models.CharField(max_length=200, blank=True)
    least_popular_item = models.CharField(max_length=200, blank=True)
    
    generated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='generated_menu_analytics'
    )
    
    class Meta:
        db_table = 'menu_analytics'
        verbose_name = _('Menu Analytics')
        verbose_name_plural = _('Menu Analytics')
        ordering = ['-created_at']

    def __str__(self):
        return f"Menu Analytics - {self.start_date} to {self.end_date}"


class ReportSchedule(TenantAwareModel):
    """
    Scheduled report generation.
    """
    
    class Frequency(models.TextChoices):
        DAILY = 'daily', _('Daily')
        WEEKLY = 'weekly', _('Weekly')
        MONTHLY = 'monthly', _('Monthly')
        QUARTERLY = 'quarterly', _('Quarterly')
        YEARLY = 'yearly', _('Yearly')

    class ReportType(models.TextChoices):
        SALES = 'sales', _('Sales Report')
        INVENTORY = 'inventory', _('Inventory Report')
        STAFF = 'staff', _('Staff Performance')
        CUSTOMER = 'customer', _('Customer Analytics')
        MENU = 'menu', _('Menu Analytics')

    name = models.CharField(max_length=200)
    report_type = models.CharField(
        max_length=20,
        choices=ReportType.choices,
        default=ReportType.SALES
    )
    frequency = models.CharField(
        max_length=20,
        choices=Frequency.choices,
        default=Frequency.DAILY
    )
    
    is_active = models.BooleanField(default=True)
    
    # Recipients
    email_recipients = models.JSONField(default=list)
    
    # Schedule settings
    schedule_config = models.JSONField(default=dict)
    
    # Last run
    last_run = models.DateTimeField(null=True, blank=True)
    next_run = models.DateTimeField(null=True, blank=True)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_report_schedules'
    )
    
    class Meta:
        db_table = 'report_schedules'
        verbose_name = _('Report Schedule')
        verbose_name_plural = _('Report Schedules')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"


class DashboardWidget(TenantAwareModel):
    """
    Dashboard widget configuration.
    """
    
    class WidgetType(models.TextChoices):
        SALES_CHART = 'sales_chart', _('Sales Chart')
        REVENUE_METRIC = 'revenue_metric', _('Revenue Metric')
        ORDER_COUNT = 'order_count', _('Order Count')
        TOP_ITEMS = 'top_items', _('Top Selling Items')
        INVENTORY_ALERTS = 'inventory_alerts', _('Inventory Alerts')
        STAFF_PERFORMANCE = 'staff_performance', _('Staff Performance')
        CUSTOMER_METRICS = 'customer_metrics', _('Customer Metrics')

    name = models.CharField(max_length=200)
    widget_type = models.CharField(
        max_length=30,
        choices=WidgetType.choices,
        default=WidgetType.SALES_CHART
    )
    
    # Widget configuration
    config = models.JSONField(default=dict)
    
    # Position and size
    position_x = models.PositiveIntegerField(default=0)
    position_y = models.PositiveIntegerField(default=0)
    width = models.PositiveIntegerField(default=4)
    height = models.PositiveIntegerField(default=3)
    
    is_active = models.BooleanField(default=True)
    
    # User who created the widget
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='dashboard_widgets'
    )
    
    class Meta:
        db_table = 'dashboard_widgets'
        verbose_name = _('Dashboard Widget')
        verbose_name_plural = _('Dashboard Widgets')
        ordering = ['position_y', 'position_x']

    def __str__(self):
        return f"{self.name} - {self.get_widget_type_display()}"

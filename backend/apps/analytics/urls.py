"""
Analytics URLs for Restaurant POS system.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SalesReportViewSet, InventoryReportViewSet, DashboardWidgetViewSet,
    dashboard_data, sales_analytics
)

router = DefaultRouter()
router.register(r'sales-reports', SalesReportViewSet, basename='sales-reports')
router.register(r'inventory-reports', InventoryReportViewSet, basename='inventory-reports')
router.register(r'dashboard-widgets', DashboardWidgetViewSet, basename='dashboard-widgets')

app_name = 'analytics'
urlpatterns = [
    path('', include(router.urls)),
    path('dashboard/', dashboard_data, name='dashboard-data'),
    path('sales-analytics/', sales_analytics, name='sales-analytics'),
]

# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("restaurants", "__first__"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomerAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("total_customers", models.PositiveIntegerField(default=0)),
                ("new_customers", models.PositiveIntegerField(default=0)),
                ("returning_customers", models.PositiveIntegerField(default=0)),
                (
                    "average_customer_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("analytics_data", models.JSONField(default=dict)),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_customer_analytics",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer Analytics",
                "verbose_name_plural": "Customer Analytics",
                "db_table": "customer_analytics",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DashboardWidget",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "widget_type",
                    models.CharField(
                        choices=[
                            ("sales_chart", "Sales Chart"),
                            ("revenue_metric", "Revenue Metric"),
                            ("order_count", "Order Count"),
                            ("top_items", "Top Selling Items"),
                            ("inventory_alerts", "Inventory Alerts"),
                            ("staff_performance", "Staff Performance"),
                            ("customer_metrics", "Customer Metrics"),
                        ],
                        default="sales_chart",
                        max_length=30,
                    ),
                ),
                ("config", models.JSONField(default=dict)),
                ("position_x", models.PositiveIntegerField(default=0)),
                ("position_y", models.PositiveIntegerField(default=0)),
                ("width", models.PositiveIntegerField(default=4)),
                ("height", models.PositiveIntegerField(default=3)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dashboard_widgets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Dashboard Widget",
                "verbose_name_plural": "Dashboard Widgets",
                "db_table": "dashboard_widgets",
                "ordering": ["position_y", "position_x"],
            },
        ),
        migrations.CreateModel(
            name="InventoryReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("stock_levels", "Stock Levels"),
                            ("low_stock", "Low Stock Items"),
                            ("stock_movement", "Stock Movement"),
                            ("supplier_performance", "Supplier Performance"),
                            ("waste_analysis", "Waste Analysis"),
                        ],
                        default="stock_levels",
                        max_length=30,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("report_data", models.JSONField(default=dict)),
                ("total_items", models.PositiveIntegerField(default=0)),
                (
                    "total_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("low_stock_items", models.PositiveIntegerField(default=0)),
                ("out_of_stock_items", models.PositiveIntegerField(default=0)),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_inventory_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Inventory Report",
                "verbose_name_plural": "Inventory Reports",
                "db_table": "inventory_reports",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MenuAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("analytics_data", models.JSONField(default=dict)),
                ("total_items_sold", models.PositiveIntegerField(default=0)),
                (
                    "total_revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("most_popular_item", models.CharField(blank=True, max_length=200)),
                ("least_popular_item", models.CharField(blank=True, max_length=200)),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_menu_analytics",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Menu Analytics",
                "verbose_name_plural": "Menu Analytics",
                "db_table": "menu_analytics",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReportSchedule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("sales", "Sales Report"),
                            ("inventory", "Inventory Report"),
                            ("staff", "Staff Performance"),
                            ("customer", "Customer Analytics"),
                            ("menu", "Menu Analytics"),
                        ],
                        default="sales",
                        max_length=20,
                    ),
                ),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("yearly", "Yearly"),
                        ],
                        default="daily",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("email_recipients", models.JSONField(default=list)),
                ("schedule_config", models.JSONField(default=dict)),
                ("last_run", models.DateTimeField(blank=True, null=True)),
                ("next_run", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_report_schedules",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Report Schedule",
                "verbose_name_plural": "Report Schedules",
                "db_table": "report_schedules",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="SalesReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("daily", "Daily Report"),
                            ("weekly", "Weekly Report"),
                            ("monthly", "Monthly Report"),
                            ("yearly", "Yearly Report"),
                            ("custom", "Custom Date Range"),
                        ],
                        default="daily",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="generating",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("report_data", models.JSONField(default=dict)),
                ("total_orders", models.PositiveIntegerField(default=0)),
                (
                    "total_revenue",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ("total_items_sold", models.PositiveIntegerField(default=0)),
                (
                    "average_order_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("pdf_file", models.FileField(blank=True, upload_to="reports/pdf/")),
                (
                    "excel_file",
                    models.FileField(blank=True, upload_to="reports/excel/"),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Sales Report",
                "verbose_name_plural": "Sales Reports",
                "db_table": "sales_reports",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StaffPerformanceReport",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("total_orders_served", models.PositiveIntegerField(default=0)),
                (
                    "total_revenue_generated",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "average_order_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "total_hours_worked",
                    models.DecimalField(decimal_places=2, default=0, max_digits=8),
                ),
                ("performance_data", models.JSONField(default=dict)),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_staff_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
                (
                    "staff_member",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_reports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Staff Performance Report",
                "verbose_name_plural": "Staff Performance Reports",
                "db_table": "staff_performance_reports",
                "ordering": ["-created_at"],
            },
        ),
    ]

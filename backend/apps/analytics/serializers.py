"""
Analytics serializers for Restaurant POS system.
"""

from rest_framework import serializers
from decimal import Decimal
from .models import (
    SalesReport, InventoryReport, StaffPerformanceReport,
    CustomerAnalytics, MenuAnalytics, ReportSchedule, DashboardWidget
)


class SalesReportSerializer(serializers.ModelSerializer):
    """
    Sales report serializer.
    """
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = SalesReport
        fields = [
            'id', 'report_type', 'status', 'start_date', 'end_date',
            'report_data', 'total_orders', 'total_revenue', 'total_items_sold',
            'average_order_value', 'generated_by', 'generated_by_name',
            'pdf_file', 'excel_file', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'status', 'report_data', 'total_orders', 'total_revenue',
            'total_items_sold', 'average_order_value', 'pdf_file', 'excel_file',
            'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['generated_by'] = self.context['request'].user
        return super().create(validated_data)


class InventoryReportSerializer(serializers.ModelSerializer):
    """
    Inventory report serializer.
    """
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = InventoryReport
        fields = [
            'id', 'report_type', 'start_date', 'end_date', 'report_data',
            'total_items', 'total_value', 'low_stock_items', 'out_of_stock_items',
            'generated_by', 'generated_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'report_data', 'total_items', 'total_value',
            'low_stock_items', 'out_of_stock_items', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['generated_by'] = self.context['request'].user
        return super().create(validated_data)


class StaffPerformanceReportSerializer(serializers.ModelSerializer):
    """
    Staff performance report serializer.
    """
    staff_member_name = serializers.CharField(source='staff_member.get_full_name', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = StaffPerformanceReport
        fields = [
            'id', 'start_date', 'end_date', 'staff_member', 'staff_member_name',
            'total_orders_served', 'total_revenue_generated', 'average_order_value',
            'total_hours_worked', 'performance_data', 'generated_by',
            'generated_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_orders_served', 'total_revenue_generated',
            'average_order_value', 'total_hours_worked', 'performance_data',
            'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['generated_by'] = self.context['request'].user
        return super().create(validated_data)


class CustomerAnalyticsSerializer(serializers.ModelSerializer):
    """
    Customer analytics serializer.
    """
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = CustomerAnalytics
        fields = [
            'id', 'start_date', 'end_date', 'total_customers', 'new_customers',
            'returning_customers', 'average_customer_value', 'analytics_data',
            'generated_by', 'generated_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_customers', 'new_customers', 'returning_customers',
            'average_customer_value', 'analytics_data', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['generated_by'] = self.context['request'].user
        return super().create(validated_data)


class MenuAnalyticsSerializer(serializers.ModelSerializer):
    """
    Menu analytics serializer.
    """
    generated_by_name = serializers.CharField(source='generated_by.get_full_name', read_only=True)
    
    class Meta:
        model = MenuAnalytics
        fields = [
            'id', 'start_date', 'end_date', 'analytics_data', 'total_items_sold',
            'total_revenue', 'most_popular_item', 'least_popular_item',
            'generated_by', 'generated_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'analytics_data', 'total_items_sold', 'total_revenue',
            'most_popular_item', 'least_popular_item', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['generated_by'] = self.context['request'].user
        return super().create(validated_data)


class ReportScheduleSerializer(serializers.ModelSerializer):
    """
    Report schedule serializer.
    """
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ReportSchedule
        fields = [
            'id', 'name', 'report_type', 'frequency', 'is_active',
            'email_recipients', 'schedule_config', 'last_run', 'next_run',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_run', 'next_run', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DashboardWidgetSerializer(serializers.ModelSerializer):
    """
    Dashboard widget serializer.
    """
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = DashboardWidget
        fields = [
            'id', 'name', 'widget_type', 'config', 'position_x', 'position_y',
            'width', 'height', 'is_active', 'created_by', 'created_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DashboardDataSerializer(serializers.Serializer):
    """
    Serializer for dashboard data.
    """
    total_revenue_today = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_orders_today = serializers.IntegerField()
    average_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    revenue_growth = serializers.DecimalField(max_digits=5, decimal_places=2)
    
    # Charts data
    sales_chart_data = serializers.ListField()
    top_selling_items = serializers.ListField()
    order_types_breakdown = serializers.DictField()
    hourly_sales = serializers.ListField()
    
    # Alerts
    low_stock_alerts = serializers.ListField()
    pending_orders = serializers.IntegerField()
    staff_on_duty = serializers.IntegerField()


class SalesAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for sales analytics data.
    """
    period = serializers.CharField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_orders = serializers.IntegerField()
    average_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    growth_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    
    # Detailed breakdowns
    daily_sales = serializers.ListField()
    payment_methods = serializers.DictField()
    order_types = serializers.DictField()
    peak_hours = serializers.ListField()
    
    # Comparisons
    previous_period_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    previous_period_orders = serializers.IntegerField()


class InventoryAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for inventory analytics data.
    """
    total_items = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2)
    low_stock_items = serializers.IntegerField()
    out_of_stock_items = serializers.IntegerField()
    
    # Categories breakdown
    categories_breakdown = serializers.ListField()
    
    # Stock movements
    recent_adjustments = serializers.ListField()
    top_consumed_items = serializers.ListField()
    
    # Supplier performance
    supplier_performance = serializers.ListField()
    
    # Alerts
    reorder_suggestions = serializers.ListField()


class MenuPerformanceSerializer(serializers.Serializer):
    """
    Serializer for menu performance analytics.
    """
    total_items_sold = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    
    # Top performers
    top_selling_items = serializers.ListField()
    top_revenue_items = serializers.ListField()
    
    # Poor performers
    least_selling_items = serializers.ListField()
    
    # Category performance
    category_performance = serializers.ListField()
    
    # Trends
    item_trends = serializers.ListField()
    
    # Recommendations
    menu_recommendations = serializers.ListField()

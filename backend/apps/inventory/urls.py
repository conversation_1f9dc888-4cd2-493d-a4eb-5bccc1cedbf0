"""
Inventory URLs for Restaurant POS system.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    UnitOfMeasureViewSet, InventoryCategoryViewSet, SupplierViewSet,
    InventoryItemViewSet, StockAdjustmentViewSet, PurchaseOrderViewSet,
    InventoryCountViewSet, inventory_report
)

router = DefaultRouter()
router.register(r'units', UnitOfMeasureViewSet, basename='units-of-measure')
router.register(r'categories', InventoryCategoryViewSet, basename='inventory-categories')
router.register(r'suppliers', SupplierViewSet, basename='suppliers')
router.register(r'items', InventoryItemViewSet, basename='inventory-items')
router.register(r'adjustments', StockAdjustmentViewSet, basename='stock-adjustments')
router.register(r'purchase-orders', PurchaseOrderViewSet, basename='purchase-orders')
router.register(r'counts', InventoryCountViewSet, basename='inventory-counts')

app_name = 'inventory'
urlpatterns = [
    path('', include(router.urls)),
    path('reports/', inventory_report, name='inventory-report'),
]

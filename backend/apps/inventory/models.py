"""
Inventory models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from apps.core.models import BaseModel, TenantAwareModel, SoftDeleteModel
from apps.restaurants.models import Restaurant


class InventoryCategory(TenantAwareModel, SoftDeleteModel):
    """
    Category for inventory items (e.g., Vegetables, Meat, Beverages, Supplies).
    """

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    sort_order = models.PositiveIntegerField(default=0)

    class Meta:
        db_table = "inventory_categories"
        verbose_name = _("Inventory Category")
        verbose_name_plural = _("Inventory Categories")
        unique_together = ["restaurant", "name"]
        ordering = ["sort_order", "name"]

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"


class UnitOfMeasure(BaseModel):
    """
    Units of measure for inventory items (kg, lbs, pieces, liters, etc.).
    """

    name = models.CharField(max_length=50, unique=True)
    abbreviation = models.CharField(max_length=10, unique=True)
    unit_type = models.CharField(
        max_length=20,
        choices=[
            ("weight", _("Weight")),
            ("volume", _("Volume")),
            ("count", _("Count")),
            ("length", _("Length")),
            ("area", _("Area")),
        ],
        default="count",
    )

    class Meta:
        db_table = "units_of_measure"
        verbose_name = _("Unit of Measure")
        verbose_name_plural = _("Units of Measure")
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} ({self.abbreviation})"


class Supplier(TenantAwareModel, SoftDeleteModel):
    """
    Supplier/vendor information for inventory management.
    """

    class SupplierType(models.TextChoices):
        FOOD_DISTRIBUTOR = "food_distributor", _("Food Distributor")
        BEVERAGE_SUPPLIER = "beverage_supplier", _("Beverage Supplier")
        EQUIPMENT_SUPPLIER = "equipment_supplier", _("Equipment Supplier")
        CLEANING_SUPPLIES = "cleaning_supplies", _("Cleaning Supplies")
        OTHER = "other", _("Other")

    name = models.CharField(max_length=200)
    supplier_type = models.CharField(max_length=30, choices=SupplierType.choices, default=SupplierType.FOOD_DISTRIBUTOR)

    # Contact Information
    contact_person = models.CharField(max_length=100, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)

    # Address
    address_line_1 = models.CharField(max_length=200, blank=True)
    address_line_2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, default="United States")

    # Business Terms
    payment_terms = models.CharField(max_length=100, blank=True)  # e.g., "Net 30"
    delivery_days = models.JSONField(default=list, blank=True)  # Days of week for delivery
    minimum_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Status
    is_active = models.BooleanField(default=True)
    rating = models.PositiveIntegerField(default=5, validators=[MinValueValidator(1), MaxValueValidator(5)])
    notes = models.TextField(blank=True)

    class Meta:
        db_table = "suppliers"
        verbose_name = _("Supplier")
        verbose_name_plural = _("Suppliers")
        unique_together = ["restaurant", "name"]
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"

    @property
    def full_address(self):
        """Return formatted full address."""
        address_parts = []
        if self.address_line_1:
            address_parts.append(self.address_line_1)
        if self.address_line_2:
            address_parts.append(self.address_line_2)
        if self.city:
            address_parts.append(self.city)
        if self.state:
            address_parts.append(self.state)
        if self.postal_code:
            address_parts.append(self.postal_code)
        return ", ".join(address_parts) if address_parts else ""


class InventoryItem(TenantAwareModel, SoftDeleteModel):
    """
    Inventory item model for tracking stock.
    """

    class ItemType(models.TextChoices):
        INGREDIENT = "ingredient", _("Ingredient")
        BEVERAGE = "beverage", _("Beverage")
        SUPPLY = "supply", _("Supply")
        EQUIPMENT = "equipment", _("Equipment")

    category = models.ForeignKey(InventoryCategory, on_delete=models.CASCADE, related_name="items")
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    item_type = models.CharField(max_length=20, choices=ItemType.choices, default=ItemType.INGREDIENT)

    # Product Information
    sku = models.CharField(max_length=50, blank=True)
    barcode = models.CharField(max_length=100, blank=True)
    brand = models.CharField(max_length=100, blank=True)

    # Units and Measurements
    unit_of_measure = models.ForeignKey(UnitOfMeasure, on_delete=models.PROTECT, related_name="inventory_items")

    # Stock Information
    current_stock = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    minimum_stock = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    maximum_stock = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    reorder_point = models.DecimalField(max_digits=10, decimal_places=3, default=0)

    # Pricing
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    last_purchase_price = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    average_cost = models.DecimalField(max_digits=10, decimal_places=4, default=0)

    # Storage Information
    storage_location = models.CharField(max_length=100, blank=True)
    storage_temperature = models.CharField(max_length=50, blank=True)  # e.g., "Refrigerated", "Frozen", "Room Temperature"
    shelf_life_days = models.PositiveIntegerField(null=True, blank=True)

    # Supplier Information
    primary_supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name="primary_items")

    # Status
    is_active = models.BooleanField(default=True)
    track_expiry = models.BooleanField(default=False)

    class Meta:
        db_table = "inventory_items"
        verbose_name = _("Inventory Item")
        verbose_name_plural = _("Inventory Items")
        unique_together = ["restaurant", "name"]
        ordering = ["category__sort_order", "name"]

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"

    @property
    def is_low_stock(self):
        """Check if item is below reorder point."""
        return self.current_stock <= self.reorder_point

    @property
    def is_out_of_stock(self):
        """Check if item is out of stock."""
        return self.current_stock <= 0

    @property
    def stock_value(self):
        """Calculate total value of current stock."""
        return self.current_stock * self.unit_cost

    def adjust_stock(self, quantity, reason="Manual adjustment"):
        """Adjust stock quantity and create adjustment record."""
        old_stock = self.current_stock
        self.current_stock += Decimal(str(quantity))
        self.save(update_fields=["current_stock"])

        # Create stock adjustment record
        StockAdjustment.objects.create(
            inventory_item=self, adjustment_type="manual", quantity_change=quantity, old_quantity=old_stock, new_quantity=self.current_stock, reason=reason
        )

        return self.current_stock


class StockAdjustment(BaseModel):
    """
    Record of stock adjustments for audit trail.
    """

    class AdjustmentType(models.TextChoices):
        MANUAL = "manual", _("Manual Adjustment")
        PURCHASE = "purchase", _("Purchase Order")
        SALE = "sale", _("Sale/Usage")
        WASTE = "waste", _("Waste/Spoilage")
        TRANSFER = "transfer", _("Transfer")
        INVENTORY_COUNT = "inventory_count", _("Inventory Count")

    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name="stock_adjustments")
    adjustment_type = models.CharField(max_length=20, choices=AdjustmentType.choices, default=AdjustmentType.MANUAL)
    quantity_change = models.DecimalField(max_digits=10, decimal_places=3)
    old_quantity = models.DecimalField(max_digits=10, decimal_places=3)
    new_quantity = models.DecimalField(max_digits=10, decimal_places=3)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4, default=0)
    reason = models.CharField(max_length=200, blank=True)
    reference_id = models.CharField(max_length=100, blank=True)  # PO number, order ID, etc.

    # User who made the adjustment
    adjusted_by = models.ForeignKey("users.User", on_delete=models.SET_NULL, null=True, blank=True, related_name="stock_adjustments")

    class Meta:
        db_table = "stock_adjustments"
        verbose_name = _("Stock Adjustment")
        verbose_name_plural = _("Stock Adjustments")
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.inventory_item.name} - {self.quantity_change} ({self.adjustment_type})"

    @property
    def value_change(self):
        """Calculate value change from this adjustment."""
        return self.quantity_change * self.unit_cost


class PurchaseOrder(TenantAwareModel, SoftDeleteModel):
    """
    Purchase order for ordering inventory from suppliers.
    """

    class Status(models.TextChoices):
        DRAFT = "draft", _("Draft")
        SENT = "sent", _("Sent to Supplier")
        CONFIRMED = "confirmed", _("Confirmed by Supplier")
        PARTIALLY_RECEIVED = "partially_received", _("Partially Received")
        RECEIVED = "received", _("Fully Received")
        CANCELLED = "cancelled", _("Cancelled")

    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, related_name="purchase_orders")
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.DRAFT)

    # Dates
    order_date = models.DateField(auto_now_add=True)
    expected_delivery_date = models.DateField(null=True, blank=True)
    actual_delivery_date = models.DateField(null=True, blank=True)

    # Amounts
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Additional Information
    notes = models.TextField(blank=True)
    delivery_instructions = models.TextField(blank=True)

    # User who created the PO
    created_by = models.ForeignKey("users.User", on_delete=models.SET_NULL, null=True, blank=True, related_name="created_purchase_orders")

    class Meta:
        db_table = "purchase_orders"
        verbose_name = _("Purchase Order")
        verbose_name_plural = _("Purchase Orders")
        ordering = ["-created_at"]

    def __str__(self):
        return f"PO-{self.po_number} - {self.supplier.name}"

    def save(self, *args, **kwargs):
        if not self.po_number:
            # Generate PO number
            last_po = PurchaseOrder.objects.filter(restaurant=self.restaurant).order_by("-created_at").first()

            if last_po and last_po.po_number.startswith("PO"):
                try:
                    last_number = int(last_po.po_number.split("-")[1])
                    self.po_number = f"PO-{last_number + 1:06d}"
                except (IndexError, ValueError):
                    self.po_number = f"PO-{self.restaurant.id.hex[:6].upper()}-001"
            else:
                self.po_number = f"PO-{self.restaurant.id.hex[:6].upper()}-001"

        super().save(*args, **kwargs)

    def calculate_totals(self):
        """Calculate and update PO totals."""
        items_total = sum(item.total_cost for item in self.items.all())
        self.subtotal = items_total
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_cost
        self.save(update_fields=["subtotal", "total_amount"])

    @property
    def is_overdue(self):
        """Check if PO is overdue for delivery."""
        if self.expected_delivery_date and self.status not in ["received", "cancelled"]:
            from django.utils import timezone

            return timezone.now().date() > self.expected_delivery_date
        return False


class PurchaseOrderItem(BaseModel):
    """
    Individual items in a purchase order.
    """

    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name="items")
    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name="purchase_order_items")
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=3)
    quantity_received = models.DecimalField(max_digits=10, decimal_places=3, default=0)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4)
    total_cost = models.DecimalField(max_digits=10, decimal_places=2)

    # Receiving information
    expiry_date = models.DateField(null=True, blank=True)
    batch_number = models.CharField(max_length=50, blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = "purchase_order_items"
        unique_together = ["purchase_order", "inventory_item"]

    def __str__(self):
        return f"{self.inventory_item.name} - {self.quantity_ordered} @ {self.unit_cost}"

    def save(self, *args, **kwargs):
        self.total_cost = self.quantity_ordered * self.unit_cost
        super().save(*args, **kwargs)

    @property
    def quantity_pending(self):
        """Calculate quantity still pending delivery."""
        return self.quantity_ordered - self.quantity_received

    @property
    def is_fully_received(self):
        """Check if item is fully received."""
        return self.quantity_received >= self.quantity_ordered


class InventoryCount(TenantAwareModel):
    """
    Physical inventory count sessions.
    """

    class Status(models.TextChoices):
        PLANNED = "planned", _("Planned")
        IN_PROGRESS = "in_progress", _("In Progress")
        COMPLETED = "completed", _("Completed")
        CANCELLED = "cancelled", _("Cancelled")

    name = models.CharField(max_length=200)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.PLANNED)
    count_date = models.DateField()
    notes = models.TextField(blank=True)

    # User who conducted the count
    counted_by = models.ForeignKey("users.User", on_delete=models.SET_NULL, null=True, blank=True, related_name="inventory_counts")

    class Meta:
        db_table = "inventory_counts"
        verbose_name = _("Inventory Count")
        verbose_name_plural = _("Inventory Counts")
        ordering = ["-count_date"]

    def __str__(self):
        return f"{self.name} - {self.count_date}"


class InventoryCountItem(BaseModel):
    """
    Individual item counts within an inventory count session.
    """

    inventory_count = models.ForeignKey(InventoryCount, on_delete=models.CASCADE, related_name="count_items")
    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name="count_records")
    system_quantity = models.DecimalField(max_digits=10, decimal_places=3)
    counted_quantity = models.DecimalField(max_digits=10, decimal_places=3)
    variance = models.DecimalField(max_digits=10, decimal_places=3)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = "inventory_count_items"
        unique_together = ["inventory_count", "inventory_item"]

    def __str__(self):
        return f"{self.inventory_item.name} - Count: {self.counted_quantity}"

    def save(self, *args, **kwargs):
        self.variance = self.counted_quantity - self.system_quantity
        super().save(*args, **kwargs)

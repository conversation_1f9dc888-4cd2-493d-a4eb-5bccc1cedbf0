"""
Inventory serializers for Restaurant POS system.
"""

from rest_framework import serializers
from decimal import Decimal
from .models import (
    InventoryCategory, UnitOfMeasure, Supplier, InventoryItem,
    StockAdjustment, PurchaseOrder, PurchaseOrderItem,
    InventoryCount, InventoryCountItem
)


class UnitOfMeasureSerializer(serializers.ModelSerializer):
    """
    Unit of measure serializer.
    """
    class Meta:
        model = UnitOfMeasure
        fields = ['id', 'name', 'abbreviation', 'unit_type', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class InventoryCategorySerializer(serializers.ModelSerializer):
    """
    Inventory category serializer.
    """
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = InventoryCategory
        fields = [
            'id', 'name', 'description', 'sort_order', 'items_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_items_count(self, obj):
        return obj.items.filter(is_active=True).count()

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class SupplierSerializer(serializers.ModelSerializer):
    """
    Supplier serializer.
    """
    full_address = serializers.ReadOnlyField()
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'supplier_type', 'contact_person', 'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country',
            'payment_terms', 'delivery_days', 'minimum_order_amount', 'is_active',
            'rating', 'notes', 'full_address', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class InventoryItemSerializer(serializers.ModelSerializer):
    """
    Inventory item serializer.
    """
    is_low_stock = serializers.ReadOnlyField()
    is_out_of_stock = serializers.ReadOnlyField()
    stock_value = serializers.ReadOnlyField()
    category_name = serializers.CharField(source='category.name', read_only=True)
    unit_name = serializers.CharField(source='unit_of_measure.name', read_only=True)
    unit_abbreviation = serializers.CharField(source='unit_of_measure.abbreviation', read_only=True)
    supplier_name = serializers.CharField(source='primary_supplier.name', read_only=True)
    
    class Meta:
        model = InventoryItem
        fields = [
            'id', 'category', 'category_name', 'name', 'description', 'item_type',
            'sku', 'barcode', 'brand', 'unit_of_measure', 'unit_name', 'unit_abbreviation',
            'current_stock', 'minimum_stock', 'maximum_stock', 'reorder_point',
            'unit_cost', 'last_purchase_price', 'average_cost', 'storage_location',
            'storage_temperature', 'shelf_life_days', 'primary_supplier', 'supplier_name',
            'is_active', 'track_expiry', 'is_low_stock', 'is_out_of_stock', 'stock_value',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class InventoryItemListSerializer(serializers.ModelSerializer):
    """
    Simplified inventory item serializer for list views.
    """
    is_low_stock = serializers.ReadOnlyField()
    is_out_of_stock = serializers.ReadOnlyField()
    category_name = serializers.CharField(source='category.name', read_only=True)
    unit_abbreviation = serializers.CharField(source='unit_of_measure.abbreviation', read_only=True)
    
    class Meta:
        model = InventoryItem
        fields = [
            'id', 'name', 'category_name', 'current_stock', 'reorder_point',
            'unit_abbreviation', 'unit_cost', 'is_low_stock', 'is_out_of_stock',
            'is_active'
        ]


class StockAdjustmentSerializer(serializers.ModelSerializer):
    """
    Stock adjustment serializer.
    """
    inventory_item_name = serializers.CharField(source='inventory_item.name', read_only=True)
    adjusted_by_name = serializers.CharField(source='adjusted_by.get_full_name', read_only=True)
    value_change = serializers.ReadOnlyField()
    
    class Meta:
        model = StockAdjustment
        fields = [
            'id', 'inventory_item', 'inventory_item_name', 'adjustment_type',
            'quantity_change', 'old_quantity', 'new_quantity', 'unit_cost',
            'reason', 'reference_id', 'adjusted_by', 'adjusted_by_name',
            'value_change', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class StockAdjustmentCreateSerializer(serializers.Serializer):
    """
    Serializer for creating stock adjustments.
    """
    inventory_item_id = serializers.UUIDField()
    quantity_change = serializers.DecimalField(max_digits=10, decimal_places=3)
    adjustment_type = serializers.ChoiceField(choices=StockAdjustment.AdjustmentType.choices)
    reason = serializers.CharField(max_length=200, required=False)
    unit_cost = serializers.DecimalField(max_digits=10, decimal_places=4, required=False)

    def create(self, validated_data):
        inventory_item = InventoryItem.objects.get(id=validated_data['inventory_item_id'])
        
        # Create stock adjustment
        adjustment = StockAdjustment.objects.create(
            inventory_item=inventory_item,
            adjustment_type=validated_data['adjustment_type'],
            quantity_change=validated_data['quantity_change'],
            old_quantity=inventory_item.current_stock,
            new_quantity=inventory_item.current_stock + validated_data['quantity_change'],
            unit_cost=validated_data.get('unit_cost', inventory_item.unit_cost),
            reason=validated_data.get('reason', ''),
            adjusted_by=self.context['request'].user
        )
        
        # Update inventory item stock
        inventory_item.current_stock += validated_data['quantity_change']
        inventory_item.save(update_fields=['current_stock'])
        
        return adjustment


class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    """
    Purchase order item serializer.
    """
    inventory_item_name = serializers.CharField(source='inventory_item.name', read_only=True)
    unit_abbreviation = serializers.CharField(source='inventory_item.unit_of_measure.abbreviation', read_only=True)
    quantity_pending = serializers.ReadOnlyField()
    is_fully_received = serializers.ReadOnlyField()
    
    class Meta:
        model = PurchaseOrderItem
        fields = [
            'id', 'inventory_item', 'inventory_item_name', 'unit_abbreviation',
            'quantity_ordered', 'quantity_received', 'quantity_pending',
            'unit_cost', 'total_cost', 'expiry_date', 'batch_number',
            'notes', 'is_fully_received', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_cost', 'created_at', 'updated_at']


class PurchaseOrderSerializer(serializers.ModelSerializer):
    """
    Purchase order serializer.
    """
    items = PurchaseOrderItemSerializer(many=True, read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    is_overdue = serializers.ReadOnlyField()
    
    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'po_number', 'supplier', 'supplier_name', 'status',
            'order_date', 'expected_delivery_date', 'actual_delivery_date',
            'subtotal', 'tax_amount', 'shipping_cost', 'total_amount',
            'notes', 'delivery_instructions', 'created_by', 'created_by_name',
            'is_overdue', 'items', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'po_number', 'order_date', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PurchaseOrderListSerializer(serializers.ModelSerializer):
    """
    Simplified purchase order serializer for list views.
    """
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    is_overdue = serializers.ReadOnlyField()
    
    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'po_number', 'supplier_name', 'status', 'order_date',
            'expected_delivery_date', 'total_amount', 'is_overdue'
        ]


class InventoryCountItemSerializer(serializers.ModelSerializer):
    """
    Inventory count item serializer.
    """
    inventory_item_name = serializers.CharField(source='inventory_item.name', read_only=True)
    unit_abbreviation = serializers.CharField(source='inventory_item.unit_of_measure.abbreviation', read_only=True)
    
    class Meta:
        model = InventoryCountItem
        fields = [
            'id', 'inventory_item', 'inventory_item_name', 'unit_abbreviation',
            'system_quantity', 'counted_quantity', 'variance', 'notes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'variance', 'created_at', 'updated_at']


class InventoryCountSerializer(serializers.ModelSerializer):
    """
    Inventory count serializer.
    """
    count_items = InventoryCountItemSerializer(many=True, read_only=True)
    counted_by_name = serializers.CharField(source='counted_by.get_full_name', read_only=True)
    
    class Meta:
        model = InventoryCount
        fields = [
            'id', 'name', 'status', 'count_date', 'notes',
            'counted_by', 'counted_by_name', 'count_items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class InventoryReportSerializer(serializers.Serializer):
    """
    Serializer for inventory reports.
    """
    total_items = serializers.IntegerField()
    total_value = serializers.DecimalField(max_digits=12, decimal_places=2)
    low_stock_items = serializers.IntegerField()
    out_of_stock_items = serializers.IntegerField()
    categories_breakdown = serializers.ListField()
    top_value_items = serializers.ListField()
    recent_adjustments = serializers.ListField()

# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("restaurants", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="InventoryCount",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("planned", "Planned"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="planned",
                        max_length=20,
                    ),
                ),
                ("count_date", models.DateField()),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Inventory Count",
                "verbose_name_plural": "Inventory Counts",
                "db_table": "inventory_counts",
                "ordering": ["-count_date"],
            },
        ),
        migrations.CreateModel(
            name="InventoryCountItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "system_quantity",
                    models.DecimalField(decimal_places=3, max_digits=10),
                ),
                (
                    "counted_quantity",
                    models.DecimalField(decimal_places=3, max_digits=10),
                ),
                ("variance", models.DecimalField(decimal_places=3, max_digits=10)),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "db_table": "inventory_count_items",
            },
        ),
        migrations.CreateModel(
            name="InventoryItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "item_type",
                    models.CharField(
                        choices=[
                            ("ingredient", "Ingredient"),
                            ("beverage", "Beverage"),
                            ("supply", "Supply"),
                            ("equipment", "Equipment"),
                        ],
                        default="ingredient",
                        max_length=20,
                    ),
                ),
                ("sku", models.CharField(blank=True, max_length=50)),
                ("barcode", models.CharField(blank=True, max_length=100)),
                ("brand", models.CharField(blank=True, max_length=100)),
                (
                    "current_stock",
                    models.DecimalField(decimal_places=3, default=0, max_digits=10),
                ),
                (
                    "minimum_stock",
                    models.DecimalField(decimal_places=3, default=0, max_digits=10),
                ),
                (
                    "maximum_stock",
                    models.DecimalField(decimal_places=3, default=0, max_digits=10),
                ),
                (
                    "reorder_point",
                    models.DecimalField(decimal_places=3, default=0, max_digits=10),
                ),
                (
                    "unit_cost",
                    models.DecimalField(decimal_places=4, default=0, max_digits=10),
                ),
                (
                    "last_purchase_price",
                    models.DecimalField(decimal_places=4, default=0, max_digits=10),
                ),
                (
                    "average_cost",
                    models.DecimalField(decimal_places=4, default=0, max_digits=10),
                ),
                ("storage_location", models.CharField(blank=True, max_length=100)),
                ("storage_temperature", models.CharField(blank=True, max_length=50)),
                ("shelf_life_days", models.PositiveIntegerField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("track_expiry", models.BooleanField(default=False)),
            ],
            options={
                "verbose_name": "Inventory Item",
                "verbose_name_plural": "Inventory Items",
                "db_table": "inventory_items",
                "ordering": ["category__sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("po_number", models.CharField(max_length=50, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("sent", "Sent to Supplier"),
                            ("confirmed", "Confirmed by Supplier"),
                            ("partially_received", "Partially Received"),
                            ("received", "Fully Received"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("order_date", models.DateField(auto_now_add=True)),
                ("expected_delivery_date", models.DateField(blank=True, null=True)),
                ("actual_delivery_date", models.DateField(blank=True, null=True)),
                (
                    "subtotal",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "tax_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "shipping_cost",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "total_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("notes", models.TextField(blank=True)),
                ("delivery_instructions", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Purchase Order",
                "verbose_name_plural": "Purchase Orders",
                "db_table": "purchase_orders",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrderItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "quantity_ordered",
                    models.DecimalField(decimal_places=3, max_digits=10),
                ),
                (
                    "quantity_received",
                    models.DecimalField(decimal_places=3, default=0, max_digits=10),
                ),
                ("unit_cost", models.DecimalField(decimal_places=4, max_digits=10)),
                ("total_cost", models.DecimalField(decimal_places=2, max_digits=10)),
                ("expiry_date", models.DateField(blank=True, null=True)),
                ("batch_number", models.CharField(blank=True, max_length=50)),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "db_table": "purchase_order_items",
            },
        ),
        migrations.CreateModel(
            name="StockAdjustment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "adjustment_type",
                    models.CharField(
                        choices=[
                            ("manual", "Manual Adjustment"),
                            ("purchase", "Purchase Order"),
                            ("sale", "Sale/Usage"),
                            ("waste", "Waste/Spoilage"),
                            ("transfer", "Transfer"),
                            ("inventory_count", "Inventory Count"),
                        ],
                        default="manual",
                        max_length=20,
                    ),
                ),
                (
                    "quantity_change",
                    models.DecimalField(decimal_places=3, max_digits=10),
                ),
                ("old_quantity", models.DecimalField(decimal_places=3, max_digits=10)),
                ("new_quantity", models.DecimalField(decimal_places=3, max_digits=10)),
                (
                    "unit_cost",
                    models.DecimalField(decimal_places=4, default=0, max_digits=10),
                ),
                ("reason", models.CharField(blank=True, max_length=200)),
                ("reference_id", models.CharField(blank=True, max_length=100)),
            ],
            options={
                "verbose_name": "Stock Adjustment",
                "verbose_name_plural": "Stock Adjustments",
                "db_table": "stock_adjustments",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Supplier",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=200)),
                (
                    "supplier_type",
                    models.CharField(
                        choices=[
                            ("food_distributor", "Food Distributor"),
                            ("beverage_supplier", "Beverage Supplier"),
                            ("equipment_supplier", "Equipment Supplier"),
                            ("cleaning_supplies", "Cleaning Supplies"),
                            ("other", "Other"),
                        ],
                        default="food_distributor",
                        max_length=30,
                    ),
                ),
                ("contact_person", models.CharField(blank=True, max_length=100)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("phone", models.CharField(blank=True, max_length=20)),
                ("website", models.URLField(blank=True)),
                ("address_line_1", models.CharField(blank=True, max_length=200)),
                ("address_line_2", models.CharField(blank=True, max_length=200)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("state", models.CharField(blank=True, max_length=100)),
                ("postal_code", models.CharField(blank=True, max_length=20)),
                ("country", models.CharField(default="United States", max_length=100)),
                ("payment_terms", models.CharField(blank=True, max_length=100)),
                ("delivery_days", models.JSONField(blank=True, default=list)),
                (
                    "minimum_order_amount",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "rating",
                    models.PositiveIntegerField(
                        default=5,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("notes", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Supplier",
                "verbose_name_plural": "Suppliers",
                "db_table": "suppliers",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="UnitOfMeasure",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=50, unique=True)),
                ("abbreviation", models.CharField(max_length=10, unique=True)),
                (
                    "unit_type",
                    models.CharField(
                        choices=[
                            ("weight", "Weight"),
                            ("volume", "Volume"),
                            ("count", "Count"),
                            ("length", "Length"),
                            ("area", "Area"),
                        ],
                        default="count",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "verbose_name": "Unit of Measure",
                "verbose_name_plural": "Units of Measure",
                "db_table": "units_of_measure",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="InventoryCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Inventory Category",
                "verbose_name_plural": "Inventory Categories",
                "db_table": "inventory_categories",
                "ordering": ["sort_order", "name"],
            },
        ),
    ]

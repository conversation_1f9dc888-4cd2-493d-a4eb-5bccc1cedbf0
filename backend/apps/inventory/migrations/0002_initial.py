# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("inventory", "0001_initial"),
        ("restaurants", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="inventorycount",
            name="counted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="inventory_counts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="inventorycount",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="inventorycountitem",
            name="inventory_count",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="count_items",
                to="inventory.inventorycount",
            ),
        ),
        migrations.AddField(
            model_name="inventoryitem",
            name="category",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="inventory.inventorycategory",
            ),
        ),
        migrations.AddField(
            model_name="inventoryitem",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="inventorycountitem",
            name="inventory_item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="count_records",
                to="inventory.inventoryitem",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_purchase_orders",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorderitem",
            name="inventory_item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="purchase_order_items",
                to="inventory.inventoryitem",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorderitem",
            name="purchase_order",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="inventory.purchaseorder",
            ),
        ),
        migrations.AddField(
            model_name="stockadjustment",
            name="adjusted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stock_adjustments",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="stockadjustment",
            name="inventory_item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="stock_adjustments",
                to="inventory.inventoryitem",
            ),
        ),
        migrations.AddField(
            model_name="supplier",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="supplier",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="purchase_orders",
                to="inventory.supplier",
            ),
        ),
        migrations.AddField(
            model_name="inventoryitem",
            name="primary_supplier",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="primary_items",
                to="inventory.supplier",
            ),
        ),
        migrations.AddField(
            model_name="inventoryitem",
            name="unit_of_measure",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="inventory_items",
                to="inventory.unitofmeasure",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="inventorycategory",
            unique_together={("restaurant", "name")},
        ),
        migrations.AlterUniqueTogether(
            name="inventorycountitem",
            unique_together={("inventory_count", "inventory_item")},
        ),
        migrations.AlterUniqueTogether(
            name="purchaseorderitem",
            unique_together={("purchase_order", "inventory_item")},
        ),
        migrations.AlterUniqueTogether(
            name="supplier",
            unique_together={("restaurant", "name")},
        ),
        migrations.AlterUniqueTogether(
            name="inventoryitem",
            unique_together={("restaurant", "name")},
        ),
    ]

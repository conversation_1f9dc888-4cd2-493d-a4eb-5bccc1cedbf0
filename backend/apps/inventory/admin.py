"""
Inventory admin configuration.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import (
    InventoryCategory, UnitOfMeasure, Supplier, InventoryItem,
    StockAdjustment, PurchaseOrder, PurchaseOrderItem,
    InventoryCount, InventoryCountItem
)


@admin.register(UnitOfMeasure)
class UnitOfMeasureAdmin(admin.ModelAdmin):
    list_display = ['name', 'abbreviation', 'unit_type', 'created_at']
    list_filter = ['unit_type']
    search_fields = ['name', 'abbreviation']
    ordering = ['name']


@admin.register(InventoryCategory)
class InventoryCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'restaurant', 'sort_order', 'items_count', 'created_at']
    list_filter = ['restaurant', 'created_at']
    search_fields = ['name', 'description', 'restaurant__name']
    ordering = ['restaurant', 'sort_order', 'name']
    
    def items_count(self, obj):
        return obj.items.count()
    items_count.short_description = 'Items Count'


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['name', 'restaurant', 'supplier_type', 'contact_person', 'is_active', 'rating', 'created_at']
    list_filter = ['restaurant', 'supplier_type', 'is_active', 'rating', 'created_at']
    search_fields = ['name', 'contact_person', 'email', 'restaurant__name']
    ordering = ['restaurant', 'name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'supplier_type', 'is_active', 'rating')
        }),
        ('Contact Information', {
            'fields': ('contact_person', 'email', 'phone', 'website')
        }),
        ('Address', {
            'fields': ('address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country')
        }),
        ('Business Terms', {
            'fields': ('payment_terms', 'delivery_days', 'minimum_order_amount')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(InventoryItem)
class InventoryItemAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'category', 'restaurant', 'current_stock', 'reorder_point',
        'unit_cost', 'stock_status', 'is_active', 'created_at'
    ]
    list_filter = [
        'restaurant', 'category', 'item_type', 'is_active', 'track_expiry', 'created_at'
    ]
    search_fields = ['name', 'description', 'sku', 'barcode', 'restaurant__name']
    ordering = ['restaurant', 'category__sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at', 'stock_value']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('category', 'name', 'description', 'item_type')
        }),
        ('Product Details', {
            'fields': ('sku', 'barcode', 'brand', 'unit_of_measure')
        }),
        ('Stock Information', {
            'fields': ('current_stock', 'minimum_stock', 'maximum_stock', 'reorder_point')
        }),
        ('Pricing', {
            'fields': ('unit_cost', 'last_purchase_price', 'average_cost', 'stock_value')
        }),
        ('Storage', {
            'fields': ('storage_location', 'storage_temperature', 'shelf_life_days', 'track_expiry')
        }),
        ('Supplier', {
            'fields': ('primary_supplier',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def stock_status(self, obj):
        if obj.is_out_of_stock:
            return format_html('<span style="color: red;">Out of Stock</span>')
        elif obj.is_low_stock:
            return format_html('<span style="color: orange;">Low Stock</span>')
        else:
            return format_html('<span style="color: green;">In Stock</span>')
    stock_status.short_description = 'Stock Status'


@admin.register(StockAdjustment)
class StockAdjustmentAdmin(admin.ModelAdmin):
    list_display = [
        'inventory_item', 'restaurant', 'adjustment_type', 'quantity_change',
        'old_quantity', 'new_quantity', 'adjusted_by', 'created_at'
    ]
    list_filter = ['adjustment_type', 'inventory_item__restaurant', 'created_at']
    search_fields = ['inventory_item__name', 'reason', 'reference_id']
    ordering = ['-created_at']
    readonly_fields = ['created_at']
    
    def restaurant(self, obj):
        return obj.inventory_item.restaurant
    restaurant.short_description = 'Restaurant'


class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 0
    readonly_fields = ['total_cost', 'quantity_pending', 'is_fully_received']


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = [
        'po_number', 'supplier', 'restaurant', 'status', 'order_date',
        'expected_delivery_date', 'total_amount', 'is_overdue'
    ]
    list_filter = ['restaurant', 'status', 'order_date', 'expected_delivery_date']
    search_fields = ['po_number', 'supplier__name', 'restaurant__name']
    ordering = ['-created_at']
    readonly_fields = ['po_number', 'order_date', 'created_at', 'updated_at', 'is_overdue']
    inlines = [PurchaseOrderItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('po_number', 'supplier', 'status', 'created_by')
        }),
        ('Dates', {
            'fields': ('order_date', 'expected_delivery_date', 'actual_delivery_date', 'is_overdue')
        }),
        ('Amounts', {
            'fields': ('subtotal', 'tax_amount', 'shipping_cost', 'total_amount')
        }),
        ('Additional Information', {
            'fields': ('notes', 'delivery_instructions')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def restaurant(self, obj):
        return obj.supplier.restaurant
    restaurant.short_description = 'Restaurant'


class InventoryCountItemInline(admin.TabularInline):
    model = InventoryCountItem
    extra = 0
    readonly_fields = ['variance']


@admin.register(InventoryCount)
class InventoryCountAdmin(admin.ModelAdmin):
    list_display = ['name', 'restaurant', 'status', 'count_date', 'counted_by', 'created_at']
    list_filter = ['restaurant', 'status', 'count_date']
    search_fields = ['name', 'restaurant__name']
    ordering = ['-count_date']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [InventoryCountItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'status', 'count_date', 'counted_by')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

[{"model": "inventory.unitofmeasure", "pk": 1, "fields": {"name": "Kilogram", "abbreviation": "kg", "unit_type": "weight"}}, {"model": "inventory.unitofmeasure", "pk": 2, "fields": {"name": "Gram", "abbreviation": "g", "unit_type": "weight"}}, {"model": "inventory.unitofmeasure", "pk": 3, "fields": {"name": "Pound", "abbreviation": "lb", "unit_type": "weight"}}, {"model": "inventory.unitofmeasure", "pk": 4, "fields": {"name": "<PERSON><PERSON><PERSON>", "abbreviation": "oz", "unit_type": "weight"}}, {"model": "inventory.unitofmeasure", "pk": 5, "fields": {"name": "Liter", "abbreviation": "L", "unit_type": "volume"}}, {"model": "inventory.unitofmeasure", "pk": 6, "fields": {"name": "Milliliter", "abbreviation": "mL", "unit_type": "volume"}}, {"model": "inventory.unitofmeasure", "pk": 7, "fields": {"name": "<PERSON>allo<PERSON>", "abbreviation": "gal", "unit_type": "volume"}}, {"model": "inventory.unitofmeasure", "pk": 8, "fields": {"name": "Quart", "abbreviation": "qt", "unit_type": "volume"}}, {"model": "inventory.unitofmeasure", "pk": 9, "fields": {"name": "Cup", "abbreviation": "cup", "unit_type": "volume"}}, {"model": "inventory.unitofmeasure", "pk": 10, "fields": {"name": "Piece", "abbreviation": "pc", "unit_type": "count"}}, {"model": "inventory.unitofmeasure", "pk": 11, "fields": {"name": "Box", "abbreviation": "box", "unit_type": "count"}}, {"model": "inventory.unitofmeasure", "pk": 12, "fields": {"name": "Case", "abbreviation": "case", "unit_type": "count"}}, {"model": "inventory.unitofmeasure", "pk": 13, "fields": {"name": "<PERSON><PERSON>", "abbreviation": "doz", "unit_type": "count"}}, {"model": "inventory.unitofmeasure", "pk": 14, "fields": {"name": "Tablespoon", "abbreviation": "tbsp", "unit_type": "volume"}}, {"model": "inventory.unitofmeasure", "pk": 15, "fields": {"name": "Teaspoon", "abbreviation": "tsp", "unit_type": "volume"}}]
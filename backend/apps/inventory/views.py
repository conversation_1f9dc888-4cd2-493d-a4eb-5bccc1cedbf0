"""
Inventory views for Restaurant POS system.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, F
from django.utils import timezone
from decimal import Decimal

from .models import InventoryCategory, UnitOfMeasure, Supplier, InventoryItem, StockAdjustment, PurchaseOrder, PurchaseOrderItem, InventoryCount, InventoryCountItem
from .serializers import (
    InventoryCategorySerializer,
    UnitOfMeasureSerializer,
    SupplierSerializer,
    InventoryItemSerializer,
    InventoryItemListSerializer,
    StockAdjustmentSerializer,
    StockAdjustmentCreateSerializer,
    PurchaseOrderSerializer,
    PurchaseOrderListSerializer,
    PurchaseOrderItemSerializer,
    InventoryCountSerializer,
    InventoryCountItemSerializer,
    InventoryReportSerializer,
)
from apps.core.permissions import IsRestaurantStaff
from apps.core.middleware import get_current_restaurant


class UnitOfMeasureViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Unit of measure viewset (read-only).
    """

    queryset = UnitOfMeasure.objects.all()
    serializer_class = UnitOfMeasureSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "abbreviation"]
    ordering = ["name"]


class InventoryCategoryViewSet(viewsets.ModelViewSet):
    """
    Inventory category management viewset.
    """

    serializer_class = InventoryCategorySerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "description"]
    ordering_fields = ["sort_order", "name", "created_at"]
    ordering = ["sort_order", "name"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return InventoryCategory.objects.filter(restaurant=restaurant)
        return InventoryCategory.objects.none()


class SupplierViewSet(viewsets.ModelViewSet):
    """
    Supplier management viewset.
    """

    serializer_class = SupplierSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["name", "contact_person", "email"]
    ordering_fields = ["name", "rating", "created_at"]
    ordering = ["name"]
    filterset_fields = ["supplier_type", "is_active"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return Supplier.objects.filter(restaurant=restaurant)
        return Supplier.objects.none()

    @action(detail=True, methods=["post"])
    def toggle_active(self, request, pk=None):
        """Toggle supplier active status."""
        supplier = self.get_object()
        supplier.is_active = not supplier.is_active
        supplier.save()

        return Response({"status": f'Supplier {"activated" if supplier.is_active else "deactivated"} successfully'})


class InventoryItemViewSet(viewsets.ModelViewSet):
    """
    Inventory item management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["name", "description", "sku", "barcode"]
    ordering_fields = ["name", "current_stock", "unit_cost", "created_at"]
    ordering = ["category__sort_order", "name"]
    filterset_fields = ["category", "item_type", "is_active", "primary_supplier"]

    def get_serializer_class(self):
        if self.action == "list":
            return InventoryItemListSerializer
        return InventoryItemSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            queryset = InventoryItem.objects.filter(restaurant=restaurant).select_related("category", "unit_of_measure", "primary_supplier")

            # Filter by stock status
            stock_status = self.request.query_params.get("stock_status")
            if stock_status == "low_stock":
                queryset = queryset.filter(current_stock__lte=F("reorder_point"))
            elif stock_status == "out_of_stock":
                queryset = queryset.filter(current_stock__lte=0)
            elif stock_status == "in_stock":
                queryset = queryset.filter(current_stock__gt=F("reorder_point"))

            return queryset
        return InventoryItem.objects.none()

    @action(detail=True, methods=["post"])
    def adjust_stock(self, request, pk=None):
        """Adjust inventory item stock."""
        item = self.get_object()
        serializer = StockAdjustmentCreateSerializer(data=request.data, context={"request": request})

        if serializer.is_valid():
            # Override inventory_item_id with current item
            serializer.validated_data["inventory_item_id"] = item.id
            adjustment = serializer.save()

            return Response({"status": "Stock adjusted successfully", "adjustment": StockAdjustmentSerializer(adjustment).data})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def toggle_active(self, request, pk=None):
        """Toggle item active status."""
        item = self.get_object()
        item.is_active = not item.is_active
        item.save()

        return Response({"status": f'Item {"activated" if item.is_active else "deactivated"} successfully'})

    @action(detail=False, methods=["get"])
    def low_stock(self, request):
        """Get low stock items."""
        items = self.get_queryset().filter(current_stock__lte=F("reorder_point"), is_active=True)
        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def out_of_stock(self, request):
        """Get out of stock items."""
        items = self.get_queryset().filter(current_stock__lte=0, is_active=True)
        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def reorder_suggestions(self, request):
        """Get items that need reordering."""
        items = self.get_queryset().filter(current_stock__lte=F("reorder_point"), is_active=True, primary_supplier__isnull=False).select_related("primary_supplier")

        # Group by supplier
        suggestions = {}
        for item in items:
            supplier_name = item.primary_supplier.name
            if supplier_name not in suggestions:
                suggestions[supplier_name] = {"supplier": SupplierSerializer(item.primary_supplier).data, "items": []}

            suggested_quantity = max(item.maximum_stock - item.current_stock, item.reorder_point * 2)

            suggestions[supplier_name]["items"].append({"item": InventoryItemListSerializer(item).data, "suggested_quantity": suggested_quantity})

        return Response(list(suggestions.values()))


class StockAdjustmentViewSet(viewsets.ModelViewSet):
    """
    Stock adjustment management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.OrderingFilter, DjangoFilterBackend]
    ordering_fields = ["created_at"]
    ordering = ["-created_at"]
    filterset_fields = ["adjustment_type", "inventory_item"]

    def get_serializer_class(self):
        if self.action == "create":
            return StockAdjustmentCreateSerializer
        return StockAdjustmentSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return StockAdjustment.objects.filter(inventory_item__restaurant=restaurant).select_related("inventory_item", "adjusted_by")
        return StockAdjustment.objects.none()


class PurchaseOrderViewSet(viewsets.ModelViewSet):
    """
    Purchase order management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["po_number", "supplier__name"]
    ordering_fields = ["created_at", "order_date", "expected_delivery_date", "total_amount"]
    ordering = ["-created_at"]
    filterset_fields = ["status", "supplier"]

    def get_serializer_class(self):
        if self.action == "list":
            return PurchaseOrderListSerializer
        return PurchaseOrderSerializer

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return PurchaseOrder.objects.filter(restaurant=restaurant).select_related("supplier", "created_by")
        return PurchaseOrder.objects.none()

    @action(detail=True, methods=["post"])
    def send_to_supplier(self, request, pk=None):
        """Send PO to supplier."""
        po = self.get_object()
        if po.status == PurchaseOrder.Status.DRAFT:
            po.status = PurchaseOrder.Status.SENT
            po.save()

            # Here you would integrate with email/fax system to send PO

            return Response({"status": "Purchase order sent to supplier successfully"})

        return Response({"error": "Purchase order must be in draft status to send"}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def receive_items(self, request, pk=None):
        """Receive items from purchase order."""
        po = self.get_object()
        received_items = request.data.get("items", [])

        for item_data in received_items:
            try:
                po_item = po.items.get(id=item_data["id"])
                quantity_received = Decimal(str(item_data["quantity_received"]))

                # Update PO item
                po_item.quantity_received += quantity_received
                if "expiry_date" in item_data:
                    po_item.expiry_date = item_data["expiry_date"]
                if "batch_number" in item_data:
                    po_item.batch_number = item_data["batch_number"]
                po_item.save()

                # Update inventory stock
                po_item.inventory_item.current_stock += quantity_received
                po_item.inventory_item.last_purchase_price = po_item.unit_cost
                po_item.inventory_item.save()

                # Create stock adjustment record
                StockAdjustment.objects.create(
                    inventory_item=po_item.inventory_item,
                    adjustment_type=StockAdjustment.AdjustmentType.PURCHASE,
                    quantity_change=quantity_received,
                    old_quantity=po_item.inventory_item.current_stock - quantity_received,
                    new_quantity=po_item.inventory_item.current_stock,
                    unit_cost=po_item.unit_cost,
                    reason=f"Received from PO {po.po_number}",
                    reference_id=po.po_number,
                    adjusted_by=request.user,
                )

            except (PurchaseOrderItem.DoesNotExist, ValueError, KeyError) as e:
                continue

        # Update PO status
        all_items_received = all(item.is_fully_received for item in po.items.all())
        any_items_received = any(item.quantity_received > 0 for item in po.items.all())

        if all_items_received:
            po.status = PurchaseOrder.Status.RECEIVED
            po.actual_delivery_date = timezone.now().date()
        elif any_items_received:
            po.status = PurchaseOrder.Status.PARTIALLY_RECEIVED

        po.save()

        return Response({"status": "Items received successfully"})


class InventoryCountViewSet(viewsets.ModelViewSet):
    """
    Inventory count management viewset.
    """

    serializer_class = InventoryCountSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name"]
    ordering_fields = ["count_date", "created_at"]
    ordering = ["-count_date"]

    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return InventoryCount.objects.filter(restaurant=restaurant)
        return InventoryCount.objects.none()

    @action(detail=True, methods=["post"])
    def start_count(self, request, pk=None):
        """Start inventory count session."""
        count = self.get_object()
        if count.status == InventoryCount.Status.PLANNED:
            count.status = InventoryCount.Status.IN_PROGRESS
            count.counted_by = request.user
            count.save()

            # Create count items for all active inventory items
            restaurant = get_current_restaurant()
            inventory_items = InventoryItem.objects.filter(restaurant=restaurant, is_active=True)

            count_items = []
            for item in inventory_items:
                count_items.append(InventoryCountItem(inventory_count=count, inventory_item=item, system_quantity=item.current_stock, counted_quantity=0))

            InventoryCountItem.objects.bulk_create(count_items)

            return Response({"status": "Inventory count started successfully"})

        return Response({"error": "Inventory count must be in planned status to start"}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def complete_count(self, request, pk=None):
        """Complete inventory count and apply adjustments."""
        count = self.get_object()
        if count.status == InventoryCount.Status.IN_PROGRESS:
            count.status = InventoryCount.Status.COMPLETED
            count.save()

            # Apply stock adjustments for items with variances
            for count_item in count.count_items.all():
                if count_item.variance != 0:
                    # Update inventory stock
                    count_item.inventory_item.current_stock = count_item.counted_quantity
                    count_item.inventory_item.save()

                    # Create stock adjustment record
                    StockAdjustment.objects.create(
                        inventory_item=count_item.inventory_item,
                        adjustment_type=StockAdjustment.AdjustmentType.INVENTORY_COUNT,
                        quantity_change=count_item.variance,
                        old_quantity=count_item.system_quantity,
                        new_quantity=count_item.counted_quantity,
                        unit_cost=count_item.inventory_item.unit_cost,
                        reason=f"Inventory count adjustment - {count.name}",
                        reference_id=str(count.id),
                        adjusted_by=request.user,
                    )

            return Response({"status": "Inventory count completed successfully"})

        return Response({"error": "Inventory count must be in progress to complete"}, status=status.HTTP_400_BAD_REQUEST)


@action(detail=False, methods=["get"])
def inventory_report(request):
    """
    Generate comprehensive inventory report.
    """
    restaurant = get_current_restaurant()
    if not restaurant:
        return Response({"error": "Restaurant not found"}, status=status.HTTP_400_BAD_REQUEST)

    # Get all active inventory items
    items = InventoryItem.objects.filter(restaurant=restaurant, is_active=True)

    # Calculate totals
    total_items = items.count()
    total_value = sum(item.stock_value for item in items)
    low_stock_items = items.filter(current_stock__lte=F("reorder_point")).count()
    out_of_stock_items = items.filter(current_stock__lte=0).count()

    # Categories breakdown
    categories_breakdown = []
    categories = InventoryCategory.objects.filter(restaurant=restaurant)
    for category in categories:
        category_items = items.filter(category=category)
        categories_breakdown.append({"category": category.name, "items_count": category_items.count(), "total_value": sum(item.stock_value for item in category_items)})

    # Top value items
    top_value_items = []
    for item in items.order_by("-current_stock")[:10]:
        top_value_items.append({"name": item.name, "current_stock": item.current_stock, "unit_cost": item.unit_cost, "stock_value": item.stock_value})

    # Recent adjustments
    recent_adjustments = []
    adjustments = StockAdjustment.objects.filter(inventory_item__restaurant=restaurant).order_by("-created_at")[:10]

    for adjustment in adjustments:
        recent_adjustments.append(
            {
                "item_name": adjustment.inventory_item.name,
                "adjustment_type": adjustment.adjustment_type,
                "quantity_change": adjustment.quantity_change,
                "created_at": adjustment.created_at,
            }
        )

    report_data = {
        "total_items": total_items,
        "total_value": total_value,
        "low_stock_items": low_stock_items,
        "out_of_stock_items": out_of_stock_items,
        "categories_breakdown": categories_breakdown,
        "top_value_items": top_value_items,
        "recent_adjustments": recent_adjustments,
    }

    serializer = InventoryReportSerializer(report_data)
    return Response(serializer.data)

"""
Authentication views for Restaurant POS system.
"""

import uuid
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .models import PasswordResetToken, EmailVerificationToken, LoginAttempt
from .serializers import UserRegistrationSerializer, LoginSerializer, UserSerializer, PasswordChangeSerializer, PasswordResetRequestSerializer, PasswordResetConfirmSerializer
from apps.users.models import UserSession

User = get_user_model()


class RegisterView(generics.CreateAPIView):
    """
    User registration endpoint.
    """

    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(summary="Register a new user", description="Create a new user account with email verification", responses={201: UserSerializer})
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Create email verification token
        token = str(uuid.uuid4())
        EmailVerificationToken.objects.create(user=user, token=token, expires_at=timezone.now() + timedelta(hours=24))

        # TODO: Send verification email

        return Response({"user": UserSerializer(user).data, "message": "Registration successful. Please check your email for verification."}, status=status.HTTP_201_CREATED)


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom JWT token obtain view with additional user data.
    """

    serializer_class = LoginSerializer

    @extend_schema(
        summary="Login user",
        description="Authenticate user and return JWT tokens",
        responses={200: {"type": "object", "properties": {"access": {"type": "string"}, "refresh": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}},
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        # Track login attempt
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        try:
            serializer.is_valid(raise_exception=True)
            user = serializer.validated_data["user"]

            # Create tokens
            refresh = RefreshToken.for_user(user)
            access = refresh.access_token

            # Create user session
            session = UserSession.objects.create(
                user=user,
                session_key=str(refresh),
                ip_address=ip_address,
                user_agent=user_agent,
                terminal_id=request.data.get("terminal_id", ""),
                location=request.data.get("location", ""),
            )

            # Log successful login
            LoginAttempt.objects.create(email=user.email, ip_address=ip_address, user_agent=user_agent, success=True)

            return Response({"access": str(access), "refresh": str(refresh), "user": UserSerializer(user).data, "session_id": str(session.id)})

        except Exception as e:
            # Log failed login
            email = request.data.get("email", "")
            LoginAttempt.objects.create(email=email, ip_address=ip_address, user_agent=user_agent, success=False, failure_reason=str(e))
            raise

    def get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class LogoutView(APIView):
    """
    Logout endpoint to invalidate tokens and end session.
    """

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(summary="Logout user", description="Invalidate JWT tokens and end user session")
    def post(self, request):
        try:
            refresh_token = request.data.get("refresh")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            # End user session
            session_id = request.data.get("session_id")
            if session_id:
                try:
                    session = UserSession.objects.get(id=session_id, user=request.user)
                    session.logout_time = timezone.now()
                    session.is_active = False
                    session.save()
                except UserSession.DoesNotExist:
                    pass

            return Response({"message": "Successfully logged out"})
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ProfileView(generics.RetrieveUpdateAPIView):
    """
    User profile view.
    """

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

    @extend_schema(summary="Get user profile", description="Retrieve current user's profile information")
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(summary="Update user profile", description="Update current user's profile information")
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class PasswordChangeView(APIView):
    """
    Password change endpoint.
    """

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(summary="Change password", description="Change user's password", request=PasswordChangeSerializer)
    def post(self, request):
        serializer = PasswordChangeSerializer(data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)

        user = request.user
        user.set_password(serializer.validated_data["new_password"])
        user.save()

        return Response({"message": "Password changed successfully"})


class PasswordResetRequestView(APIView):
    """
    Password reset request endpoint.
    """

    permission_classes = [permissions.AllowAny]

    @extend_schema(summary="Request password reset", description="Send password reset email to user", request=PasswordResetRequestSerializer)
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"]
        user = User.objects.get(email=email)

        # Create reset token
        token = str(uuid.uuid4())
        PasswordResetToken.objects.create(user=user, token=token, expires_at=timezone.now() + timedelta(hours=1))

        # TODO: Send reset email

        return Response({"message": "Password reset email sent"})


class PasswordResetConfirmView(APIView):
    """
    Password reset confirmation endpoint.
    """

    permission_classes = [permissions.AllowAny]

    @extend_schema(summary="Confirm password reset", description="Reset password using token from email", request=PasswordResetConfirmSerializer)
    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token = serializer.validated_data["token"]
        new_password = serializer.validated_data["new_password"]

        try:
            reset_token = PasswordResetToken.objects.get(token=token, is_used=False, expires_at__gt=timezone.now())

            user = reset_token.user
            user.set_password(new_password)
            user.save()

            reset_token.is_used = True
            reset_token.save()

            return Response({"message": "Password reset successful"})

        except PasswordResetToken.DoesNotExist:
            return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
@permission_classes([permissions.AllowAny])
def verify_email(request):
    """
    Email verification endpoint.
    """
    token = request.data.get("token")

    if not token:
        return Response({"error": "Token is required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        verification_token = EmailVerificationToken.objects.get(token=token, is_used=False, expires_at__gt=timezone.now())

        user = verification_token.user
        user.is_verified = True
        user.save()

        verification_token.is_used = True
        verification_token.save()

        return Response({"message": "Email verified successfully"})

    except EmailVerificationToken.DoesNotExist:
        return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

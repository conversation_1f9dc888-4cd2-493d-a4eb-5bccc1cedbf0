"""
Custom permissions for Restaurant POS system.
"""

from rest_framework import permissions
from django.contrib.auth import get_user_model
from apps.core.middleware import get_current_restaurant

User = get_user_model()


class IsSuperAdmin(permissions.BasePermission):
    """
    Permission for Super Admin users only.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type == User.UserType.SUPER_ADMIN
        )


class IsRestaurantOwner(permissions.BasePermission):
    """
    Permission for Restaurant Owner users.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type == User.UserType.RESTAURANT_OWNER
        )


class IsManager(permissions.BasePermission):
    """
    Permission for Manager users.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type in [
                User.UserType.RESTAURANT_OWNER,
                User.UserType.MANAGER
            ]
        )


class IsStaff(permissions.BasePermission):
    """
    Permission for all staff members (excluding customers).
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type in [
                User.UserType.SUPER_ADMIN,
                User.UserType.RESTAURANT_OWNER,
                User.UserType.MANAGER,
                User.UserType.CASHIER,
                User.UserType.WAITER,
                User.UserType.KITCHEN_STAFF
            ]
        )


class IsCashier(permissions.BasePermission):
    """
    Permission for Cashier users.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type in [
                User.UserType.SUPER_ADMIN,
                User.UserType.RESTAURANT_OWNER,
                User.UserType.MANAGER,
                User.UserType.CASHIER
            ]
        )


class IsWaiter(permissions.BasePermission):
    """
    Permission for Waiter users.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type in [
                User.UserType.SUPER_ADMIN,
                User.UserType.RESTAURANT_OWNER,
                User.UserType.MANAGER,
                User.UserType.WAITER
            ]
        )


class IsKitchenStaff(permissions.BasePermission):
    """
    Permission for Kitchen Staff users.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type in [
                User.UserType.SUPER_ADMIN,
                User.UserType.RESTAURANT_OWNER,
                User.UserType.MANAGER,
                User.UserType.KITCHEN_STAFF
            ]
        )


class HasRestaurantAccess(permissions.BasePermission):
    """
    Permission to check if user has access to the current restaurant.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Super admin has access to all restaurants
        if request.user.user_type == User.UserType.SUPER_ADMIN:
            return True
        
        # Get current restaurant from middleware
        current_restaurant = get_current_restaurant()
        if not current_restaurant:
            return False
        
        return request.user.has_restaurant_access(current_restaurant)


class CanProcessRefunds(permissions.BasePermission):
    """
    Permission for users who can process refunds.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Super admin and owners can always process refunds
        if request.user.user_type in [User.UserType.SUPER_ADMIN, User.UserType.RESTAURANT_OWNER]:
            return True
        
        # Check staff profile permissions
        if hasattr(request.user, 'staff_profile'):
            return request.user.staff_profile.can_process_refunds
        
        return False


class CanModifyPrices(permissions.BasePermission):
    """
    Permission for users who can modify prices.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Super admin, owners, and managers can modify prices
        if request.user.user_type in [
            User.UserType.SUPER_ADMIN, 
            User.UserType.RESTAURANT_OWNER,
            User.UserType.MANAGER
        ]:
            return True
        
        # Check staff profile permissions
        if hasattr(request.user, 'staff_profile'):
            return request.user.staff_profile.can_modify_prices
        
        return False


class CanAccessReports(permissions.BasePermission):
    """
    Permission for users who can access reports.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Super admin, owners, and managers can access reports
        if request.user.user_type in [
            User.UserType.SUPER_ADMIN, 
            User.UserType.RESTAURANT_OWNER,
            User.UserType.MANAGER
        ]:
            return True
        
        # Check staff profile permissions
        if hasattr(request.user, 'staff_profile'):
            return request.user.staff_profile.can_access_reports
        
        return False


class CanManageInventory(permissions.BasePermission):
    """
    Permission for users who can manage inventory.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Super admin, owners, and managers can manage inventory
        if request.user.user_type in [
            User.UserType.SUPER_ADMIN, 
            User.UserType.RESTAURANT_OWNER,
            User.UserType.MANAGER
        ]:
            return True
        
        # Check staff profile permissions
        if hasattr(request.user, 'staff_profile'):
            return request.user.staff_profile.can_manage_inventory
        
        return False

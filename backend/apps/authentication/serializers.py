"""
Authentication serializers for Restaurant POS system.
"""

from rest_framework import serializers
from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from apps.users.models import Staff<PERSON>rofile, CustomerProfile

User = get_user_model()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration.
    """

    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ["email", "username", "first_name", "last_name", "phone", "user_type", "password", "password_confirm"]
        extra_kwargs = {
            "email": {"required": True},
            "first_name": {"required": True},
            "last_name": {"required": True},
        }

    def validate(self, attrs):
        if attrs["password"] != attrs["password_confirm"]:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs

    def create(self, validated_data):
        validated_data.pop("password_confirm")
        password = validated_data.pop("password")

        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()

        # Create profile based on user type
        if user.user_type == User.UserType.CUSTOMER:
            CustomerProfile.objects.create(user=user)

        return user


class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login.
    """

    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    # restaurant_id = serializers.UUIDField(required=False)

    def validate(self, attrs):
        email = attrs.get("email")
        password = attrs.get("password")
        # restaurant_id = attrs.get('restaurant_id')

        if email and password:
            user = authenticate(request=self.context.get("request"), username=email, password=password)

            if not user:
                raise serializers.ValidationError("Invalid credentials.")

            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")

            # For restaurant staff, validate restaurant access
            # if restaurant_id and user.user_type != User.UserType.SUPER_ADMIN:
            #     if not user.has_restaurant_access(restaurant_id):
            #         raise serializers.ValidationError('No access to this restaurant.')

            attrs["user"] = user
            return attrs
        else:
            raise serializers.ValidationError("Must include email and password.")


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for user details.
    """

    full_name = serializers.ReadOnlyField()

    class Meta:
        model = User
        fields = ["id", "email", "username", "first_name", "last_name", "full_name", "phone", "user_type", "is_verified", "restaurant", "created_at", "updated_at"]
        read_only_fields = ["id", "created_at", "updated_at", "is_verified"]


class StaffProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for staff profile.
    """

    user = UserSerializer(read_only=True)

    class Meta:
        model = StaffProfile
        fields = [
            "id",
            "user",
            "employee_id",
            "position",
            "department",
            "hire_date",
            "salary",
            "hourly_rate",
            "preferred_shift",
            "can_process_refunds",
            "can_modify_prices",
            "can_access_reports",
            "can_manage_inventory",
            "emergency_contact_name",
            "emergency_contact_phone",
            "restaurant",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class CustomerProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for customer profile.
    """

    user = UserSerializer(read_only=True)

    class Meta:
        model = CustomerProfile
        fields = [
            "id",
            "user",
            "date_of_birth",
            "loyalty_points",
            "total_orders",
            "total_spent",
            "dietary_restrictions",
            "favorite_items",
            "email_notifications",
            "sms_notifications",
            "promotional_emails",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "loyalty_points", "total_orders", "total_spent", "created_at", "updated_at"]


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change.
    """

    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect.")
        return value


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request.
    """

    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this email does not exist.")
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for password reset confirmation.
    """

    token = serializers.CharField()
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs

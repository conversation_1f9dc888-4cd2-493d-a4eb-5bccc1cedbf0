"""
Middleware for multi-tenant support in Restaurant POS system.
"""

import threading
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils.deprecation import MiddlewareMixin


# Thread-local storage for current restaurant
_thread_locals = threading.local()


class TenantMiddleware(MiddlewareMixin):
    """
    Middleware to handle multi-tenant restaurant isolation.
    Sets the current restaurant based on the request.
    """

    def process_view(self, request, view_func, view_args, view_kwargs):
        """
        Process the view to set the current restaurant.
        This runs after authentication middleware but before the view.
        """
        # Skip tenant resolution for admin, docs, and health endpoints
        if any(request.path.startswith(path) for path in ["/admin/", "/api/docs/", "/api/schema/", "/health/"]):
            return None

        # Skip for authentication endpoints
        if request.path.startswith("/api/auth/"):
            return None

        # Get restaurant from various sources
        restaurant = self._get_restaurant_from_request(request)

        if restaurant:
            set_current_restaurant(restaurant)
            request.restaurant = restaurant
        else:
            # For API endpoints that require a restaurant, set a default restaurant
            if request.path.startswith("/api/"):
                # Get the first restaurant as default (for single-tenant setup)
                from apps.restaurants.models import Restaurant

                try:
                    default_restaurant = Restaurant.objects.first()
                    if default_restaurant:
                        set_current_restaurant(default_restaurant)
                        request.restaurant = default_restaurant
                except Restaurant.DoesNotExist:
                    pass

        return None

    def _get_restaurant_from_request(self, request):
        """
        Get restaurant from request headers, subdomain, or user context.
        """
        from apps.restaurants.models import Restaurant

        # Method 1: From X-Restaurant-ID header
        restaurant_id = request.META.get("HTTP_X_RESTAURANT_ID")
        if restaurant_id:
            try:
                return Restaurant.objects.get(id=restaurant_id, is_active=True)
            except (Restaurant.DoesNotExist, ValueError):
                pass

        # Method 2: From subdomain
        host = request.get_host()
        if "." in host:
            subdomain = host.split(".")[0]
            try:
                return Restaurant.objects.get(subdomain=subdomain, is_active=True)
            except Restaurant.DoesNotExist:
                pass

        # Method 3: From authenticated user's restaurant
        if hasattr(request, "user") and request.user.is_authenticated:
            # The user is already the custom User model, so we can directly access restaurant
            if hasattr(request.user, "restaurant") and request.user.restaurant:
                return request.user.restaurant

        return None

    def process_response(self, request, response):
        """
        Clean up thread-local storage after request.
        """
        clear_current_restaurant()
        return response


def get_current_restaurant():
    """
    Get the current restaurant from thread-local storage.
    """
    return getattr(_thread_locals, "restaurant", None)


def set_current_restaurant(restaurant):
    """
    Set the current restaurant in thread-local storage.
    """
    _thread_locals.restaurant = restaurant


def clear_current_restaurant():
    """
    Clear the current restaurant from thread-local storage.
    """
    if hasattr(_thread_locals, "restaurant"):
        delattr(_thread_locals, "restaurant")


class TenantQuerySetMixin:
    """
    Mixin for QuerySets to automatically filter by current restaurant.
    """

    def get_queryset(self):
        queryset = super().get_queryset()
        restaurant = get_current_restaurant()

        if restaurant and hasattr(self.model, "restaurant"):
            queryset = queryset.filter(restaurant=restaurant)

        return queryset


class TenantModelMixin:
    """
    Mixin for models to automatically set restaurant on save.
    """

    def save(self, *args, **kwargs):
        if hasattr(self, "restaurant") and not self.restaurant_id:
            current_restaurant = get_current_restaurant()
            if current_restaurant:
                self.restaurant = current_restaurant

        super().save(*args, **kwargs)

"""
WebSocket routing for Restaurant POS system.
"""

from django.urls import path
from channels.routing import URLRouter

# Import consumers from different apps
# from apps.orders.consumers import OrderConsumer
# from apps.notifications.consumers import NotificationConsumer

websocket_urlpatterns = [
    # Order updates (for kitchen display, POS terminals)
    # path('ws/orders/<uuid:restaurant_id>/', OrderConsumer.as_asgi()),
    
    # Real-time notifications
    # path('ws/notifications/<uuid:restaurant_id>/', NotificationConsumer.as_asgi()),
    
    # Kitchen Display System
    # path('ws/kitchen/<uuid:restaurant_id>/', KitchenConsumer.as_asgi()),
]

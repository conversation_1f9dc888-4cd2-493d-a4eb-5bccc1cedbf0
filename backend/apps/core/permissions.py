"""
Custom permissions for Restaurant POS system.
"""

from rest_framework import permissions
from django.contrib.auth.models import User


class IsSuperAdmin(permissions.BasePermission):
    """
    Permission to check if user is a superuser/admin.
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_superuser


class IsRestaurantOwnerOrManager(permissions.BasePermission):
    """
    Permission to check if user is restaurant owner or manager.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Allow superusers
        if request.user.is_superuser:
            return True
        
        # Check if user has restaurant owner or manager role
        # This would typically check user's role in relation to a restaurant
        # For now, we'll allow authenticated users (can be refined later)
        return True
    
    def has_object_permission(self, request, view, obj):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Allow superusers
        if request.user.is_superuser:
            return True
        
        # Check if user owns or manages this specific restaurant
        # This would typically check the relationship between user and restaurant
        # For now, we'll allow authenticated users (can be refined later)
        return True


class IsRestaurantStaff(permissions.BasePermission):
    """
    Permission to check if user is restaurant staff member.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Allow superusers
        if request.user.is_superuser:
            return True
        
        # Check if user is staff member of any restaurant
        # This would typically check user's role/employment status
        # For now, we'll allow authenticated users (can be refined later)
        return True


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed for any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions are only allowed to the owner of the object.
        return obj.created_by == request.user if hasattr(obj, 'created_by') else True

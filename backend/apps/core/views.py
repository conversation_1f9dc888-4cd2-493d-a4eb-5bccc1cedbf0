"""
Core views for Restaurant POS system.
"""

import os
from django.http import JsonResponse
from django.views import View
from django.conf import settings
from django.db import connection
from django.core.cache import cache
import redis


class HealthCheckView(View):
    """
    Simple health check endpoint.
    """

    def get(self, request):
        return JsonResponse({"status": "healthy", "service": "Restaurant POS API", "version": "1.0.0"})


class SystemStatusView(View):
    """
    Detailed system status check.
    """

    def get(self, request):
        status = {"status": "healthy", "checks": {}}

        # Database check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            status["checks"]["database"] = "healthy"
        except Exception as e:
            status["checks"]["database"] = f"unhealthy: {str(e)}"
            status["status"] = "unhealthy"

        # Redis check
        try:
            cache.set("health_check", "ok", 10)
            cache.get("health_check")
            status["checks"]["redis"] = "healthy"
        except Exception as e:
            status["checks"]["redis"] = f"unhealthy: {str(e)}"
            status["status"] = "unhealthy"

        # MongoDB check (if configured)
        try:
            from pymongo import MongoClient
            from django.conf import settings

            if hasattr(settings, "MONGODB_SETTINGS"):
                client = MongoClient(settings.MONGODB_SETTINGS["host"])
                client.admin.command("ping")
                status["checks"]["mongodb"] = "healthy"
        except Exception as e:
            status["checks"]["mongodb"] = f"unhealthy: {str(e)}"
            status["status"] = "unhealthy"

        return JsonResponse(status)

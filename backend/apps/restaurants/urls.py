"""
Restaurant URLs for multi-tenant POS system.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import RestaurantViewSet, RestaurantSettingsViewSet, TableViewSet, RestaurantDomainViewSet

router = DefaultRouter()
router.register(r"restaurants", RestaurantViewSet)
router.register(r"settings", RestaurantSettingsViewSet, basename="restaurant-settings")
router.register(r"tables", TableViewSet, basename="tables")
router.register(r"domains", RestaurantDomainViewSet, basename="restaurant-domains")

app_name = "restaurants"
urlpatterns = [
    path("", include(router.urls)),
]

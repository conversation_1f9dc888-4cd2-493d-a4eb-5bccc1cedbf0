"""
Management command to generate QR codes for restaurant tables.
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from apps.restaurants.models import Restaurant, Table
from apps.restaurants.utils import generate_table_qr_code


class Command(BaseCommand):
    help = 'Generate QR codes for restaurant tables'

    def add_arguments(self, parser):
        parser.add_argument(
            '--restaurant',
            type=str,
            help='Restaurant slug to generate QR codes for (optional, generates for all if not specified)',
        )
        parser.add_argument(
            '--base-url',
            type=str,
            default='http://localhost:3000',
            help='Base URL for customer interface (default: http://localhost:3000)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regenerate QR codes even if they already exist',
        )

    def handle(self, *args, **options):
        restaurant_slug = options.get('restaurant')
        base_url = options.get('base_url')
        force = options.get('force')

        # Get restaurants to process
        if restaurant_slug:
            try:
                restaurants = [Restaurant.objects.get(slug=restaurant_slug)]
            except Restaurant.DoesNotExist:
                raise CommandError(f'Restaurant with slug "{restaurant_slug}" does not exist.')
        else:
            restaurants = Restaurant.objects.filter(status='active')

        total_generated = 0

        for restaurant in restaurants:
            self.stdout.write(f'Processing restaurant: {restaurant.name}')
            
            tables = restaurant.tables.all()
            restaurant_generated = 0

            for table in tables:
                # Skip if QR code exists and not forcing regeneration
                if table.qr_code and not force:
                    self.stdout.write(
                        self.style.WARNING(
                            f'  Skipping {table.display_name} - QR code already exists (use --force to regenerate)'
                        )
                    )
                    continue

                try:
                    # Generate QR code
                    qr_file = generate_table_qr_code(table, base_url)
                    table.qr_code.save(qr_file.name, qr_file, save=True)
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'  Generated QR code for {table.display_name} -> {table.qr_code_url}'
                        )
                    )
                    restaurant_generated += 1
                    total_generated += 1

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f'  Failed to generate QR code for {table.display_name}: {str(e)}'
                        )
                    )

            self.stdout.write(f'Generated {restaurant_generated} QR codes for {restaurant.name}\n')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully generated {total_generated} QR codes total.'
            )
        )

"""
Restaurant serializers for multi-tenant POS system.
"""

from rest_framework import serializers
from django.utils.text import slugify
from .models import Restaurant, RestaurantSettings, Table, RestaurantDomain


class RestaurantSerializer(serializers.ModelSerializer):
    """
    Restaurant serializer for CRUD operations.
    """
    full_address = serializers.ReadOnlyField()
    is_subscription_active = serializers.ReadOnlyField()
    
    class Meta:
        model = Restaurant
        fields = [
            'id', 'name', 'slug', 'subdomain', 'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country',
            'cuisine_type', 'description', 'logo', 'subscription_plan', 'status',
            'trial_ends_at', 'subscription_starts_at', 'subscription_ends_at',
            'timezone', 'currency', 'tax_rate', 'service_charge_rate',
            'max_users', 'max_tables', 'max_menu_items',
            'analytics_enabled', 'inventory_enabled', 'loyalty_program_enabled',
            'full_address', 'is_subscription_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'slug', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Auto-generate slug from name
        if not validated_data.get('slug'):
            validated_data['slug'] = slugify(validated_data['name'])
        
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Update slug if name changes
        if 'name' in validated_data and validated_data['name'] != instance.name:
            validated_data['slug'] = slugify(validated_data['name'])
        
        return super().update(instance, validated_data)


class RestaurantCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for restaurant creation with owner assignment.
    """
    owner_email = serializers.EmailField(write_only=True)
    owner_first_name = serializers.CharField(max_length=30, write_only=True)
    owner_last_name = serializers.CharField(max_length=30, write_only=True)
    owner_password = serializers.CharField(min_length=8, write_only=True)
    
    class Meta:
        model = Restaurant
        fields = [
            'name', 'subdomain', 'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country',
            'cuisine_type', 'description', 'subscription_plan',
            'timezone', 'currency', 'tax_rate', 'service_charge_rate',
            'owner_email', 'owner_first_name', 'owner_last_name', 'owner_password'
        ]

    def create(self, validated_data):
        from django.contrib.auth import get_user_model
        from apps.users.models import StaffProfile
        
        User = get_user_model()
        
        # Extract owner data
        owner_data = {
            'email': validated_data.pop('owner_email'),
            'first_name': validated_data.pop('owner_first_name'),
            'last_name': validated_data.pop('owner_last_name'),
            'password': validated_data.pop('owner_password'),
        }
        
        # Generate username from email
        owner_data['username'] = owner_data['email'].split('@')[0]
        owner_data['user_type'] = 'restaurant_owner'
        
        # Create restaurant
        validated_data['slug'] = slugify(validated_data['name'])
        restaurant = super().create(validated_data)
        
        # Create owner user
        owner = User.objects.create_user(**owner_data)
        
        # Create staff profile for owner
        StaffProfile.objects.create(
            user=owner,
            restaurant=restaurant,
            role='owner',
            permissions={
                'can_manage_staff': True,
                'can_manage_menu': True,
                'can_manage_inventory': True,
                'can_view_reports': True,
                'can_manage_settings': True,
                'can_process_payments': True,
                'can_manage_tables': True,
                'can_void_orders': True,
            }
        )
        
        # Create default restaurant settings
        RestaurantSettings.objects.create(
            restaurant=restaurant,
            tip_suggestions=[15, 18, 20, 25],
            operating_hours={
                'monday': {'open': '09:00', 'close': '22:00', 'closed': False},
                'tuesday': {'open': '09:00', 'close': '22:00', 'closed': False},
                'wednesday': {'open': '09:00', 'close': '22:00', 'closed': False},
                'thursday': {'open': '09:00', 'close': '22:00', 'closed': False},
                'friday': {'open': '09:00', 'close': '23:00', 'closed': False},
                'saturday': {'open': '09:00', 'close': '23:00', 'closed': False},
                'sunday': {'open': '10:00', 'close': '21:00', 'closed': False},
            }
        )
        
        return restaurant


class RestaurantSettingsSerializer(serializers.ModelSerializer):
    """
    Restaurant settings serializer.
    """
    class Meta:
        model = RestaurantSettings
        fields = [
            'id', 'auto_print_receipts', 'receipt_footer_text', 'order_number_prefix',
            'table_number_prefix', 'kitchen_display_enabled', 'order_ready_sound',
            'auto_complete_orders', 'email_notifications', 'sms_notifications',
            'low_stock_alerts', 'daily_report_emails', 'accept_cash', 'accept_cards',
            'accept_digital_wallets', 'tip_suggestions', 'operating_hours',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TableSerializer(serializers.ModelSerializer):
    """
    Table serializer for restaurant table management.
    """
    display_name = serializers.ReadOnlyField()
    
    class Meta:
        model = Table
        fields = [
            'id', 'number', 'name', 'capacity', 'status', 'section',
            'x_position', 'y_position', 'qr_code', 'display_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Get restaurant from context (set by tenant middleware)
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class RestaurantDomainSerializer(serializers.ModelSerializer):
    """
    Restaurant domain serializer.
    """
    class Meta:
        model = RestaurantDomain
        fields = [
            'id', 'domain', 'is_primary', 'is_verified', 'ssl_enabled',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_verified', 'ssl_enabled', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Get restaurant from context
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class RestaurantStatsSerializer(serializers.Serializer):
    """
    Restaurant statistics serializer.
    """
    total_orders_today = serializers.IntegerField()
    total_revenue_today = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_orders_month = serializers.IntegerField()
    total_revenue_month = serializers.DecimalField(max_digits=10, decimal_places=2)
    active_tables = serializers.IntegerField()
    total_tables = serializers.IntegerField()
    total_menu_items = serializers.IntegerField()
    total_staff = serializers.IntegerField()
    subscription_status = serializers.CharField()
    subscription_expires = serializers.DateTimeField(allow_null=True)

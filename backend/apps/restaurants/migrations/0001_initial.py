# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Restaurant",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=200)),
                ("slug", models.SlugField(unique=True)),
                (
                    "subdomain",
                    models.CharField(
                        max_length=50,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                "^[a-zA-Z0-9-]+$",
                                "Only alphanumeric characters and hyphens allowed.",
                            )
                        ],
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("phone", models.CharField(max_length=20)),
                ("website", models.URLField(blank=True)),
                ("address_line_1", models.CharField(max_length=200)),
                ("address_line_2", models.CharField(blank=True, max_length=200)),
                ("city", models.CharField(max_length=100)),
                ("state", models.CharField(max_length=100)),
                ("postal_code", models.CharField(max_length=20)),
                ("country", models.CharField(default="United States", max_length=100)),
                ("cuisine_type", models.CharField(blank=True, max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="restaurant_logos/"
                    ),
                ),
                (
                    "subscription_plan",
                    models.CharField(
                        choices=[
                            ("basic", "Basic"),
                            ("standard", "Standard"),
                            ("premium", "Premium"),
                            ("enterprise", "Enterprise"),
                        ],
                        default="basic",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                            ("trial", "Trial"),
                        ],
                        default="trial",
                        max_length=20,
                    ),
                ),
                ("trial_ends_at", models.DateTimeField(blank=True, null=True)),
                ("subscription_starts_at", models.DateTimeField(blank=True, null=True)),
                ("subscription_ends_at", models.DateTimeField(blank=True, null=True)),
                ("timezone", models.CharField(default="UTC", max_length=50)),
                ("currency", models.CharField(default="USD", max_length=3)),
                (
                    "tax_rate",
                    models.DecimalField(decimal_places=4, default=0.0875, max_digits=5),
                ),
                (
                    "service_charge_rate",
                    models.DecimalField(decimal_places=4, default=0.0, max_digits=5),
                ),
                ("max_users", models.PositiveIntegerField(default=5)),
                ("max_tables", models.PositiveIntegerField(default=20)),
                ("max_menu_items", models.PositiveIntegerField(default=100)),
                ("analytics_enabled", models.BooleanField(default=False)),
                ("inventory_enabled", models.BooleanField(default=False)),
                ("loyalty_program_enabled", models.BooleanField(default=False)),
            ],
            options={
                "verbose_name": "Restaurant",
                "verbose_name_plural": "Restaurants",
                "db_table": "restaurants",
            },
        ),
        migrations.CreateModel(
            name="RestaurantDomain",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("domain", models.CharField(max_length=255, unique=True)),
                ("is_primary", models.BooleanField(default=False)),
                ("is_verified", models.BooleanField(default=False)),
                ("ssl_enabled", models.BooleanField(default=False)),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="domains",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Restaurant Domain",
                "verbose_name_plural": "Restaurant Domains",
                "db_table": "restaurant_domains",
            },
        ),
        migrations.CreateModel(
            name="RestaurantSettings",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("auto_print_receipts", models.BooleanField(default=True)),
                ("receipt_footer_text", models.TextField(blank=True)),
                ("order_number_prefix", models.CharField(default="ORD", max_length=10)),
                ("table_number_prefix", models.CharField(default="T", max_length=10)),
                ("kitchen_display_enabled", models.BooleanField(default=True)),
                ("order_ready_sound", models.BooleanField(default=True)),
                ("auto_complete_orders", models.BooleanField(default=False)),
                ("email_notifications", models.BooleanField(default=True)),
                ("sms_notifications", models.BooleanField(default=False)),
                ("low_stock_alerts", models.BooleanField(default=True)),
                ("daily_report_emails", models.BooleanField(default=True)),
                ("accept_cash", models.BooleanField(default=True)),
                ("accept_cards", models.BooleanField(default=True)),
                ("accept_digital_wallets", models.BooleanField(default=False)),
                ("tip_suggestions", models.JSONField(default=list)),
                ("operating_hours", models.JSONField(default=dict)),
                (
                    "restaurant",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settings",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Restaurant Settings",
                "verbose_name_plural": "Restaurant Settings",
                "db_table": "restaurant_settings",
            },
        ),
        migrations.CreateModel(
            name="Table",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("number", models.CharField(max_length=10)),
                ("name", models.CharField(blank=True, max_length=50)),
                ("capacity", models.PositiveIntegerField(default=4)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("occupied", "Occupied"),
                            ("reserved", "Reserved"),
                            ("out_of_order", "Out of Order"),
                        ],
                        default="available",
                        max_length=20,
                    ),
                ),
                ("section", models.CharField(blank=True, max_length=50)),
                ("x_position", models.FloatField(default=0)),
                ("y_position", models.FloatField(default=0)),
                (
                    "qr_code",
                    models.ImageField(
                        blank=True, null=True, upload_to="table_qr_codes/"
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tables",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Table",
                "verbose_name_plural": "Tables",
                "db_table": "tables",
                "unique_together": {("restaurant", "number")},
            },
        ),
    ]

"""
Restaurant models for multi-tenant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from apps.core.models import BaseModel, SoftDeleteModel


class Restaurant(SoftDeleteModel):
    """
    Restaurant model for multi-tenant support.
    """
    
    class SubscriptionPlan(models.TextChoices):
        BASIC = 'basic', _('Basic')
        STANDARD = 'standard', _('Standard')
        PREMIUM = 'premium', _('Premium')
        ENTERPRISE = 'enterprise', _('Enterprise')

    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        SUSPENDED = 'suspended', _('Suspended')
        TRIAL = 'trial', _('Trial')

    name = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    subdomain = models.CharField(
        max_length=50, 
        unique=True,
        validators=[RegexValidator(r'^[a-zA-Z0-9-]+$', 'Only alphanumeric characters and hyphens allowed.')]
    )
    
    # Contact Information
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    website = models.URLField(blank=True)
    
    # Address
    address_line_1 = models.CharField(max_length=200)
    address_line_2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100, default='United States')
    
    # Business Information
    cuisine_type = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)
    logo = models.ImageField(upload_to='restaurant_logos/', blank=True, null=True)
    
    # Subscription & Billing
    subscription_plan = models.CharField(
        max_length=20,
        choices=SubscriptionPlan.choices,
        default=SubscriptionPlan.BASIC
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.TRIAL
    )
    trial_ends_at = models.DateTimeField(null=True, blank=True)
    subscription_starts_at = models.DateTimeField(null=True, blank=True)
    subscription_ends_at = models.DateTimeField(null=True, blank=True)
    
    # Settings
    timezone = models.CharField(max_length=50, default='UTC')
    currency = models.CharField(max_length=3, default='USD')
    tax_rate = models.DecimalField(max_digits=5, decimal_places=4, default=0.0875)  # 8.75%
    service_charge_rate = models.DecimalField(max_digits=5, decimal_places=4, default=0.0)
    
    # Features enabled based on subscription
    max_users = models.PositiveIntegerField(default=5)
    max_tables = models.PositiveIntegerField(default=20)
    max_menu_items = models.PositiveIntegerField(default=100)
    analytics_enabled = models.BooleanField(default=False)
    inventory_enabled = models.BooleanField(default=False)
    loyalty_program_enabled = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'restaurants'
        verbose_name = _('Restaurant')
        verbose_name_plural = _('Restaurants')

    def __str__(self):
        return self.name

    @property
    def full_address(self):
        """Return formatted full address."""
        address_parts = [self.address_line_1]
        if self.address_line_2:
            address_parts.append(self.address_line_2)
        address_parts.extend([self.city, self.state, self.postal_code])
        return ', '.join(address_parts)

    def is_subscription_active(self):
        """Check if subscription is active."""
        from django.utils import timezone
        now = timezone.now()
        
        if self.status == self.Status.TRIAL:
            return self.trial_ends_at and self.trial_ends_at > now
        
        return (
            self.status == self.Status.ACTIVE and
            self.subscription_ends_at and
            self.subscription_ends_at > now
        )


class RestaurantSettings(BaseModel):
    """
    Additional settings for restaurants.
    """
    restaurant = models.OneToOneField(
        Restaurant,
        on_delete=models.CASCADE,
        related_name='settings'
    )
    
    # POS Settings
    auto_print_receipts = models.BooleanField(default=True)
    receipt_footer_text = models.TextField(blank=True)
    order_number_prefix = models.CharField(max_length=10, default='ORD')
    table_number_prefix = models.CharField(max_length=10, default='T')
    
    # Kitchen Display Settings
    kitchen_display_enabled = models.BooleanField(default=True)
    order_ready_sound = models.BooleanField(default=True)
    auto_complete_orders = models.BooleanField(default=False)
    
    # Notification Settings
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    low_stock_alerts = models.BooleanField(default=True)
    daily_report_emails = models.BooleanField(default=True)
    
    # Payment Settings
    accept_cash = models.BooleanField(default=True)
    accept_cards = models.BooleanField(default=True)
    accept_digital_wallets = models.BooleanField(default=False)
    tip_suggestions = models.JSONField(default=list)  # [15, 18, 20, 25]
    
    # Operating Hours (JSON format)
    operating_hours = models.JSONField(default=dict)
    
    class Meta:
        db_table = 'restaurant_settings'
        verbose_name = _('Restaurant Settings')
        verbose_name_plural = _('Restaurant Settings')

    def __str__(self):
        return f"Settings for {self.restaurant.name}"


class Table(BaseModel):
    """
    Restaurant table model.
    """
    
    class Status(models.TextChoices):
        AVAILABLE = 'available', _('Available')
        OCCUPIED = 'occupied', _('Occupied')
        RESERVED = 'reserved', _('Reserved')
        OUT_OF_ORDER = 'out_of_order', _('Out of Order')

    restaurant = models.ForeignKey(
        Restaurant,
        on_delete=models.CASCADE,
        related_name='tables'
    )
    number = models.CharField(max_length=10)
    name = models.CharField(max_length=50, blank=True)
    capacity = models.PositiveIntegerField(default=4)
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.AVAILABLE
    )
    
    # Location in restaurant
    section = models.CharField(max_length=50, blank=True)  # e.g., "Main Dining", "Patio"
    x_position = models.FloatField(default=0)  # For floor plan layout
    y_position = models.FloatField(default=0)
    
    # QR Code for contactless ordering
    qr_code = models.ImageField(upload_to='table_qr_codes/', blank=True, null=True)
    qr_code_url = models.URLField(blank=True, help_text="URL for customer ordering interface")
    
    class Meta:
        db_table = 'tables'
        verbose_name = _('Table')
        verbose_name_plural = _('Tables')
        unique_together = ['restaurant', 'number']

    def __str__(self):
        return f"Table {self.number} - {self.restaurant.name}"

    @property
    def display_name(self):
        return self.name if self.name else f"Table {self.number}"

    def generate_qr_code_url(self, base_url):
        """Generate QR code URL for customer ordering."""
        return f"{base_url}/order/{self.restaurant.slug}/table/{self.number}"

    def save(self, *args, **kwargs):
        # Generate QR code URL if not set
        if not self.qr_code_url and hasattr(self, '_base_url'):
            self.qr_code_url = self.generate_qr_code_url(self._base_url)
        super().save(*args, **kwargs)


class RestaurantDomain(BaseModel):
    """
    Custom domains for restaurants.
    """
    restaurant = models.ForeignKey(
        Restaurant,
        on_delete=models.CASCADE,
        related_name='domains'
    )
    domain = models.CharField(max_length=255, unique=True)
    is_primary = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    ssl_enabled = models.BooleanField(default=False)

    class Meta:
        db_table = 'restaurant_domains'
        verbose_name = _('Restaurant Domain')
        verbose_name_plural = _('Restaurant Domains')

    def __str__(self):
        return f"{self.domain} - {self.restaurant.name}"

"""
Utility functions for restaurant management.
"""

import qrcode
from io import BytesIO
from django.core.files.base import ContentFile
from django.conf import settings
import uuid


def generate_table_qr_code(table, base_url=None):
    """
    Generate QR code for table ordering.
    
    Args:
        table: Table instance
        base_url: Base URL for the customer interface
    
    Returns:
        ContentFile: QR code image file
    """
    if not base_url:
        base_url = getattr(settings, 'CUSTOMER_INTERFACE_URL', 'http://localhost:3000')
    
    # Generate the customer ordering URL
    qr_url = f"{base_url}/order/{table.restaurant.slug}/table/{table.number}"
    
    # Create QR code
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(qr_url)
    qr.make(fit=True)
    
    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Save to BytesIO
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    
    # Create filename
    filename = f"table_{table.restaurant.slug}_{table.number}_qr.png"
    
    # Update table's QR code URL
    table.qr_code_url = qr_url
    
    return ContentFile(buffer.getvalue(), name=filename)


def regenerate_all_table_qr_codes(restaurant, base_url=None):
    """
    Regenerate QR codes for all tables in a restaurant.
    
    Args:
        restaurant: Restaurant instance
        base_url: Base URL for the customer interface
    
    Returns:
        int: Number of QR codes generated
    """
    count = 0
    for table in restaurant.tables.all():
        qr_file = generate_table_qr_code(table, base_url)
        table.qr_code.save(qr_file.name, qr_file, save=True)
        count += 1
    
    return count

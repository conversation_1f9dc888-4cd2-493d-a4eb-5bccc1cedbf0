"""
Restaurant views for multi-tenant POS system.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Sum, Q
from django.shortcuts import get_object_or_404

from .models import Restaurant, RestaurantSettings, Table, RestaurantDomain
from .serializers import RestaurantSerializer, RestaurantCreateSerializer, RestaurantSettingsSerializer, TableSerializer, RestaurantDomainSerializer, RestaurantStatsSerializer
from apps.core.permissions import IsSuperAdmin, IsRestaurantOwnerOrManager
from apps.core.middleware import get_current_restaurant


class RestaurantViewSet(viewsets.ModelViewSet):
    """
    Restaurant management viewset.
    """

    queryset = Restaurant.objects.all()

    def get_serializer_class(self):
        if self.action == "create":
            return RestaurantCreateSerializer
        return RestaurantSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action == "create":
            # Only super admins can create restaurants
            permission_classes = [IsSuperAdmin]
        elif self.action in ["update", "partial_update", "destroy"]:
            # Only super admins and restaurant owners can modify
            permission_classes = [IsSuperAdmin | IsRestaurantOwnerOrManager]
        else:
            # Anyone authenticated can view (filtered by tenant)
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """
        Filter restaurants based on user permissions.
        """
        user = self.request.user

        if user.user_type == "super_admin":
            # Super admins can see all restaurants
            return Restaurant.objects.all()
        else:
            # Regular users can only see their own restaurant
            restaurant = get_current_restaurant()
            if restaurant:
                return Restaurant.objects.filter(id=restaurant.id)
            return Restaurant.objects.none()

    @action(detail=True, methods=["get"])
    def stats(self, request, pk=None):
        """
        Get restaurant statistics.
        """
        restaurant = self.get_object()
        today = timezone.now().date()
        month_start = today.replace(day=1)

        # Calculate stats (will work once orders app is implemented)
        stats_data = {
            "total_orders_today": 0,  # Order.objects.filter(...).count()
            "total_revenue_today": 0,
            "total_orders_month": 0,
            "total_revenue_month": 0,
            "active_tables": restaurant.tables.filter(status="occupied").count(),
            "total_tables": restaurant.tables.count(),
            "total_menu_items": 0,  # restaurant.menu_items.count()
            "total_staff": 0,  # restaurant.staff_profiles.count()
            "subscription_status": restaurant.status,
            "subscription_expires": restaurant.subscription_ends_at,
        }

        serializer = RestaurantStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def activate(self, request, pk=None):
        """
        Activate restaurant subscription.
        """
        restaurant = self.get_object()
        restaurant.status = Restaurant.Status.ACTIVE
        restaurant.save()

        return Response({"status": "Restaurant activated successfully"})

    @action(detail=True, methods=["post"])
    def suspend(self, request, pk=None):
        """
        Suspend restaurant.
        """
        restaurant = self.get_object()
        restaurant.status = Restaurant.Status.SUSPENDED
        restaurant.save()

        return Response({"status": "Restaurant suspended successfully"})


class RestaurantSettingsViewSet(viewsets.ModelViewSet):
    """
    Restaurant settings management.
    """

    serializer_class = RestaurantSettingsSerializer
    permission_classes = [IsRestaurantOwnerOrManager]

    def get_queryset(self):
        """
        Return settings for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            return RestaurantSettings.objects.filter(restaurant=restaurant)
        return RestaurantSettings.objects.none()

    def get_object(self):
        """
        Get or create settings for current restaurant.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            settings, created = RestaurantSettings.objects.get_or_create(
                restaurant=restaurant,
                defaults={
                    "tip_suggestions": [15, 18, 20, 25],
                    "operating_hours": {
                        "monday": {"open": "09:00", "close": "22:00", "closed": False},
                        "tuesday": {"open": "09:00", "close": "22:00", "closed": False},
                        "wednesday": {"open": "09:00", "close": "22:00", "closed": False},
                        "thursday": {"open": "09:00", "close": "22:00", "closed": False},
                        "friday": {"open": "09:00", "close": "23:00", "closed": False},
                        "saturday": {"open": "09:00", "close": "23:00", "closed": False},
                        "sunday": {"open": "10:00", "close": "21:00", "closed": False},
                    },
                },
            )
            return settings
        return None


class TableViewSet(viewsets.ModelViewSet):
    """
    Table management viewset.
    """

    serializer_class = TableSerializer
    permission_classes = [IsRestaurantOwnerOrManager]

    def get_queryset(self):
        """
        Return tables for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            return Table.objects.filter(restaurant=restaurant)
        return Table.objects.none()

    @action(detail=True, methods=["post"])
    def set_status(self, request, pk=None):
        """
        Update table status.
        """
        table = self.get_object()
        new_status = request.data.get("status")

        if new_status in [choice[0] for choice in Table.Status.choices]:
            table.status = new_status
            table.save()
            return Response({"status": f"Table status updated to {new_status}"})

        return Response({"error": "Invalid status"}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def floor_plan(self, request):
        """
        Get table layout for floor plan.
        """
        tables = self.get_queryset()
        serializer = self.get_serializer(tables, many=True)
        return Response(serializer.data)


class RestaurantDomainViewSet(viewsets.ModelViewSet):
    """
    Restaurant domain management.
    """

    serializer_class = RestaurantDomainSerializer
    permission_classes = [IsRestaurantOwnerOrManager]

    def get_queryset(self):
        """
        Return domains for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            return RestaurantDomain.objects.filter(restaurant=restaurant)
        return RestaurantDomain.objects.none()

    @action(detail=True, methods=["post"])
    def verify(self, request, pk=None):
        """
        Verify domain ownership (placeholder for actual verification logic).
        """
        domain = self.get_object()
        # In a real implementation, this would check DNS records, etc.
        domain.is_verified = True
        domain.save()

        return Response({"status": "Domain verified successfully"})

    @action(detail=True, methods=["post"])
    def set_primary(self, request, pk=None):
        """
        Set domain as primary.
        """
        domain = self.get_object()

        # Remove primary flag from other domains
        RestaurantDomain.objects.filter(restaurant=domain.restaurant).update(is_primary=False)

        # Set this domain as primary
        domain.is_primary = True
        domain.save()

        return Response({"status": "Domain set as primary"})

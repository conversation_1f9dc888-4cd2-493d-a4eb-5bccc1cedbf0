"""
Django admin configuration for restaurants app.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.http import HttpResponse
from .models import Restaurant, RestaurantSettings, Table
from .utils import generate_table_qr_code


@admin.register(Restaurant)
class RestaurantAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'status', 'subscription_plan', 'created_at']
    list_filter = ['status', 'subscription_plan', 'created_at']
    search_fields = ['name', 'slug', 'email']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'subdomain', 'description', 'logo')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'website')
        }),
        ('Address', {
            'fields': ('address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country')
        }),
        ('Business Information', {
            'fields': ('cuisine_type', 'tax_rate', 'service_charge_rate', 'currency')
        }),
        ('Subscription', {
            'fields': ('subscription_plan', 'status', 'trial_end_date', 'subscription_end_date')
        }),
        ('Features', {
            'fields': ('max_users', 'max_tables', 'max_menu_items', 'analytics_enabled', 'inventory_enabled', 'loyalty_program_enabled')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(RestaurantSettings)
class RestaurantSettingsAdmin(admin.ModelAdmin):
    list_display = ['restaurant', 'timezone', 'language', 'currency']
    list_filter = ['timezone', 'language', 'currency']
    search_fields = ['restaurant__name']


class TableAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'restaurant', 'capacity', 'status', 'section', 'qr_code_preview', 'qr_code_actions']
    list_filter = ['restaurant', 'status', 'section']
    search_fields = ['number', 'name', 'restaurant__name']
    readonly_fields = ['qr_code_preview', 'qr_code_url']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('restaurant', 'number', 'name', 'capacity', 'status')
        }),
        ('Location', {
            'fields': ('section', 'x_position', 'y_position')
        }),
        ('QR Code', {
            'fields': ('qr_code', 'qr_code_url', 'qr_code_preview'),
            'description': 'QR code for customer ordering'
        }),
    )
    
    def qr_code_preview(self, obj):
        """Display QR code preview in admin."""
        if obj.qr_code:
            return format_html(
                '<img src="{}" width="100" height="100" style="border: 1px solid #ddd;" />',
                obj.qr_code.url
            )
        return "No QR code"
    qr_code_preview.short_description = "QR Code Preview"
    
    def qr_code_actions(self, obj):
        """Display action buttons for QR code management."""
        actions = []
        
        if obj.qr_code:
            # Download QR code
            actions.append(format_html(
                '<a href="{}" download="table_{}_qr.png" class="button">Download</a>',
                obj.qr_code.url,
                obj.number
            ))
        
        # Generate/Regenerate QR code
        generate_url = reverse('admin:generate_table_qr', args=[obj.pk])
        actions.append(format_html(
            '<a href="{}" class="button">Generate QR Code</a>',
            generate_url
        ))
        
        return format_html(' '.join(actions))
    qr_code_actions.short_description = "Actions"
    
    def get_urls(self):
        """Add custom URLs for QR code generation."""
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:table_id>/generate-qr/',
                self.admin_site.admin_view(self.generate_qr_code),
                name='generate_table_qr',
            ),
        ]
        return custom_urls + urls
    
    def generate_qr_code(self, request, table_id):
        """Generate QR code for a specific table."""
        try:
            table = Table.objects.get(pk=table_id)
            
            # Generate QR code
            base_url = request.build_absolute_uri('/').rstrip('/')
            base_url = base_url.replace('/admin', '')  # Remove admin path
            qr_file = generate_table_qr_code(table, base_url)
            
            # Save QR code
            table.qr_code.save(qr_file.name, qr_file, save=True)
            
            self.message_user(
                request,
                f'QR code generated successfully for {table.display_name}'
            )
            
        except Table.DoesNotExist:
            self.message_user(
                request,
                'Table not found',
                level='ERROR'
            )
        except Exception as e:
            self.message_user(
                request,
                f'Error generating QR code: {str(e)}',
                level='ERROR'
            )
        
        # Redirect back to table change page
        return HttpResponse(
            f'<script>window.location.href = "{reverse("admin:restaurants_table_change", args=[table_id])}";</script>'
        )


admin.site.register(Table, TableAdmin)

"""
Menu URLs for Restaurant POS system.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    MenuCategoryViewSet, MenuItemViewSet, MenuModifierGroupViewSet,
    MenuModifierViewSet, MenuComboViewSet
)

router = DefaultRouter()
router.register(r'categories', MenuCategoryViewSet, basename='menu-categories')
router.register(r'items', MenuItemViewSet, basename='menu-items')
router.register(r'modifier-groups', MenuModifierGroupViewSet, basename='modifier-groups')
router.register(r'modifiers', MenuModifierViewSet, basename='modifiers')
router.register(r'combos', MenuComboViewSet, basename='menu-combos')

app_name = 'menu'
urlpatterns = [
    path('', include(router.urls)),
]

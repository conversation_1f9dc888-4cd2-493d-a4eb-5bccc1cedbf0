"""
Menu admin configuration.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import (
    MenuCategory, MenuItem, MenuModifierGroup, MenuModifier,
    MenuItemModifierGroup, MenuCombo, MenuComboItem
)


@admin.register(MenuCategory)
class MenuCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'restaurant', 'sort_order', 'is_active', 'items_count', 'created_at']
    list_filter = ['restaurant', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'restaurant__name']
    ordering = ['restaurant', 'sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']
    
    def items_count(self, obj):
        return obj.items.count()
    items_count.short_description = 'Items Count'


class MenuItemModifierGroupInline(admin.TabularInline):
    model = MenuItemModifierGroup
    extra = 0


@admin.register(MenuItem)
class MenuItemAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'category', 'restaurant', 'price', 'item_type', 
        'is_available', 'is_featured', 'stock_status', 'created_at'
    ]
    list_filter = [
        'restaurant', 'category', 'item_type', 'is_available', 
        'is_featured', 'stock_enabled', 'created_at'
    ]
    search_fields = ['name', 'description', 'sku', 'restaurant__name']
    ordering = ['restaurant', 'category__sort_order', 'sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at', 'profit_margin']
    inlines = [MenuItemModifierGroupInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('category', 'name', 'description', 'item_type')
        }),
        ('Pricing', {
            'fields': ('price', 'cost_price', 'profit_margin')
        }),
        ('Availability', {
            'fields': ('is_available', 'is_featured', 'available_from', 'available_until')
        }),
        ('Stock Management', {
            'fields': ('stock_enabled', 'stock_quantity', 'low_stock_threshold'),
            'classes': ('collapse',)
        }),
        ('Product Details', {
            'fields': ('sku', 'barcode', 'image', 'gallery_images'),
            'classes': ('collapse',)
        }),
        ('Nutritional Information', {
            'fields': ('calories', 'allergens', 'dietary_info'),
            'classes': ('collapse',)
        }),
        ('Kitchen Information', {
            'fields': ('prep_time_minutes', 'cooking_instructions'),
            'classes': ('collapse',)
        }),
        ('Translations', {
            'fields': ('name_translations', 'description_translations'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('sort_order', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def stock_status(self, obj):
        if not obj.stock_enabled:
            return format_html('<span style="color: gray;">N/A</span>')
        elif obj.stock_quantity == 0:
            return format_html('<span style="color: red;">Out of Stock</span>')
        elif obj.is_low_stock:
            return format_html('<span style="color: orange;">Low Stock ({})</span>', obj.stock_quantity)
        else:
            return format_html('<span style="color: green;">In Stock ({})</span>', obj.stock_quantity)
    stock_status.short_description = 'Stock Status'


class MenuModifierInline(admin.TabularInline):
    model = MenuModifier
    extra = 0


@admin.register(MenuModifierGroup)
class MenuModifierGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'restaurant', 'selection_type', 'is_required', 'modifiers_count', 'created_at']
    list_filter = ['restaurant', 'selection_type', 'is_required', 'created_at']
    search_fields = ['name', 'description', 'restaurant__name']
    ordering = ['restaurant', 'sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [MenuModifierInline]
    
    def modifiers_count(self, obj):
        return obj.modifiers.count()
    modifiers_count.short_description = 'Modifiers Count'


@admin.register(MenuModifier)
class MenuModifierAdmin(admin.ModelAdmin):
    list_display = ['name', 'group', 'restaurant', 'price_adjustment', 'is_available', 'stock_status']
    list_filter = ['group__restaurant', 'group', 'is_available', 'stock_enabled']
    search_fields = ['name', 'group__name', 'group__restaurant__name']
    ordering = ['group__restaurant', 'group', 'sort_order', 'name']
    readonly_fields = ['restaurant']
    
    def restaurant(self, obj):
        return obj.group.restaurant
    restaurant.short_description = 'Restaurant'
    
    def stock_status(self, obj):
        if not obj.stock_enabled:
            return format_html('<span style="color: gray;">N/A</span>')
        elif obj.stock_quantity == 0:
            return format_html('<span style="color: red;">Out of Stock</span>')
        else:
            return format_html('<span style="color: green;">In Stock ({})</span>', obj.stock_quantity)
    stock_status.short_description = 'Stock Status'


class MenuComboItemInline(admin.TabularInline):
    model = MenuComboItem
    extra = 0


@admin.register(MenuCombo)
class MenuComboAdmin(admin.ModelAdmin):
    list_display = ['name', 'restaurant', 'price', 'savings_amount', 'is_available', 'created_at']
    list_filter = ['restaurant', 'is_available', 'created_at']
    search_fields = ['name', 'description', 'restaurant__name']
    ordering = ['restaurant', 'sort_order', 'name']
    readonly_fields = ['created_at', 'updated_at', 'total_individual_price', 'savings_amount']
    inlines = [MenuComboItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'price', 'image')
        }),
        ('Availability', {
            'fields': ('is_available', 'sort_order')
        }),
        ('Pricing Analysis', {
            'fields': ('total_individual_price', 'savings_amount'),
            'classes': ('collapse',)
        }),
        ('Translations', {
            'fields': ('name_translations', 'description_translations'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

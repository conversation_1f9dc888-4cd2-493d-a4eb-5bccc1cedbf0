# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("restaurants", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MenuCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "image",
                    models.ImageField(
                        blank=True, null=True, upload_to="menu_categories/"
                    ),
                ),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("is_active", models.BooleanField(default=True)),
                ("name_translations", models.J<PERSON><PERSON>ield(blank=True, default=dict)),
                (
                    "description_translations",
                    models.JSONField(blank=True, default=dict),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Menu Category",
                "verbose_name_plural": "Menu Categories",
                "db_table": "menu_categories",
                "ordering": ["sort_order", "name"],
                "unique_together": {("restaurant", "name")},
            },
        ),
        migrations.CreateModel(
            name="MenuCombo",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "image",
                    models.ImageField(blank=True, null=True, upload_to="menu_combos/"),
                ),
                ("is_available", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("name_translations", models.JSONField(blank=True, default=dict)),
                (
                    "description_translations",
                    models.JSONField(blank=True, default=dict),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Menu Combo",
                "verbose_name_plural": "Menu Combos",
                "db_table": "menu_combos",
                "ordering": ["sort_order", "name"],
                "unique_together": {("restaurant", "name")},
            },
        ),
        migrations.CreateModel(
            name="MenuItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "cost_price",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "item_type",
                    models.CharField(
                        choices=[
                            ("food", "Food"),
                            ("beverage", "Beverage"),
                            ("combo", "Combo"),
                            ("addon", "Add-on"),
                        ],
                        default="food",
                        max_length=20,
                    ),
                ),
                ("sku", models.CharField(blank=True, max_length=50)),
                ("barcode", models.CharField(blank=True, max_length=100)),
                ("is_available", models.BooleanField(default=True)),
                ("is_featured", models.BooleanField(default=False)),
                ("available_from", models.TimeField(blank=True, null=True)),
                ("available_until", models.TimeField(blank=True, null=True)),
                ("stock_enabled", models.BooleanField(default=False)),
                ("stock_quantity", models.PositiveIntegerField(default=0)),
                ("low_stock_threshold", models.PositiveIntegerField(default=5)),
                (
                    "image",
                    models.ImageField(blank=True, null=True, upload_to="menu_items/"),
                ),
                ("gallery_images", models.JSONField(blank=True, default=list)),
                ("calories", models.PositiveIntegerField(blank=True, null=True)),
                ("allergens", models.JSONField(blank=True, default=list)),
                ("dietary_info", models.JSONField(blank=True, default=list)),
                ("prep_time_minutes", models.PositiveIntegerField(default=0)),
                ("cooking_instructions", models.TextField(blank=True)),
                ("name_translations", models.JSONField(blank=True, default=dict)),
                (
                    "description_translations",
                    models.JSONField(blank=True, default=dict),
                ),
                ("sort_order", models.PositiveIntegerField(default=0)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="menu.menucategory",
                    ),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Menu Item",
                "verbose_name_plural": "Menu Items",
                "db_table": "menu_items",
                "ordering": ["category__sort_order", "sort_order", "name"],
                "unique_together": {("restaurant", "name")},
            },
        ),
        migrations.CreateModel(
            name="MenuModifierGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "selection_type",
                    models.CharField(
                        choices=[
                            ("single", "Single Selection"),
                            ("multiple", "Multiple Selection"),
                        ],
                        default="single",
                        max_length=20,
                    ),
                ),
                ("is_required", models.BooleanField(default=False)),
                ("min_selections", models.PositiveIntegerField(default=0)),
                ("max_selections", models.PositiveIntegerField(default=1)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("name_translations", models.JSONField(blank=True, default=dict)),
                (
                    "description_translations",
                    models.JSONField(blank=True, default=dict),
                ),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Menu Modifier Group",
                "verbose_name_plural": "Menu Modifier Groups",
                "db_table": "menu_modifier_groups",
                "ordering": ["sort_order", "name"],
                "unique_together": {("restaurant", "name")},
            },
        ),
        migrations.CreateModel(
            name="MenuComboItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("quantity", models.PositiveIntegerField(default=1)),
                ("is_optional", models.BooleanField(default=False)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                (
                    "combo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="combo_items",
                        to="menu.menucombo",
                    ),
                ),
                (
                    "menu_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="combo_inclusions",
                        to="menu.menuitem",
                    ),
                ),
            ],
            options={
                "db_table": "menu_combo_items",
                "ordering": ["sort_order"],
                "unique_together": {("combo", "menu_item")},
            },
        ),
        migrations.CreateModel(
            name="MenuModifier",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "price_adjustment",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Price to add/subtract from base item price",
                        max_digits=10,
                    ),
                ),
                ("is_available", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                ("stock_enabled", models.BooleanField(default=False)),
                ("stock_quantity", models.PositiveIntegerField(default=0)),
                ("name_translations", models.JSONField(blank=True, default=dict)),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="modifiers",
                        to="menu.menumodifiergroup",
                    ),
                ),
            ],
            options={
                "verbose_name": "Menu Modifier",
                "verbose_name_plural": "Menu Modifiers",
                "db_table": "menu_modifiers",
                "ordering": ["sort_order", "name"],
                "unique_together": {("group", "name")},
            },
        ),
        migrations.CreateModel(
            name="MenuItemModifierGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_required", models.BooleanField(default=False)),
                ("sort_order", models.PositiveIntegerField(default=0)),
                (
                    "menu_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="modifier_groups",
                        to="menu.menuitem",
                    ),
                ),
                (
                    "modifier_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="menu_items",
                        to="menu.menumodifiergroup",
                    ),
                ),
            ],
            options={
                "db_table": "menu_item_modifier_groups",
                "ordering": ["sort_order"],
                "unique_together": {("menu_item", "modifier_group")},
            },
        ),
    ]

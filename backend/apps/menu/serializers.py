"""
Menu serializers for Restaurant POS system.
"""

from rest_framework import serializers
from .models import (
    MenuCategory, MenuItem, MenuModifierGroup, MenuModifier,
    MenuItemModifierGroup, MenuCombo, MenuComboItem
)


class MenuModifierSerializer(serializers.ModelSerializer):
    """
    Menu modifier serializer.
    """
    is_in_stock = serializers.ReadOnlyField()
    
    class Meta:
        model = MenuModifier
        fields = [
            'id', 'name', 'price_adjustment', 'is_available', 'sort_order',
            'stock_enabled', 'stock_quantity', 'is_in_stock',
            'name_translations', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class MenuModifierGroupSerializer(serializers.ModelSerializer):
    """
    Menu modifier group serializer.
    """
    modifiers = MenuModifierSerializer(many=True, read_only=True)
    
    class Meta:
        model = MenuModifierGroup
        fields = [
            'id', 'name', 'description', 'selection_type', 'is_required',
            'min_selections', 'max_selections', 'sort_order',
            'name_translations', 'description_translations',
            'modifiers', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class MenuItemModifierGroupSerializer(serializers.ModelSerializer):
    """
    Menu item modifier group relationship serializer.
    """
    modifier_group = MenuModifierGroupSerializer(read_only=True)
    modifier_group_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = MenuItemModifierGroup
        fields = [
            'id', 'modifier_group', 'modifier_group_id', 'is_required', 'sort_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class MenuItemSerializer(serializers.ModelSerializer):
    """
    Menu item serializer.
    """
    is_in_stock = serializers.ReadOnlyField()
    is_low_stock = serializers.ReadOnlyField()
    profit_margin = serializers.ReadOnlyField()
    modifier_groups = MenuItemModifierGroupSerializer(many=True, read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = MenuItem
        fields = [
            'id', 'category', 'category_name', 'name', 'description', 'price', 'cost_price',
            'item_type', 'sku', 'barcode', 'is_available', 'is_featured',
            'available_from', 'available_until', 'stock_enabled', 'stock_quantity',
            'low_stock_threshold', 'image', 'gallery_images', 'calories', 'allergens',
            'dietary_info', 'prep_time_minutes', 'cooking_instructions',
            'name_translations', 'description_translations', 'sort_order',
            'is_in_stock', 'is_low_stock', 'profit_margin', 'modifier_groups',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Get restaurant from context (set by tenant middleware)
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class MenuItemListSerializer(serializers.ModelSerializer):
    """
    Simplified menu item serializer for list views.
    """
    is_in_stock = serializers.ReadOnlyField()
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = MenuItem
        fields = [
            'id', 'category', 'category_name', 'name', 'description', 'price',
            'item_type', 'is_available', 'is_featured', 'image', 'calories',
            'allergens', 'dietary_info', 'prep_time_minutes', 'is_in_stock',
            'sort_order'
        ]


class MenuCategorySerializer(serializers.ModelSerializer):
    """
    Menu category serializer.
    """
    items = MenuItemListSerializer(many=True, read_only=True)
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = MenuCategory
        fields = [
            'id', 'name', 'description', 'image', 'sort_order', 'is_active',
            'name_translations', 'description_translations', 'items', 'items_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_items_count(self, obj):
        return obj.items.filter(is_available=True).count()

    def create(self, validated_data):
        # Get restaurant from context (set by tenant middleware)
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class MenuCategoryListSerializer(serializers.ModelSerializer):
    """
    Simplified menu category serializer for list views.
    """
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = MenuCategory
        fields = [
            'id', 'name', 'description', 'image', 'sort_order', 'is_active',
            'items_count'
        ]

    def get_items_count(self, obj):
        return obj.items.filter(is_available=True).count()


class MenuComboItemSerializer(serializers.ModelSerializer):
    """
    Menu combo item serializer.
    """
    menu_item = MenuItemListSerializer(read_only=True)
    menu_item_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = MenuComboItem
        fields = [
            'id', 'menu_item', 'menu_item_id', 'quantity', 'is_optional', 'sort_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class MenuComboSerializer(serializers.ModelSerializer):
    """
    Menu combo serializer.
    """
    combo_items = MenuComboItemSerializer(many=True, read_only=True)
    total_individual_price = serializers.ReadOnlyField()
    savings_amount = serializers.ReadOnlyField()
    
    class Meta:
        model = MenuCombo
        fields = [
            'id', 'name', 'description', 'price', 'image', 'is_available', 'sort_order',
            'name_translations', 'description_translations', 'combo_items',
            'total_individual_price', 'savings_amount', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Get restaurant from context (set by tenant middleware)
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class MenuItemStockUpdateSerializer(serializers.Serializer):
    """
    Serializer for updating menu item stock.
    """
    stock_quantity = serializers.IntegerField(min_value=0)
    adjustment_reason = serializers.CharField(max_length=200, required=False)


class MenuBulkUpdateSerializer(serializers.Serializer):
    """
    Serializer for bulk menu operations.
    """
    item_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False
    )
    action = serializers.ChoiceField(choices=[
        'activate', 'deactivate', 'feature', 'unfeature', 'delete'
    ])


class MenuImportSerializer(serializers.Serializer):
    """
    Serializer for menu import operations.
    """
    csv_file = serializers.FileField()
    update_existing = serializers.BooleanField(default=False)
    create_categories = serializers.BooleanField(default=True)

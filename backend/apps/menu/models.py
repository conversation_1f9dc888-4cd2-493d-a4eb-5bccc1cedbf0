"""
Menu models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from apps.core.models import BaseModel, TenantAwareModel, SoftDeleteModel
from apps.restaurants.models import Restaurant


class MenuCategory(TenantAwareModel, SoftDeleteModel):
    """
    Menu category model for organizing menu items.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    image = models.ImageField(upload_to='menu_categories/', blank=True, null=True)
    sort_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    
    # Multi-language support
    name_translations = models.JSONField(default=dict, blank=True)
    description_translations = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'menu_categories'
        verbose_name = _('Menu Category')
        verbose_name_plural = _('Menu Categories')
        unique_together = ['restaurant', 'name']
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"

    def get_translated_name(self, language_code='en'):
        """Get translated name for given language."""
        return self.name_translations.get(language_code, self.name)

    def get_translated_description(self, language_code='en'):
        """Get translated description for given language."""
        return self.description_translations.get(language_code, self.description)


class MenuItem(TenantAwareModel, SoftDeleteModel):
    """
    Menu item model.
    """
    
    class ItemType(models.TextChoices):
        FOOD = 'food', _('Food')
        BEVERAGE = 'beverage', _('Beverage')
        COMBO = 'combo', _('Combo')
        ADDON = 'addon', _('Add-on')

    category = models.ForeignKey(
        MenuCategory,
        on_delete=models.CASCADE,
        related_name='items'
    )
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Item details
    item_type = models.CharField(
        max_length=20,
        choices=ItemType.choices,
        default=ItemType.FOOD
    )
    sku = models.CharField(max_length=50, blank=True)
    barcode = models.CharField(max_length=100, blank=True)
    
    # Availability
    is_available = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    available_from = models.TimeField(null=True, blank=True)
    available_until = models.TimeField(null=True, blank=True)
    
    # Stock management (optional)
    stock_enabled = models.BooleanField(default=False)
    stock_quantity = models.PositiveIntegerField(default=0)
    low_stock_threshold = models.PositiveIntegerField(default=5)
    
    # Images
    image = models.ImageField(upload_to='menu_items/', blank=True, null=True)
    gallery_images = models.JSONField(default=list, blank=True)  # List of image URLs
    
    # Nutritional information
    calories = models.PositiveIntegerField(null=True, blank=True)
    allergens = models.JSONField(default=list, blank=True)  # List of allergen names
    dietary_info = models.JSONField(default=list, blank=True)  # vegetarian, vegan, gluten-free, etc.
    
    # Preparation
    prep_time_minutes = models.PositiveIntegerField(default=0)
    cooking_instructions = models.TextField(blank=True)
    
    # Multi-language support
    name_translations = models.JSONField(default=dict, blank=True)
    description_translations = models.JSONField(default=dict, blank=True)
    
    # Sorting and display
    sort_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'menu_items'
        verbose_name = _('Menu Item')
        verbose_name_plural = _('Menu Items')
        unique_together = ['restaurant', 'name']
        ordering = ['category__sort_order', 'sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"

    @property
    def is_in_stock(self):
        """Check if item is in stock."""
        if not self.stock_enabled:
            return True
        return self.stock_quantity > 0

    @property
    def is_low_stock(self):
        """Check if item is low in stock."""
        if not self.stock_enabled:
            return False
        return self.stock_quantity <= self.low_stock_threshold

    @property
    def profit_margin(self):
        """Calculate profit margin percentage."""
        if self.cost_price > 0:
            return ((self.price - self.cost_price) / self.price) * 100
        return 0

    def get_translated_name(self, language_code='en'):
        """Get translated name for given language."""
        return self.name_translations.get(language_code, self.name)

    def get_translated_description(self, language_code='en'):
        """Get translated description for given language."""
        return self.description_translations.get(language_code, self.description)

    def reduce_stock(self, quantity=1):
        """Reduce stock quantity."""
        if self.stock_enabled and self.stock_quantity >= quantity:
            self.stock_quantity -= quantity
            self.save(update_fields=['stock_quantity'])
            return True
        return False


class MenuModifierGroup(TenantAwareModel):
    """
    Modifier group for menu items (e.g., Size, Toppings, Sides).
    """
    
    class SelectionType(models.TextChoices):
        SINGLE = 'single', _('Single Selection')
        MULTIPLE = 'multiple', _('Multiple Selection')

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    selection_type = models.CharField(
        max_length=20,
        choices=SelectionType.choices,
        default=SelectionType.SINGLE
    )
    is_required = models.BooleanField(default=False)
    min_selections = models.PositiveIntegerField(default=0)
    max_selections = models.PositiveIntegerField(default=1)
    sort_order = models.PositiveIntegerField(default=0)
    
    # Multi-language support
    name_translations = models.JSONField(default=dict, blank=True)
    description_translations = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'menu_modifier_groups'
        verbose_name = _('Menu Modifier Group')
        verbose_name_plural = _('Menu Modifier Groups')
        unique_together = ['restaurant', 'name']
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"


class MenuModifier(TenantAwareModel):
    """
    Individual modifier option within a modifier group.
    """
    group = models.ForeignKey(
        MenuModifierGroup,
        on_delete=models.CASCADE,
        related_name='modifiers'
    )
    name = models.CharField(max_length=100)
    price_adjustment = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        default=0,
        help_text="Price to add/subtract from base item price"
    )
    is_available = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    
    # Stock management for modifiers
    stock_enabled = models.BooleanField(default=False)
    stock_quantity = models.PositiveIntegerField(default=0)
    
    # Multi-language support
    name_translations = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'menu_modifiers'
        verbose_name = _('Menu Modifier')
        verbose_name_plural = _('Menu Modifiers')
        unique_together = ['group', 'name']
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} ({self.group.name}) - {self.restaurant.name}"

    @property
    def is_in_stock(self):
        """Check if modifier is in stock."""
        if not self.stock_enabled:
            return True
        return self.stock_quantity > 0


class MenuItemModifierGroup(BaseModel):
    """
    Many-to-many relationship between menu items and modifier groups.
    """
    menu_item = models.ForeignKey(
        MenuItem,
        on_delete=models.CASCADE,
        related_name='modifier_groups'
    )
    modifier_group = models.ForeignKey(
        MenuModifierGroup,
        on_delete=models.CASCADE,
        related_name='menu_items'
    )
    is_required = models.BooleanField(default=False)
    sort_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'menu_item_modifier_groups'
        unique_together = ['menu_item', 'modifier_group']
        ordering = ['sort_order']

    def __str__(self):
        return f"{self.menu_item.name} - {self.modifier_group.name}"


class MenuCombo(TenantAwareModel, SoftDeleteModel):
    """
    Combo meals that include multiple menu items.
    """
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    image = models.ImageField(upload_to='menu_combos/', blank=True, null=True)
    is_available = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    
    # Multi-language support
    name_translations = models.JSONField(default=dict, blank=True)
    description_translations = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'menu_combos'
        verbose_name = _('Menu Combo')
        verbose_name_plural = _('Menu Combos')
        unique_together = ['restaurant', 'name']
        ordering = ['sort_order', 'name']

    def __str__(self):
        return f"{self.name} - {self.restaurant.name}"

    @property
    def total_individual_price(self):
        """Calculate total price if items were ordered individually."""
        return sum(item.menu_item.price * item.quantity for item in self.combo_items.all())

    @property
    def savings_amount(self):
        """Calculate savings amount compared to individual prices."""
        return self.total_individual_price - self.price


class MenuComboItem(BaseModel):
    """
    Items included in a combo meal.
    """
    combo = models.ForeignKey(
        MenuCombo,
        on_delete=models.CASCADE,
        related_name='combo_items'
    )
    menu_item = models.ForeignKey(
        MenuItem,
        on_delete=models.CASCADE,
        related_name='combo_inclusions'
    )
    quantity = models.PositiveIntegerField(default=1)
    is_optional = models.BooleanField(default=False)
    sort_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'menu_combo_items'
        unique_together = ['combo', 'menu_item']
        ordering = ['sort_order']

    def __str__(self):
        return f"{self.combo.name} - {self.menu_item.name} (x{self.quantity})"

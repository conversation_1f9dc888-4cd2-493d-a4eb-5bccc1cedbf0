"""
Menu views for Restaurant POS system.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg
from django.shortcuts import get_object_or_404

from .models import MenuCategory, MenuItem, MenuModifierGroup, MenuModifier, MenuItemModifierGroup, MenuCombo, MenuComboItem
from .serializers import (
    MenuCategorySerializer,
    MenuCategoryListSerializer,
    MenuItemSerializer,
    MenuItemListSerializer,
    MenuModifierGroupSerializer,
    MenuModifierSerializer,
    MenuComboSerializer,
    MenuItemStockUpdateSerializer,
    MenuBulkUpdateSerializer,
    MenuImportSerializer,
)
from apps.core.permissions import IsRestaurantStaff
from apps.core.middleware import get_current_restaurant


class MenuCategoryViewSet(viewsets.ModelViewSet):
    """
    Menu category management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "description"]
    ordering_fields = ["sort_order", "name", "created_at"]
    ordering = ["sort_order", "name"]

    def get_serializer_class(self):
        if self.action == "list":
            return MenuCategoryListSerializer
        return MenuCategorySerializer

    def get_queryset(self):
        """
        Return categories for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            queryset = MenuCategory.objects.filter(restaurant=restaurant)

            # Filter by active status
            is_active = self.request.query_params.get("is_active")
            if is_active is not None:
                queryset = queryset.filter(is_active=is_active.lower() == "true")

            return queryset
        return MenuCategory.objects.none()

    @action(detail=True, methods=["post"])
    def toggle_active(self, request, pk=None):
        """
        Toggle category active status.
        """
        category = self.get_object()
        category.is_active = not category.is_active
        category.save()

        return Response({"status": f'Category {"activated" if category.is_active else "deactivated"} successfully'})

    @action(detail=False, methods=["post"])
    def reorder(self, request):
        """
        Reorder categories.
        """
        category_orders = request.data.get("category_orders", [])

        for item in category_orders:
            category_id = item.get("id")
            sort_order = item.get("sort_order")

            if category_id and sort_order is not None:
                MenuCategory.objects.filter(id=category_id, restaurant=get_current_restaurant()).update(sort_order=sort_order)

        return Response({"status": "Categories reordered successfully"})


class MenuItemViewSet(viewsets.ModelViewSet):
    """
    Menu item management viewset.
    """

    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterBackend]
    search_fields = ["name", "description", "sku"]
    ordering_fields = ["sort_order", "name", "price", "created_at"]
    ordering = ["category__sort_order", "sort_order", "name"]
    filterset_fields = ["category", "item_type", "is_available", "is_featured"]

    def get_serializer_class(self):
        if self.action == "list":
            return MenuItemListSerializer
        return MenuItemSerializer

    def get_queryset(self):
        """
        Return menu items for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            queryset = MenuItem.objects.filter(restaurant=restaurant).select_related("category")

            # Filter by stock status
            stock_status = self.request.query_params.get("stock_status")
            if stock_status == "in_stock":
                queryset = queryset.filter(Q(stock_enabled=False) | Q(stock_quantity__gt=0))
            elif stock_status == "out_of_stock":
                queryset = queryset.filter(stock_enabled=True, stock_quantity=0)
            elif stock_status == "low_stock":
                queryset = queryset.filter(stock_enabled=True, stock_quantity__lte=models.F("low_stock_threshold"), stock_quantity__gt=0)

            # Filter by price range
            min_price = self.request.query_params.get("min_price")
            max_price = self.request.query_params.get("max_price")
            if min_price:
                queryset = queryset.filter(price__gte=min_price)
            if max_price:
                queryset = queryset.filter(price__lte=max_price)

            return queryset
        return MenuItem.objects.none()

    @action(detail=True, methods=["post"])
    def update_stock(self, request, pk=None):
        """
        Update menu item stock quantity.
        """
        item = self.get_object()
        serializer = MenuItemStockUpdateSerializer(data=request.data)

        if serializer.is_valid():
            old_quantity = item.stock_quantity
            item.stock_quantity = serializer.validated_data["stock_quantity"]
            item.save()

            # Log stock adjustment (would integrate with inventory system)
            adjustment_reason = serializer.validated_data.get("adjustment_reason", "Manual adjustment")

            return Response({"status": "Stock updated successfully", "old_quantity": old_quantity, "new_quantity": item.stock_quantity, "adjustment_reason": adjustment_reason})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def toggle_availability(self, request, pk=None):
        """
        Toggle item availability.
        """
        item = self.get_object()
        item.is_available = not item.is_available
        item.save()

        return Response({"status": f'Item {"made available" if item.is_available else "made unavailable"} successfully'})

    @action(detail=True, methods=["post"])
    def toggle_featured(self, request, pk=None):
        """
        Toggle item featured status.
        """
        item = self.get_object()
        item.is_featured = not item.is_featured
        item.save()

        return Response({"status": f'Item {"featured" if item.is_featured else "unfeatured"} successfully'})

    @action(detail=False, methods=["post"])
    def bulk_update(self, request):
        """
        Bulk update menu items.
        """
        serializer = MenuBulkUpdateSerializer(data=request.data)

        if serializer.is_valid():
            item_ids = serializer.validated_data["item_ids"]
            action_type = serializer.validated_data["action"]

            items = MenuItem.objects.filter(id__in=item_ids, restaurant=get_current_restaurant())

            if action_type == "activate":
                items.update(is_available=True)
            elif action_type == "deactivate":
                items.update(is_available=False)
            elif action_type == "feature":
                items.update(is_featured=True)
            elif action_type == "unfeature":
                items.update(is_featured=False)
            elif action_type == "delete":
                items.delete()

            return Response({"status": f"Bulk {action_type} completed successfully", "affected_items": len(item_ids)})

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=["get"])
    def featured(self, request):
        """
        Get featured menu items.
        """
        items = self.get_queryset().filter(is_featured=True, is_available=True)
        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def low_stock(self, request):
        """
        Get low stock items.
        """
        items = self.get_queryset().filter(stock_enabled=True, stock_quantity__lte=models.F("low_stock_threshold"), stock_quantity__gt=0)
        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)


class MenuModifierGroupViewSet(viewsets.ModelViewSet):
    """
    Menu modifier group management viewset.
    """

    serializer_class = MenuModifierGroupSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "description"]
    ordering_fields = ["sort_order", "name", "created_at"]
    ordering = ["sort_order", "name"]

    def get_queryset(self):
        """
        Return modifier groups for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            return MenuModifierGroup.objects.filter(restaurant=restaurant)
        return MenuModifierGroup.objects.none()


class MenuModifierViewSet(viewsets.ModelViewSet):
    """
    Menu modifier management viewset.
    """

    serializer_class = MenuModifierSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name"]
    ordering_fields = ["sort_order", "name", "price_adjustment"]
    ordering = ["sort_order", "name"]

    def get_queryset(self):
        """
        Return modifiers for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            queryset = MenuModifier.objects.filter(restaurant=restaurant)

            # Filter by modifier group
            group_id = self.request.query_params.get("group")
            if group_id:
                queryset = queryset.filter(group_id=group_id)

            return queryset
        return MenuModifier.objects.none()


class MenuComboViewSet(viewsets.ModelViewSet):
    """
    Menu combo management viewset.
    """

    serializer_class = MenuComboSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "description"]
    ordering_fields = ["sort_order", "name", "price", "created_at"]
    ordering = ["sort_order", "name"]

    def get_queryset(self):
        """
        Return combos for current restaurant only.
        """
        restaurant = get_current_restaurant()
        if restaurant:
            return MenuCombo.objects.filter(restaurant=restaurant)
        return MenuCombo.objects.none()

    @action(detail=True, methods=["post"])
    def toggle_availability(self, request, pk=None):
        """
        Toggle combo availability.
        """
        combo = self.get_object()
        combo.is_available = not combo.is_available
        combo.save()

        return Response({"status": f'Combo {"made available" if combo.is_available else "made unavailable"} successfully'})

"""
Customer Management & CRM URLs for Restaurant POS system.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CustomerViewSet, LoyaltyProgramViewSet, CustomerLoyaltyViewSet,
    CustomerFeedbackViewSet, CustomerGroupViewSet, MarketingCampaignViewSet
)

router = DefaultRouter()
router.register(r'customers', CustomerViewSet, basename='customers')
router.register(r'loyalty-programs', LoyaltyProgramViewSet, basename='loyalty-programs')
router.register(r'customer-loyalty', CustomerLoyaltyViewSet, basename='customer-loyalty')
router.register(r'feedback', CustomerFeedbackViewSet, basename='feedback')
router.register(r'groups', CustomerGroupViewSet, basename='groups')
router.register(r'campaigns', MarketingCampaignViewSet, basename='campaigns')

app_name = 'customers'
urlpatterns = [
    path('', include(router.urls)),
]

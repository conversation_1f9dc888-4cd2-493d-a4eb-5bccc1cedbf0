"""
Customer Management & CRM models for Restaurant POS system.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from apps.core.models import BaseModel, TenantAwareModel
from apps.restaurants.models import Restaurant
from apps.users.models import User


class Customer(TenantAwareModel):
    """
    Customer model for CRM system.
    """
    
    class CustomerType(models.TextChoices):
        WALK_IN = 'walk_in', _('Walk-in Customer')
        REGULAR = 'regular', _('Regular Customer')
        VIP = 'vip', _('VIP Customer')
        CORPORATE = 'corporate', _('Corporate Customer')

    # Basic information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    
    # Customer type and status
    customer_type = models.Char<PERSON><PERSON>(
        max_length=20,
        choices=CustomerType.choices,
        default=CustomerType.WALK_IN
    )
    is_active = models.BooleanField(default=True)
    
    # Address information
    address_line_1 = models.CharField(max_length=200, blank=True)
    address_line_2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)
    
    # Customer metrics
    total_orders = models.PositiveIntegerField(default=0)
    total_spent = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    last_order_date = models.DateTimeField(null=True, blank=True)
    
    # Preferences
    dietary_restrictions = models.JSONField(default=list, blank=True)
    favorite_items = models.JSONField(default=list, blank=True)
    preferred_table = models.CharField(max_length=50, blank=True)
    special_notes = models.TextField(blank=True)
    
    # Marketing preferences
    email_marketing_consent = models.BooleanField(default=False)
    sms_marketing_consent = models.BooleanField(default=False)
    
    # Birthday for special offers
    date_of_birth = models.DateField(null=True, blank=True)
    anniversary_date = models.DateField(null=True, blank=True)
    
    class Meta:
        db_table = 'customers'
        verbose_name = _('Customer')
        verbose_name_plural = _('Customers')
        ordering = ['last_name', 'first_name']
        unique_together = ['restaurant', 'email']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    def update_metrics(self):
        """Update customer metrics based on orders."""
        from apps.orders.models import Order
        
        orders = Order.objects.filter(
            customer=self,
            status=Order.Status.COMPLETED
        )
        
        self.total_orders = orders.count()
        self.total_spent = orders.aggregate(
            total=models.Sum('total_amount')
        )['total'] or 0
        
        if self.total_orders > 0:
            self.average_order_value = self.total_spent / self.total_orders
            self.last_order_date = orders.order_by('-order_time').first().order_time
        
        self.save()


class LoyaltyProgram(TenantAwareModel):
    """
    Loyalty program configuration.
    """
    
    class ProgramType(models.TextChoices):
        POINTS = 'points', _('Points Based')
        VISITS = 'visits', _('Visit Based')
        SPENDING = 'spending', _('Spending Based')

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    program_type = models.CharField(
        max_length=20,
        choices=ProgramType.choices,
        default=ProgramType.POINTS
    )
    
    is_active = models.BooleanField(default=True)
    
    # Points configuration
    points_per_dollar = models.DecimalField(
        max_digits=5, decimal_places=2, default=1,
        help_text="Points earned per dollar spent"
    )
    points_redemption_value = models.DecimalField(
        max_digits=5, decimal_places=2, default=0.01,
        help_text="Dollar value per point when redeeming"
    )
    
    # Visit configuration
    visits_required = models.PositiveIntegerField(
        default=10,
        help_text="Number of visits required for reward"
    )
    
    # Spending configuration
    spending_threshold = models.DecimalField(
        max_digits=10, decimal_places=2, default=100,
        help_text="Spending amount required for reward"
    )
    
    # Reward configuration
    reward_description = models.CharField(max_length=200)
    reward_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Program rules
    minimum_order_value = models.DecimalField(
        max_digits=10, decimal_places=2, default=0,
        help_text="Minimum order value to earn points/visits"
    )
    points_expiry_days = models.PositiveIntegerField(
        default=365,
        help_text="Days after which points expire (0 = never expire)"
    )
    
    class Meta:
        db_table = 'loyalty_programs'
        verbose_name = _('Loyalty Program')
        verbose_name_plural = _('Loyalty Programs')
        ordering = ['name']

    def __str__(self):
        return self.name


class CustomerLoyalty(TenantAwareModel):
    """
    Customer loyalty account.
    """
    
    customer = models.OneToOneField(
        Customer,
        on_delete=models.CASCADE,
        related_name='loyalty_account'
    )
    loyalty_program = models.ForeignKey(
        LoyaltyProgram,
        on_delete=models.CASCADE,
        related_name='customer_accounts'
    )
    
    # Current status
    current_points = models.PositiveIntegerField(default=0)
    current_visits = models.PositiveIntegerField(default=0)
    lifetime_points_earned = models.PositiveIntegerField(default=0)
    lifetime_points_redeemed = models.PositiveIntegerField(default=0)
    
    # Tier information
    tier_level = models.CharField(max_length=50, default='Bronze')
    tier_benefits = models.JSONField(default=list, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    enrollment_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'customer_loyalty'
        verbose_name = _('Customer Loyalty Account')
        verbose_name_plural = _('Customer Loyalty Accounts')
        unique_together = ['customer', 'loyalty_program']

    def __str__(self):
        return f"{self.customer.get_full_name()} - {self.loyalty_program.name}"

    def add_points(self, points, description=""):
        """Add points to customer account."""
        self.current_points += points
        self.lifetime_points_earned += points
        self.save()
        
        # Create transaction record
        LoyaltyTransaction.objects.create(
            customer_loyalty=self,
            transaction_type=LoyaltyTransaction.TransactionType.EARNED,
            points=points,
            description=description
        )

    def redeem_points(self, points, description=""):
        """Redeem points from customer account."""
        if self.current_points >= points:
            self.current_points -= points
            self.lifetime_points_redeemed += points
            self.save()
            
            # Create transaction record
            LoyaltyTransaction.objects.create(
                customer_loyalty=self,
                transaction_type=LoyaltyTransaction.TransactionType.REDEEMED,
                points=points,
                description=description
            )
            return True
        return False

    def add_visit(self):
        """Add a visit to customer account."""
        self.current_visits += 1
        self.save()


class LoyaltyTransaction(TenantAwareModel):
    """
    Loyalty points transaction history.
    """
    
    class TransactionType(models.TextChoices):
        EARNED = 'earned', _('Points Earned')
        REDEEMED = 'redeemed', _('Points Redeemed')
        EXPIRED = 'expired', _('Points Expired')
        ADJUSTED = 'adjusted', _('Manual Adjustment')

    customer_loyalty = models.ForeignKey(
        CustomerLoyalty,
        on_delete=models.CASCADE,
        related_name='transactions'
    )
    
    transaction_type = models.CharField(
        max_length=20,
        choices=TransactionType.choices
    )
    points = models.IntegerField()
    description = models.CharField(max_length=200, blank=True)
    
    # Related order (if applicable)
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='loyalty_transactions'
    )
    
    # Staff who processed the transaction
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_loyalty_transactions'
    )
    
    class Meta:
        db_table = 'loyalty_transactions'
        verbose_name = _('Loyalty Transaction')
        verbose_name_plural = _('Loyalty Transactions')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer_loyalty.customer.get_full_name()} - {self.get_transaction_type_display()}: {self.points} points"


class CustomerFeedback(TenantAwareModel):
    """
    Customer feedback and reviews.
    """
    
    class FeedbackType(models.TextChoices):
        REVIEW = 'review', _('Review')
        COMPLAINT = 'complaint', _('Complaint')
        SUGGESTION = 'suggestion', _('Suggestion')
        COMPLIMENT = 'compliment', _('Compliment')

    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending Review')
        ACKNOWLEDGED = 'acknowledged', _('Acknowledged')
        RESOLVED = 'resolved', _('Resolved')
        CLOSED = 'closed', _('Closed')

    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='feedback'
    )
    
    feedback_type = models.CharField(
        max_length=20,
        choices=FeedbackType.choices,
        default=FeedbackType.REVIEW
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    
    # Ratings (1-5 scale)
    overall_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    food_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    service_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    ambiance_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True,
        blank=True
    )
    
    # Feedback content
    title = models.CharField(max_length=200, blank=True)
    comment = models.TextField()
    
    # Related order
    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='feedback'
    )
    
    # Response from restaurant
    response = models.TextField(blank=True)
    responded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='feedback_responses'
    )
    response_date = models.DateTimeField(null=True, blank=True)
    
    # Follow-up
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'customer_feedback'
        verbose_name = _('Customer Feedback')
        verbose_name_plural = _('Customer Feedback')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer.get_full_name()} - {self.get_feedback_type_display()}"


class CustomerGroup(TenantAwareModel):
    """
    Customer groups for targeted marketing.
    """
    
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Group criteria
    criteria = models.JSONField(
        default=dict,
        help_text="JSON criteria for automatic group membership"
    )
    
    # Manual members
    customers = models.ManyToManyField(
        Customer,
        through='CustomerGroupMembership',
        related_name='groups'
    )
    
    is_active = models.BooleanField(default=True)
    
    # Marketing settings
    email_template = models.TextField(blank=True)
    sms_template = models.TextField(blank=True)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_customer_groups'
    )
    
    class Meta:
        db_table = 'customer_groups'
        verbose_name = _('Customer Group')
        verbose_name_plural = _('Customer Groups')
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_member_count(self):
        return self.customers.count()


class CustomerGroupMembership(TenantAwareModel):
    """
    Customer group membership.
    """
    
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    group = models.ForeignKey(CustomerGroup, on_delete=models.CASCADE)
    
    # Membership details
    joined_date = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    # How they joined
    join_method = models.CharField(
        max_length=50,
        choices=[
            ('manual', 'Manual Addition'),
            ('automatic', 'Automatic Criteria'),
            ('import', 'Data Import'),
            ('signup', 'Customer Signup')
        ],
        default='manual'
    )
    
    added_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='added_group_memberships'
    )
    
    class Meta:
        db_table = 'customer_group_memberships'
        verbose_name = _('Customer Group Membership')
        verbose_name_plural = _('Customer Group Memberships')
        unique_together = ['customer', 'group']
        ordering = ['-joined_date']

    def __str__(self):
        return f"{self.customer.get_full_name()} in {self.group.name}"


class MarketingCampaign(TenantAwareModel):
    """
    Marketing campaigns for customer engagement.
    """
    
    class CampaignType(models.TextChoices):
        EMAIL = 'email', _('Email Campaign')
        SMS = 'sms', _('SMS Campaign')
        PUSH = 'push', _('Push Notification')
        MIXED = 'mixed', _('Mixed Campaign')

    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        SCHEDULED = 'scheduled', _('Scheduled')
        RUNNING = 'running', _('Running')
        COMPLETED = 'completed', _('Completed')
        CANCELLED = 'cancelled', _('Cancelled')

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    campaign_type = models.CharField(
        max_length=20,
        choices=CampaignType.choices,
        default=CampaignType.EMAIL
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT
    )
    
    # Target audience
    target_groups = models.ManyToManyField(
        CustomerGroup,
        related_name='campaigns',
        blank=True
    )
    target_customers = models.ManyToManyField(
        Customer,
        related_name='campaigns',
        blank=True
    )
    
    # Campaign content
    subject = models.CharField(max_length=200, blank=True)
    email_content = models.TextField(blank=True)
    sms_content = models.TextField(blank=True)
    
    # Scheduling
    scheduled_date = models.DateTimeField(null=True, blank=True)
    sent_date = models.DateTimeField(null=True, blank=True)
    
    # Campaign metrics
    total_recipients = models.PositiveIntegerField(default=0)
    emails_sent = models.PositiveIntegerField(default=0)
    emails_opened = models.PositiveIntegerField(default=0)
    emails_clicked = models.PositiveIntegerField(default=0)
    sms_sent = models.PositiveIntegerField(default=0)
    sms_delivered = models.PositiveIntegerField(default=0)
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_campaigns'
    )
    
    class Meta:
        db_table = 'marketing_campaigns'
        verbose_name = _('Marketing Campaign')
        verbose_name_plural = _('Marketing Campaigns')
        ordering = ['-created_at']

    def __str__(self):
        return self.name

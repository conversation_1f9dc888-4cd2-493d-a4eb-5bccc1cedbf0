"""
Customer Management & CRM views for Restaurant POS system.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterFilter
from django.db.models import Q, Sum, Count, Avg, F
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, timedelta

from .models import (
    Customer, LoyaltyProgram, CustomerLoyalty, LoyaltyTransaction,
    CustomerFeedback, CustomerGroup, CustomerGroupMembership, MarketingCampaign
)
from .serializers import (
    CustomerSerializer, LoyaltyProgramSerializer, CustomerLoyaltySerializer,
    LoyaltyTransactionSerializer, CustomerFeedbackSerializer, CustomerGroupSerializer,
    CustomerGroupMembershipSerializer, MarketingCampaignSerializer,
    CustomerAnalyticsSerializer, LoyaltyRedemptionSerializer
)
from apps.core.permissions import IsRestaurantStaff
from apps.core.middleware import get_current_restaurant


class CustomerViewSet(viewsets.ModelViewSet):
    """
    Customer management viewset.
    """
    serializer_class = CustomerSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterFilter]
    search_fields = ['first_name', 'last_name', 'email', 'phone']
    ordering_fields = ['first_name', 'last_name', 'total_spent', 'total_orders', 'last_order_date']
    ordering = ['last_name', 'first_name']
    filterset_fields = ['customer_type', 'is_active']
    
    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return Customer.objects.filter(restaurant=restaurant)
        return Customer.objects.none()
    
    @action(detail=True, methods=['post'])
    def update_metrics(self, request, pk=None):
        """Update customer metrics based on orders."""
        customer = self.get_object()
        customer.update_metrics()
        serializer = self.get_serializer(customer)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """Get customer analytics."""
        restaurant = get_current_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=status.HTTP_400_BAD_REQUEST)
        
        customers = Customer.objects.filter(restaurant=restaurant)
        
        # Basic metrics
        total_customers = customers.count()
        
        # New customers this month
        this_month = timezone.now().replace(day=1)
        new_customers_this_month = customers.filter(
            created_at__gte=this_month
        ).count()
        
        # Returning customers (customers with more than 1 order)
        returning_customers = customers.filter(total_orders__gt=1).count()
        
        # Average customer value
        average_customer_value = customers.aggregate(
            avg_value=Avg('total_spent')
        )['avg_value'] or 0
        
        # Customer retention rate (simplified)
        last_month = this_month - timedelta(days=30)
        customers_last_month = customers.filter(
            created_at__lt=this_month,
            created_at__gte=last_month
        ).count()
        
        customers_still_active = customers.filter(
            created_at__lt=this_month,
            created_at__gte=last_month,
            last_order_date__gte=this_month
        ).count()
        
        retention_rate = (customers_still_active / customers_last_month * 100) if customers_last_month > 0 else 0
        
        # Customer segments
        customer_segments = [
            {
                'segment': 'VIP',
                'count': customers.filter(customer_type='vip').count(),
                'avg_spending': customers.filter(customer_type='vip').aggregate(avg=Avg('total_spent'))['avg'] or 0
            },
            {
                'segment': 'Regular',
                'count': customers.filter(customer_type='regular').count(),
                'avg_spending': customers.filter(customer_type='regular').aggregate(avg=Avg('total_spent'))['avg'] or 0
            },
            {
                'segment': 'Walk-in',
                'count': customers.filter(customer_type='walk_in').count(),
                'avg_spending': customers.filter(customer_type='walk_in').aggregate(avg=Avg('total_spent'))['avg'] or 0
            }
        ]
        
        # Top customers
        top_customers_by_spending = customers.order_by('-total_spent')[:10]
        top_customers_by_visits = customers.order_by('-total_orders')[:10]
        
        # Customer behavior metrics
        active_customers = customers.filter(total_orders__gt=0)
        average_order_frequency = active_customers.aggregate(
            avg_freq=Avg('total_orders')
        )['avg_freq'] or 0
        
        customer_lifetime_value = average_customer_value
        
        # Feedback metrics
        feedback_data = CustomerFeedback.objects.filter(
            customer__restaurant=restaurant
        ).aggregate(
            avg_rating=Avg('overall_rating'),
            count=Count('id')
        )
        
        # Loyalty metrics
        loyalty_accounts = CustomerLoyalty.objects.filter(
            customer__restaurant=restaurant
        )
        
        loyalty_participation = (loyalty_accounts.count() / total_customers * 100) if total_customers > 0 else 0
        average_points_balance = loyalty_accounts.aggregate(
            avg_points=Avg('current_points')
        )['avg_points'] or 0
        
        analytics_data = {
            'total_customers': total_customers,
            'new_customers_this_month': new_customers_this_month,
            'returning_customers': returning_customers,
            'average_customer_value': average_customer_value,
            'customer_retention_rate': retention_rate,
            'customer_segments': customer_segments,
            'top_customers_by_spending': [
                {
                    'name': customer.get_full_name(),
                    'total_spent': float(customer.total_spent),
                    'total_orders': customer.total_orders
                }
                for customer in top_customers_by_spending
            ],
            'top_customers_by_visits': [
                {
                    'name': customer.get_full_name(),
                    'total_orders': customer.total_orders,
                    'total_spent': float(customer.total_spent)
                }
                for customer in top_customers_by_visits
            ],
            'average_order_frequency': average_order_frequency,
            'customer_lifetime_value': customer_lifetime_value,
            'average_rating': feedback_data['avg_rating'] or 0,
            'feedback_count': feedback_data['count'],
            'loyalty_program_participation': loyalty_participation,
            'average_points_balance': average_points_balance
        }
        
        serializer = CustomerAnalyticsSerializer(analytics_data)
        return Response(serializer.data)


class LoyaltyProgramViewSet(viewsets.ModelViewSet):
    """
    Loyalty program management viewset.
    """
    serializer_class = LoyaltyProgramSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.OrderingFilter]
    ordering = ['name']
    
    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return LoyaltyProgram.objects.filter(restaurant=restaurant)
        return LoyaltyProgram.objects.none()


class CustomerLoyaltyViewSet(viewsets.ModelViewSet):
    """
    Customer loyalty account management viewset.
    """
    serializer_class = CustomerLoyaltySerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterFilter]
    search_fields = ['customer__first_name', 'customer__last_name', 'customer__email']
    ordering_fields = ['current_points', 'lifetime_points_earned', 'enrollment_date']
    ordering = ['-current_points']
    filterset_fields = ['loyalty_program', 'is_active', 'tier_level']
    
    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return CustomerLoyalty.objects.filter(restaurant=restaurant)
        return CustomerLoyalty.objects.none()
    
    @action(detail=True, methods=['post'])
    def redeem_points(self, request, pk=None):
        """Redeem loyalty points."""
        loyalty_account = self.get_object()
        serializer = LoyaltyRedemptionSerializer(data=request.data)
        
        if serializer.is_valid():
            points = serializer.validated_data['points_to_redeem']
            description = serializer.validated_data.get('description', 'Points redemption')
            
            if loyalty_account.redeem_points(points, description):
                return Response({
                    'message': 'Points redeemed successfully',
                    'remaining_points': loyalty_account.current_points
                })
            else:
                return Response(
                    {'error': 'Insufficient points'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def add_points(self, request, pk=None):
        """Add loyalty points."""
        loyalty_account = self.get_object()
        points = request.data.get('points', 0)
        description = request.data.get('description', 'Manual points addition')
        
        if points > 0:
            loyalty_account.add_points(points, description)
            return Response({
                'message': 'Points added successfully',
                'current_points': loyalty_account.current_points
            })
        
        return Response(
            {'error': 'Invalid points amount'},
            status=status.HTTP_400_BAD_REQUEST
        )


class CustomerFeedbackViewSet(viewsets.ModelViewSet):
    """
    Customer feedback management viewset.
    """
    serializer_class = CustomerFeedbackSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterFilter]
    search_fields = ['customer__first_name', 'customer__last_name', 'title', 'comment']
    ordering_fields = ['created_at', 'overall_rating', 'status']
    ordering = ['-created_at']
    filterset_fields = ['feedback_type', 'status', 'overall_rating']
    
    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return CustomerFeedback.objects.filter(restaurant=restaurant)
        return CustomerFeedback.objects.none()
    
    @action(detail=True, methods=['post'])
    def respond(self, request, pk=None):
        """Respond to customer feedback."""
        feedback = self.get_object()
        response_text = request.data.get('response', '')
        
        if response_text:
            feedback.response = response_text
            feedback.responded_by = request.user
            feedback.response_date = timezone.now()
            feedback.status = CustomerFeedback.Status.ACKNOWLEDGED
            feedback.save()
            
            serializer = self.get_serializer(feedback)
            return Response(serializer.data)
        
        return Response(
            {'error': 'Response text is required'},
            status=status.HTTP_400_BAD_REQUEST
        )


class CustomerGroupViewSet(viewsets.ModelViewSet):
    """
    Customer group management viewset.
    """
    serializer_class = CustomerGroupSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering = ['name']
    
    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return CustomerGroup.objects.filter(restaurant=restaurant)
        return CustomerGroup.objects.none()
    
    @action(detail=True, methods=['post'])
    def add_customers(self, request, pk=None):
        """Add customers to group."""
        group = self.get_object()
        customer_ids = request.data.get('customer_ids', [])
        
        added_count = 0
        for customer_id in customer_ids:
            try:
                customer = Customer.objects.get(
                    id=customer_id,
                    restaurant=group.restaurant
                )
                membership, created = CustomerGroupMembership.objects.get_or_create(
                    customer=customer,
                    group=group,
                    defaults={
                        'restaurant': group.restaurant,
                        'added_by': request.user,
                        'join_method': 'manual'
                    }
                )
                if created:
                    added_count += 1
            except Customer.DoesNotExist:
                continue
        
        return Response({
            'message': f'{added_count} customers added to group',
            'total_members': group.get_member_count()
        })


class MarketingCampaignViewSet(viewsets.ModelViewSet):
    """
    Marketing campaign management viewset.
    """
    serializer_class = MarketingCampaignSerializer
    permission_classes = [IsRestaurantStaff]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter, DjangoFilterFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'scheduled_date', 'sent_date']
    ordering = ['-created_at']
    filterset_fields = ['campaign_type', 'status']
    
    def get_queryset(self):
        restaurant = get_current_restaurant()
        if restaurant:
            return MarketingCampaign.objects.filter(restaurant=restaurant)
        return MarketingCampaign.objects.none()
    
    @action(detail=True, methods=['post'])
    def send_campaign(self, request, pk=None):
        """Send marketing campaign."""
        campaign = self.get_object()
        
        if campaign.status != MarketingCampaign.Status.DRAFT:
            return Response(
                {'error': 'Campaign can only be sent from draft status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculate total recipients
        total_recipients = 0
        
        # Add group members
        for group in campaign.target_groups.all():
            total_recipients += group.customers.filter(is_active=True).count()
        
        # Add individual customers
        total_recipients += campaign.target_customers.filter(is_active=True).count()
        
        # Update campaign
        campaign.status = MarketingCampaign.Status.RUNNING
        campaign.sent_date = timezone.now()
        campaign.total_recipients = total_recipients
        
        # Simulate sending (in real implementation, this would trigger actual email/SMS sending)
        if campaign.campaign_type in ['email', 'mixed']:
            campaign.emails_sent = total_recipients
        
        if campaign.campaign_type in ['sms', 'mixed']:
            campaign.sms_sent = total_recipients
            campaign.sms_delivered = int(total_recipients * 0.95)  # 95% delivery rate
        
        campaign.status = MarketingCampaign.Status.COMPLETED
        campaign.save()
        
        serializer = self.get_serializer(campaign)
        return Response(serializer.data)

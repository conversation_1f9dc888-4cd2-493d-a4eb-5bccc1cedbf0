"""
Customer-facing URLs for QR code ordering system.
"""

from django.urls import path
from . import customer_ordering_views

app_name = 'customer_ordering'

urlpatterns = [
    # Restaurant and table info
    path(
        'restaurant/<slug:restaurant_slug>/table/<str:table_number>/',
        customer_ordering_views.get_restaurant_by_table,
        name='restaurant-table-info'
    ),
    
    # Menu
    path(
        'restaurant/<slug:restaurant_slug>/menu/',
        customer_ordering_views.get_customer_menu,
        name='customer-menu'
    ),
    
    # Orders
    path(
        'restaurant/<slug:restaurant_slug>/table/<str:table_number>/order/',
        customer_ordering_views.create_customer_order,
        name='create-customer-order'
    ),
    
    path(
        'restaurant/<slug:restaurant_slug>/order/<uuid:order_id>/status/',
        customer_ordering_views.get_customer_order_status,
        name='customer-order-status'
    ),
    
    # Customer actions
    path(
        'restaurant/<slug:restaurant_slug>/table/<str:table_number>/call-waiter/',
        customer_ordering_views.call_waiter,
        name='call-waiter'
    ),
    
    path(
        'restaurant/<slug:restaurant_slug>/table/<str:table_number>/request-bill/',
        customer_ordering_views.request_bill,
        name='request-bill'
    ),
]

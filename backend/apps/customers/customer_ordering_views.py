"""
Customer-facing views for QR code ordering system.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.shortcuts import get_object_or_404
from django.db.models import Prefetch
from django.utils import timezone

from apps.restaurants.models import Restaurant, Table
from apps.menu.models import MenuItem, MenuCategory
from apps.orders.models import Order, OrderItem
from apps.menu.serializers import MenuItemSerializer, MenuCategorySerializer
from apps.orders.serializers import OrderSerializer, OrderItemSerializer


@api_view(['GET'])
@permission_classes([AllowAny])
def get_restaurant_by_table(request, restaurant_slug, table_number):
    """
    Get restaurant and table information for customer ordering.
    """
    try:
        restaurant = get_object_or_404(Restaurant, slug=restaurant_slug, status='active')
        table = get_object_or_404(Table, restaurant=restaurant, number=table_number)
        
        return Response({
            'restaurant': {
                'id': restaurant.id,
                'name': restaurant.name,
                'slug': restaurant.slug,
                'description': restaurant.description,
                'logo': restaurant.logo.url if restaurant.logo else None,
                'phone': restaurant.phone,
                'address': {
                    'line_1': restaurant.address_line_1,
                    'line_2': restaurant.address_line_2,
                    'city': restaurant.city,
                    'state': restaurant.state,
                    'postal_code': restaurant.postal_code,
                    'country': restaurant.country,
                }
            },
            'table': {
                'id': table.id,
                'number': table.number,
                'name': table.name,
                'display_name': table.display_name,
                'capacity': table.capacity,
                'section': table.section,
                'status': table.status,
            }
        })
    except Restaurant.DoesNotExist:
        return Response(
            {'error': 'Restaurant not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Table.DoesNotExist:
        return Response(
            {'error': 'Table not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def get_customer_menu(request, restaurant_slug):
    """
    Get menu for customer ordering interface.
    """
    try:
        restaurant = get_object_or_404(Restaurant, slug=restaurant_slug, status='active')
        
        # Get categories with available menu items
        categories = MenuCategory.objects.filter(
            restaurant=restaurant,
            is_active=True
        ).prefetch_related(
            Prefetch(
                'menu_items',
                queryset=MenuItem.objects.filter(
                    is_available=True,
                    is_active=True
                ).order_by('sort_order', 'name')
            )
        ).order_by('sort_order', 'name')
        
        # Serialize categories and items
        categories_data = []
        for category in categories:
            available_items = [item for item in category.menu_items.all() if item.is_available]
            if available_items:  # Only include categories with available items
                categories_data.append({
                    'id': category.id,
                    'name': category.name,
                    'description': category.description,
                    'image': category.image.url if category.image else None,
                    'items': MenuItemSerializer(available_items, many=True).data
                })
        
        return Response({
            'restaurant_name': restaurant.name,
            'categories': categories_data
        })
        
    except Restaurant.DoesNotExist:
        return Response(
            {'error': 'Restaurant not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def create_customer_order(request, restaurant_slug, table_number):
    """
    Create order from customer interface.
    """
    try:
        restaurant = get_object_or_404(Restaurant, slug=restaurant_slug, status='active')
        table = get_object_or_404(Table, restaurant=restaurant, number=table_number)
        
        # Extract order data
        data = request.data
        customer_name = data.get('customer_name', 'Customer')
        customer_phone = data.get('customer_phone', '')
        customer_email = data.get('customer_email', '')
        special_instructions = data.get('special_instructions', '')
        items_data = data.get('items', [])
        
        if not items_data:
            return Response(
                {'error': 'No items in order'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create order
        order = Order.objects.create(
            restaurant=restaurant,
            table=table,
            order_type=Order.OrderType.DINE_IN,
            status=Order.Status.PENDING,
            customer_name=customer_name,
            customer_phone=customer_phone,
            customer_email=customer_email,
            special_instructions=special_instructions,
        )
        
        # Create order items
        for item_data in items_data:
            menu_item = get_object_or_404(
                MenuItem, 
                id=item_data['menu_item_id'],
                restaurant=restaurant,
                is_available=True
            )
            
            quantity = item_data.get('quantity', 1)
            unit_price = menu_item.price
            total_price = unit_price * quantity
            
            OrderItem.objects.create(
                order=order,
                menu_item=menu_item,
                quantity=quantity,
                unit_price=unit_price,
                total_price=total_price,
                special_instructions=item_data.get('special_instructions', '')
            )
        
        # Calculate totals
        order.calculate_totals()
        order.save()
        
        # Update table status
        if table.status == Table.Status.AVAILABLE:
            table.status = Table.Status.OCCUPIED
            table.save()
        
        return Response({
            'order_id': order.id,
            'order_number': order.order_number,
            'status': order.status,
            'total_amount': str(order.total_amount),
            'message': 'Order placed successfully!'
        }, status=status.HTTP_201_CREATED)
        
    except Restaurant.DoesNotExist:
        return Response(
            {'error': 'Restaurant not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Table.DoesNotExist:
        return Response(
            {'error': 'Table not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except MenuItem.DoesNotExist:
        return Response(
            {'error': 'Menu item not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': str(e)}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def get_customer_order_status(request, restaurant_slug, order_id):
    """
    Get order status for customer tracking.
    """
    try:
        restaurant = get_object_or_404(Restaurant, slug=restaurant_slug, status='active')
        order = get_object_or_404(Order, id=order_id, restaurant=restaurant)
        
        return Response({
            'order_id': order.id,
            'order_number': order.order_number,
            'status': order.status,
            'status_display': order.get_status_display(),
            'total_amount': str(order.total_amount),
            'estimated_time': order.estimated_completion_time,
            'items': OrderItemSerializer(order.items.all(), many=True).data
        })
        
    except (Restaurant.DoesNotExist, Order.DoesNotExist):
        return Response(
            {'error': 'Order not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def call_waiter(request, restaurant_slug, table_number):
    """
    Call waiter functionality for customers.
    """
    try:
        restaurant = get_object_or_404(Restaurant, slug=restaurant_slug, status='active')
        table = get_object_or_404(Table, restaurant=restaurant, number=table_number)
        
        # Here you would typically send a notification to staff
        # For now, we'll just return a success response
        
        return Response({
            'message': f'Waiter has been notified for {table.display_name}',
            'table': table.display_name,
            'time': timezone.now().isoformat()
        })
        
    except (Restaurant.DoesNotExist, Table.DoesNotExist):
        return Response(
            {'error': 'Table not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
@permission_classes([AllowAny])
def request_bill(request, restaurant_slug, table_number):
    """
    Request bill functionality for customers.
    """
    try:
        restaurant = get_object_or_404(Restaurant, slug=restaurant_slug, status='active')
        table = get_object_or_404(Table, restaurant=restaurant, number=table_number)
        
        # Get active orders for this table
        active_orders = Order.objects.filter(
            restaurant=restaurant,
            table=table,
            status__in=[Order.Status.PENDING, Order.Status.CONFIRMED, Order.Status.PREPARING, Order.Status.READY, Order.Status.SERVED]
        )
        
        if not active_orders.exists():
            return Response(
                {'error': 'No active orders found for this table'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Here you would typically notify staff about bill request
        # For now, we'll return order summary
        
        total_amount = sum(order.total_amount for order in active_orders)
        
        return Response({
            'message': 'Bill request sent to staff',
            'table': table.display_name,
            'orders_count': active_orders.count(),
            'total_amount': str(total_amount),
            'time': timezone.now().isoformat()
        })
        
    except (Restaurant.DoesNotExist, Table.DoesNotExist):
        return Response(
            {'error': 'Table not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )

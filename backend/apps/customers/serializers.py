"""
Customer Management & CRM serializers for Restaurant POS system.
"""

from rest_framework import serializers
from .models import (
    Customer, LoyaltyProgram, CustomerLoyalty, LoyaltyTransaction,
    CustomerFeedback, CustomerGroup, CustomerGroupMembership, MarketingCampaign
)


class CustomerSerializer(serializers.ModelSerializer):
    """
    Customer serializer.
    """
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = Customer
        fields = [
            'id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'customer_type', 'is_active', 'address_line_1', 'address_line_2',
            'city', 'state', 'postal_code', 'country', 'total_orders',
            'total_spent', 'average_order_value', 'last_order_date',
            'dietary_restrictions', 'favorite_items', 'preferred_table',
            'special_notes', 'email_marketing_consent', 'sms_marketing_consent',
            'date_of_birth', 'anniversary_date', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_orders', 'total_spent', 'average_order_value',
            'last_order_date', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class LoyaltyProgramSerializer(serializers.ModelSerializer):
    """
    Loyalty program serializer.
    """
    
    class Meta:
        model = LoyaltyProgram
        fields = [
            'id', 'name', 'description', 'program_type', 'is_active',
            'points_per_dollar', 'points_redemption_value', 'visits_required',
            'spending_threshold', 'reward_description', 'reward_value',
            'minimum_order_value', 'points_expiry_days', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class CustomerLoyaltySerializer(serializers.ModelSerializer):
    """
    Customer loyalty account serializer.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    program_name = serializers.CharField(source='loyalty_program.name', read_only=True)
    
    class Meta:
        model = CustomerLoyalty
        fields = [
            'id', 'customer', 'customer_name', 'loyalty_program', 'program_name',
            'current_points', 'current_visits', 'lifetime_points_earned',
            'lifetime_points_redeemed', 'tier_level', 'tier_benefits',
            'is_active', 'enrollment_date', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'current_points', 'current_visits', 'lifetime_points_earned',
            'lifetime_points_redeemed', 'enrollment_date', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)


class LoyaltyTransactionSerializer(serializers.ModelSerializer):
    """
    Loyalty transaction serializer.
    """
    customer_name = serializers.CharField(source='customer_loyalty.customer.get_full_name', read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)
    
    class Meta:
        model = LoyaltyTransaction
        fields = [
            'id', 'customer_loyalty', 'customer_name', 'transaction_type',
            'points', 'description', 'order', 'processed_by', 'processed_by_name',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['processed_by'] = self.context['request'].user
        return super().create(validated_data)


class CustomerFeedbackSerializer(serializers.ModelSerializer):
    """
    Customer feedback serializer.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    responded_by_name = serializers.CharField(source='responded_by.get_full_name', read_only=True)
    
    class Meta:
        model = CustomerFeedback
        fields = [
            'id', 'customer', 'customer_name', 'feedback_type', 'status',
            'overall_rating', 'food_rating', 'service_rating', 'ambiance_rating',
            'title', 'comment', 'order', 'response', 'responded_by',
            'responded_by_name', 'response_date', 'follow_up_required',
            'follow_up_date', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'response_date', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # If response is being added, set response date and user
        if 'response' in validated_data and validated_data['response'] and not instance.response:
            validated_data['response_date'] = timezone.now()
            validated_data['responded_by'] = self.context['request'].user
        
        return super().update(instance, validated_data)


class CustomerGroupSerializer(serializers.ModelSerializer):
    """
    Customer group serializer.
    """
    member_count = serializers.IntegerField(source='get_member_count', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = CustomerGroup
        fields = [
            'id', 'name', 'description', 'criteria', 'is_active',
            'email_template', 'sms_template', 'member_count',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class CustomerGroupMembershipSerializer(serializers.ModelSerializer):
    """
    Customer group membership serializer.
    """
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    group_name = serializers.CharField(source='group.name', read_only=True)
    added_by_name = serializers.CharField(source='added_by.get_full_name', read_only=True)
    
    class Meta:
        model = CustomerGroupMembership
        fields = [
            'id', 'customer', 'customer_name', 'group', 'group_name',
            'joined_date', 'is_active', 'join_method', 'added_by',
            'added_by_name', 'created_at'
        ]
        read_only_fields = ['id', 'joined_date', 'created_at']

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['added_by'] = self.context['request'].user
        return super().create(validated_data)


class MarketingCampaignSerializer(serializers.ModelSerializer):
    """
    Marketing campaign serializer.
    """
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    target_groups_names = serializers.StringRelatedField(source='target_groups', many=True, read_only=True)
    
    class Meta:
        model = MarketingCampaign
        fields = [
            'id', 'name', 'description', 'campaign_type', 'status',
            'target_groups', 'target_groups_names', 'target_customers',
            'subject', 'email_content', 'sms_content', 'scheduled_date',
            'sent_date', 'total_recipients', 'emails_sent', 'emails_opened',
            'emails_clicked', 'sms_sent', 'sms_delivered', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'sent_date', 'total_recipients', 'emails_sent',
            'emails_opened', 'emails_clicked', 'sms_sent', 'sms_delivered',
            'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        from apps.core.middleware import get_current_restaurant
        restaurant = get_current_restaurant()
        if restaurant:
            validated_data['restaurant'] = restaurant
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class CustomerAnalyticsSerializer(serializers.Serializer):
    """
    Customer analytics data serializer.
    """
    total_customers = serializers.IntegerField()
    new_customers_this_month = serializers.IntegerField()
    returning_customers = serializers.IntegerField()
    average_customer_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    customer_retention_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    
    # Customer segments
    customer_segments = serializers.ListField()
    
    # Top customers
    top_customers_by_spending = serializers.ListField()
    top_customers_by_visits = serializers.ListField()
    
    # Customer behavior
    average_order_frequency = serializers.DecimalField(max_digits=5, decimal_places=2)
    customer_lifetime_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    # Feedback metrics
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    feedback_count = serializers.IntegerField()
    
    # Loyalty metrics
    loyalty_program_participation = serializers.DecimalField(max_digits=5, decimal_places=2)
    average_points_balance = serializers.DecimalField(max_digits=10, decimal_places=2)


class LoyaltyRedemptionSerializer(serializers.Serializer):
    """
    Serializer for loyalty points redemption.
    """
    customer_loyalty_id = serializers.UUIDField()
    points_to_redeem = serializers.IntegerField(min_value=1)
    description = serializers.CharField(max_length=200, required=False)
    
    def validate(self, data):
        try:
            customer_loyalty = CustomerLoyalty.objects.get(
                id=data['customer_loyalty_id']
            )
            if customer_loyalty.current_points < data['points_to_redeem']:
                raise serializers.ValidationError(
                    "Insufficient points balance"
                )
        except CustomerLoyalty.DoesNotExist:
            raise serializers.ValidationError(
                "Customer loyalty account not found"
            )
        
        return data

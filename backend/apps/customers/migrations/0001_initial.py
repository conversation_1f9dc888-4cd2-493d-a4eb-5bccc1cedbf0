# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("restaurants", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomerGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "criteria",
                    models.JSONField(
                        default=dict,
                        help_text="JSON criteria for automatic group membership",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("email_template", models.TextField(blank=True)),
                ("sms_template", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Customer Group",
                "verbose_name_plural": "Customer Groups",
                "db_table": "customer_groups",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="CustomerGroupMembership",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("joined_date", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "join_method",
                    models.CharField(
                        choices=[
                            ("manual", "Manual Addition"),
                            ("automatic", "Automatic Criteria"),
                            ("import", "Data Import"),
                            ("signup", "Customer Signup"),
                        ],
                        default="manual",
                        max_length=50,
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer Group Membership",
                "verbose_name_plural": "Customer Group Memberships",
                "db_table": "customer_group_memberships",
                "ordering": ["-joined_date"],
            },
        ),
        migrations.CreateModel(
            name="CustomerLoyalty",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("current_points", models.PositiveIntegerField(default=0)),
                ("current_visits", models.PositiveIntegerField(default=0)),
                ("lifetime_points_earned", models.PositiveIntegerField(default=0)),
                ("lifetime_points_redeemed", models.PositiveIntegerField(default=0)),
                ("tier_level", models.CharField(default="Bronze", max_length=50)),
                ("tier_benefits", models.JSONField(blank=True, default=list)),
                ("is_active", models.BooleanField(default=True)),
                ("enrollment_date", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Customer Loyalty Account",
                "verbose_name_plural": "Customer Loyalty Accounts",
                "db_table": "customer_loyalty",
            },
        ),
        migrations.CreateModel(
            name="LoyaltyProgram",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "program_type",
                    models.CharField(
                        choices=[
                            ("points", "Points Based"),
                            ("visits", "Visit Based"),
                            ("spending", "Spending Based"),
                        ],
                        default="points",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "points_per_dollar",
                    models.DecimalField(
                        decimal_places=2,
                        default=1,
                        help_text="Points earned per dollar spent",
                        max_digits=5,
                    ),
                ),
                (
                    "points_redemption_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.01,
                        help_text="Dollar value per point when redeeming",
                        max_digits=5,
                    ),
                ),
                (
                    "visits_required",
                    models.PositiveIntegerField(
                        default=10, help_text="Number of visits required for reward"
                    ),
                ),
                (
                    "spending_threshold",
                    models.DecimalField(
                        decimal_places=2,
                        default=100,
                        help_text="Spending amount required for reward",
                        max_digits=10,
                    ),
                ),
                ("reward_description", models.CharField(max_length=200)),
                (
                    "reward_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "minimum_order_value",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Minimum order value to earn points/visits",
                        max_digits=10,
                    ),
                ),
                (
                    "points_expiry_days",
                    models.PositiveIntegerField(
                        default=365,
                        help_text="Days after which points expire (0 = never expire)",
                    ),
                ),
            ],
            options={
                "verbose_name": "Loyalty Program",
                "verbose_name_plural": "Loyalty Programs",
                "db_table": "loyalty_programs",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="LoyaltyTransaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("earned", "Points Earned"),
                            ("redeemed", "Points Redeemed"),
                            ("expired", "Points Expired"),
                            ("adjusted", "Manual Adjustment"),
                        ],
                        max_length=20,
                    ),
                ),
                ("points", models.IntegerField()),
                ("description", models.CharField(blank=True, max_length=200)),
            ],
            options={
                "verbose_name": "Loyalty Transaction",
                "verbose_name_plural": "Loyalty Transactions",
                "db_table": "loyalty_transactions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MarketingCampaign",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "campaign_type",
                    models.CharField(
                        choices=[
                            ("email", "Email Campaign"),
                            ("sms", "SMS Campaign"),
                            ("push", "Push Notification"),
                            ("mixed", "Mixed Campaign"),
                        ],
                        default="email",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("scheduled", "Scheduled"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("subject", models.CharField(blank=True, max_length=200)),
                ("email_content", models.TextField(blank=True)),
                ("sms_content", models.TextField(blank=True)),
                ("scheduled_date", models.DateTimeField(blank=True, null=True)),
                ("sent_date", models.DateTimeField(blank=True, null=True)),
                ("total_recipients", models.PositiveIntegerField(default=0)),
                ("emails_sent", models.PositiveIntegerField(default=0)),
                ("emails_opened", models.PositiveIntegerField(default=0)),
                ("emails_clicked", models.PositiveIntegerField(default=0)),
                ("sms_sent", models.PositiveIntegerField(default=0)),
                ("sms_delivered", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "Marketing Campaign",
                "verbose_name_plural": "Marketing Campaigns",
                "db_table": "marketing_campaigns",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Customer",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("first_name", models.CharField(max_length=100)),
                ("last_name", models.CharField(max_length=100)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("phone", models.CharField(blank=True, max_length=20)),
                (
                    "customer_type",
                    models.CharField(
                        choices=[
                            ("walk_in", "Walk-in Customer"),
                            ("regular", "Regular Customer"),
                            ("vip", "VIP Customer"),
                            ("corporate", "Corporate Customer"),
                        ],
                        default="walk_in",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("address_line_1", models.CharField(blank=True, max_length=200)),
                ("address_line_2", models.CharField(blank=True, max_length=200)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("state", models.CharField(blank=True, max_length=100)),
                ("postal_code", models.CharField(blank=True, max_length=20)),
                ("country", models.CharField(blank=True, max_length=100)),
                ("total_orders", models.PositiveIntegerField(default=0)),
                (
                    "total_spent",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "average_order_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("last_order_date", models.DateTimeField(blank=True, null=True)),
                ("dietary_restrictions", models.JSONField(blank=True, default=list)),
                ("favorite_items", models.JSONField(blank=True, default=list)),
                ("preferred_table", models.CharField(blank=True, max_length=50)),
                ("special_notes", models.TextField(blank=True)),
                ("email_marketing_consent", models.BooleanField(default=False)),
                ("sms_marketing_consent", models.BooleanField(default=False)),
                ("date_of_birth", models.DateField(blank=True, null=True)),
                ("anniversary_date", models.DateField(blank=True, null=True)),
                (
                    "restaurant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_set",
                        to="restaurants.restaurant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer",
                "verbose_name_plural": "Customers",
                "db_table": "customers",
                "ordering": ["last_name", "first_name"],
            },
        ),
        migrations.CreateModel(
            name="CustomerFeedback",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "feedback_type",
                    models.CharField(
                        choices=[
                            ("review", "Review"),
                            ("complaint", "Complaint"),
                            ("suggestion", "Suggestion"),
                            ("compliment", "Compliment"),
                        ],
                        default="review",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("closed", "Closed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "overall_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "food_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "service_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "ambiance_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=200)),
                ("comment", models.TextField()),
                ("response", models.TextField(blank=True)),
                ("response_date", models.DateTimeField(blank=True, null=True)),
                ("follow_up_required", models.BooleanField(default=False)),
                ("follow_up_date", models.DateTimeField(blank=True, null=True)),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedback",
                        to="customers.customer",
                    ),
                ),
            ],
            options={
                "verbose_name": "Customer Feedback",
                "verbose_name_plural": "Customer Feedback",
                "db_table": "customer_feedback",
                "ordering": ["-created_at"],
            },
        ),
    ]

# Generated by Django 5.2.6 on 2025-09-11 07:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("customers", "0002_initial"),
        ("orders", "0001_initial"),
        ("restaurants", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="customerfeedback",
            name="responded_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="feedback_responses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="customerfeedback",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="customergroup",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_customer_groups",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="customergroup",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="customergroupmembership",
            name="added_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="added_group_memberships",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="customergroupmembership",
            name="customer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="customers.customer"
            ),
        ),
        migrations.AddField(
            model_name="customergroupmembership",
            name="group",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="customers.customergroup",
            ),
        ),
        migrations.AddField(
            model_name="customergroupmembership",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="customergroup",
            name="customers",
            field=models.ManyToManyField(
                related_name="groups",
                through="customers.CustomerGroupMembership",
                to="customers.customer",
            ),
        ),
        migrations.AddField(
            model_name="customerloyalty",
            name="customer",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="loyalty_account",
                to="customers.customer",
            ),
        ),
        migrations.AddField(
            model_name="customerloyalty",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="loyaltyprogram",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="customerloyalty",
            name="loyalty_program",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="customer_accounts",
                to="customers.loyaltyprogram",
            ),
        ),
        migrations.AddField(
            model_name="loyaltytransaction",
            name="customer_loyalty",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transactions",
                to="customers.customerloyalty",
            ),
        ),
        migrations.AddField(
            model_name="loyaltytransaction",
            name="order",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="loyalty_transactions",
                to="orders.order",
            ),
        ),
        migrations.AddField(
            model_name="loyaltytransaction",
            name="processed_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="processed_loyalty_transactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="loyaltytransaction",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="marketingcampaign",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_campaigns",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="marketingcampaign",
            name="restaurant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_set",
                to="restaurants.restaurant",
            ),
        ),
        migrations.AddField(
            model_name="marketingcampaign",
            name="target_customers",
            field=models.ManyToManyField(
                blank=True, related_name="campaigns", to="customers.customer"
            ),
        ),
        migrations.AddField(
            model_name="marketingcampaign",
            name="target_groups",
            field=models.ManyToManyField(
                blank=True, related_name="campaigns", to="customers.customergroup"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="customer",
            unique_together={("restaurant", "email")},
        ),
        migrations.AlterUniqueTogether(
            name="customergroupmembership",
            unique_together={("customer", "group")},
        ),
        migrations.AlterUniqueTogether(
            name="customerloyalty",
            unique_together={("customer", "loyalty_program")},
        ),
    ]

// Initialize MongoDB for Restaurant POS analytics

// Switch to the analytics database
db = db.getSiblingDB('restaurant_pos_analytics');

// Create collections with indexes for better performance
db.createCollection('sales_analytics');
db.createCollection('order_analytics');
db.createCollection('inventory_analytics');
db.createCollection('customer_analytics');
db.createCollection('staff_analytics');

// Create indexes for sales analytics
db.sales_analytics.createIndex({ "restaurant_id": 1, "date": -1 });
db.sales_analytics.createIndex({ "restaurant_id": 1, "created_at": -1 });

// Create indexes for order analytics
db.order_analytics.createIndex({ "restaurant_id": 1, "order_date": -1 });
db.order_analytics.createIndex({ "restaurant_id": 1, "status": 1 });
db.order_analytics.createIndex({ "restaurant_id": 1, "table_id": 1 });

// Create indexes for inventory analytics
db.inventory_analytics.createIndex({ "restaurant_id": 1, "item_id": 1 });
db.inventory_analytics.createIndex({ "restaurant_id": 1, "date": -1 });

// Create indexes for customer analytics
db.customer_analytics.createIndex({ "restaurant_id": 1, "customer_id": 1 });
db.customer_analytics.createIndex({ "restaurant_id": 1, "visit_date": -1 });

// Create indexes for staff analytics
db.staff_analytics.createIndex({ "restaurant_id": 1, "staff_id": 1 });
db.staff_analytics.createIndex({ "restaurant_id": 1, "shift_date": -1 });

print('MongoDB initialized successfully for Restaurant POS analytics');

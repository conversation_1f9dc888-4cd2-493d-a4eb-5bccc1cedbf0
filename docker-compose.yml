version: "3.8"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: restaurant_pos_postgres
    environment:
      POSTGRES_DB: restaurant_pos
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - restaurant_pos_network

  # MongoDB Database
  mongodb:
    image: mongo:7
    container_name: restaurant_pos_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: restaurant_pos_analytics
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init.js:/docker-entrypoint-initdb.d/init.js
    ports:
      - "27017:27017"
    networks:
      - restaurant_pos_network

  # Redis for caching and Celery
  redis:
    image: redis:7-alpine
    container_name: restaurant_pos_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - restaurant_pos_network

  # Django Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: restaurant_pos_backend
    environment:
      - DEBUG=1
      - DATABASE_URL=***********************************************/restaurant_pos
      - MONGODB_URL=********************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
      - backend_media:/app/media
      - backend_static:/app/staticfiles
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - mongodb
      - redis
    networks:
      - restaurant_pos_network
    command: python manage.py runserver 0.0.0.0:8000

  # Celery Worker
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: restaurant_pos_celery
    environment:
      - DEBUG=1
      - DATABASE_URL=***********************************************/restaurant_pos
      - MONGODB_URL=********************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - mongodb
      - redis
    networks:
      - restaurant_pos_network
    command: celery -A config worker -l info

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: restaurant_pos_celery_beat
    environment:
      - DEBUG=1
      - DATABASE_URL=***********************************************/restaurant_pos
      - MONGODB_URL=********************************************************************************
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - mongodb
      - redis
    networks:
      - restaurant_pos_network
    command: celery -A config beat -l info

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: restaurant_pos_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - REACT_APP_WS_URL=ws://localhost:8000/ws
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - restaurant_pos_network
    command: npm start

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: restaurant_pos_nginx
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - backend_static:/var/www/static
      - backend_media:/var/www/media
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - restaurant_pos_network
    profiles:
      - production

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  backend_media:
  backend_static:

networks:
  restaurant_pos_network:
    driver: bridge

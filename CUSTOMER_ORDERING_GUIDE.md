# Customer QR Code Ordering System

This guide explains the implementation of the customer-facing QR code ordering system for the Restaurant POS.

## Overview

The customer ordering system allows customers to:
1. Scan QR codes at restaurant tables
2. Browse the menu on their mobile devices
3. Place orders directly from their table
4. Track order status in real-time
5. Call waiters and request bills

## Architecture

### Backend Components

#### 1. Models (Updated)
- **Table Model**: Added `qr_code_url` field for customer ordering URLs
- **Order Model**: Existing model handles customer orders
- **MenuItem Model**: Existing model provides menu data

#### 2. Customer API Views (`apps/customers/customer_ordering_views.py`)
- `get_restaurant_by_table`: Get restaurant and table info
- `get_customer_menu`: Fetch available menu items
- `create_customer_order`: Place orders from customer interface
- `get_customer_order_status`: Track order status
- `call_waiter`: Notify staff for assistance
- `request_bill`: Request bill from staff

#### 3. QR Code Generation (`apps/restaurants/utils.py`)
- `generate_table_qr_code`: Create QR codes for tables
- `regenerate_all_table_qr_codes`: Bulk QR code generation

#### 4. Management Commands
- `generate_qr_codes`: Django command to generate QR codes for tables

### Frontend Components

#### 1. Customer Ordering Interface (`frontend/src/pages/customer/`)
- **CustomerOrderingPage**: Main ordering interface
- **OrderStatusPage**: Order tracking interface

#### 2. Customer API Client (`frontend/src/lib/customerAPI.ts`)
- API functions for customer-facing operations
- No authentication required for customer endpoints

## Features

### 1. QR Code Generation
- Automatic QR code generation for each table
- QR codes link to customer ordering interface
- Admin interface for QR code management

### 2. Mobile-Optimized Menu
- Responsive design for mobile devices
- Category-based menu navigation
- Item details with images, descriptions, and pricing
- Real-time availability status

### 3. Shopping Cart
- Add/remove items from cart
- Quantity adjustments
- Special instructions for items
- Order total calculation

### 4. Customer Information
- Optional customer name and contact info
- Order confirmation and tracking

### 5. Order Tracking
- Real-time order status updates
- Progress visualization
- Estimated completion times

### 6. Customer Actions
- Call waiter button
- Request bill functionality
- Order history access

## API Endpoints

### Customer-Facing Endpoints (No Authentication Required)

```
GET /api/customer/restaurant/{slug}/table/{number}/
- Get restaurant and table information

GET /api/customer/restaurant/{slug}/menu/
- Get customer menu with available items

POST /api/customer/restaurant/{slug}/table/{number}/order/
- Create new customer order

GET /api/customer/restaurant/{slug}/order/{order_id}/status/
- Get order status and tracking info

POST /api/customer/restaurant/{slug}/table/{number}/call-waiter/
- Call waiter for assistance

POST /api/customer/restaurant/{slug}/table/{number}/request-bill/
- Request bill from staff
```

## Setup Instructions

### 1. Install Dependencies

Add to `backend/requirements/base.txt`:
```
qrcode[pil]==7.4.2
```

### 2. Run Migrations

```bash
python manage.py migrate restaurants
```

### 3. Generate QR Codes

```bash
# Generate QR codes for all restaurants
python manage.py generate_qr_codes

# Generate for specific restaurant
python manage.py generate_qr_codes --restaurant restaurant-slug

# Force regenerate existing QR codes
python manage.py generate_qr_codes --force

# Custom base URL
python manage.py generate_qr_codes --base-url https://yourdomain.com
```

### 4. Admin Configuration

1. Access Django Admin at `/admin/`
2. Go to Restaurants → Tables
3. View and manage QR codes for each table
4. Download or regenerate QR codes as needed

## Customer Flow

### 1. QR Code Scan
1. Customer scans QR code at table
2. Redirected to: `/order/{restaurant-slug}/table/{table-number}`
3. Restaurant and table info loaded automatically

### 2. Menu Browsing
1. Mobile-optimized menu interface loads
2. Categories displayed in sidebar
3. Menu items shown with images and details
4. Real-time availability status

### 3. Ordering Process
1. Customer adds items to cart
2. Adjusts quantities and adds special instructions
3. Provides name and optional contact info
4. Places order with confirmation

### 4. Order Tracking
1. Redirected to order status page
2. Real-time updates on order progress
3. Estimated completion time displayed
4. Progress visualization with status steps

### 5. Additional Actions
1. Call waiter button for assistance
2. Request bill when ready to pay
3. View order history and details

## URL Structure

### Customer-Facing URLs
- `/order/{restaurant-slug}/table/{table-number}` - Main ordering interface
- `/order/{restaurant-slug}/order/{order-id}/status` - Order tracking

### QR Code URLs
QR codes generate URLs in the format:
```
https://yourdomain.com/order/{restaurant-slug}/table/{table-number}
```

## Customization

### 1. Styling
- Customer interface uses Tailwind CSS
- Mobile-first responsive design
- Customizable color scheme and branding

### 2. Features
- Configurable customer information requirements
- Optional order modifications
- Customizable order status messages

### 3. Integration
- Integrates with existing POS system
- Kitchen display system shows customer orders
- Staff notifications for waiter calls and bill requests

## Testing

### 1. QR Code Generation Test
```bash
cd backend
python test_qr_generation.py
```

### 2. API Testing
Use tools like Postman or curl to test customer API endpoints:

```bash
# Get restaurant info
curl http://localhost:8000/api/customer/restaurant/test-restaurant/table/1/

# Get menu
curl http://localhost:8000/api/customer/restaurant/test-restaurant/menu/
```

### 3. Frontend Testing
1. Start the development server
2. Navigate to customer ordering URL
3. Test the complete ordering flow

## Production Deployment

### 1. Environment Variables
```
CUSTOMER_INTERFACE_URL=https://yourdomain.com
```

### 2. QR Code Storage
- Configure proper media storage (AWS S3, etc.)
- Ensure QR code images are publicly accessible
- Set up CDN for fast image delivery

### 3. Performance Considerations
- Enable caching for menu data
- Optimize images for mobile devices
- Use WebSocket for real-time order updates

## Security Considerations

### 1. Public Endpoints
- Customer endpoints are public (no authentication)
- Validate all input data
- Rate limiting recommended

### 2. Data Privacy
- Minimal customer data collection
- Optional contact information
- GDPR compliance considerations

### 3. Order Validation
- Verify restaurant and table existence
- Check menu item availability
- Validate order totals

## Troubleshooting

### Common Issues

1. **QR Code Not Generating**
   - Check qrcode library installation
   - Verify media storage configuration
   - Check file permissions

2. **Customer Interface Not Loading**
   - Verify frontend routes are configured
   - Check API endpoint accessibility
   - Validate restaurant slug and table number

3. **Orders Not Appearing in POS**
   - Check database connectivity
   - Verify order creation API
   - Check kitchen display integration

### Debug Commands

```bash
# Check QR code generation
python manage.py generate_qr_codes --restaurant test-restaurant

# Verify API endpoints
python manage.py shell
>>> from apps.restaurants.models import Restaurant, Table
>>> restaurant = Restaurant.objects.first()
>>> table = restaurant.tables.first()
>>> print(table.qr_code_url)
```

## Future Enhancements

### Planned Features
1. Customer loyalty program integration
2. Payment processing through customer interface
3. Table reservation system
4. Multi-language support
5. Offline ordering capability
6. Push notifications for order updates
7. Customer feedback and rating system
8. Social media integration
9. Promotional offers and discounts
10. Analytics and customer insights

This customer ordering system provides a complete contactless dining experience similar to DiningPlus and other modern restaurant management systems.

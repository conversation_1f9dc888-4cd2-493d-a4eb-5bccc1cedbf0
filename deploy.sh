#!/bin/bash

# Production deployment script for Restaurant POS
set -e

echo "🚀 Starting Restaurant POS Production Deployment"

# Check if .env.production exists
if [ ! -f .env.production ]; then
    echo "❌ Error: .env.production file not found!"
    echo "Please copy .env.production.example to .env.production and configure it."
    exit 1
fi

# Load environment variables
export $(cat .env.production | grep -v '^#' | xargs)

echo "📋 Pre-deployment checks..."

# Check required environment variables
required_vars=("SECRET_KEY" "DB_PASSWORD" "ALLOWED_HOSTS")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: $var is not set in .env.production"
        exit 1
    fi
done

echo "✅ Environment variables validated"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p backups
mkdir -p nginx/ssl

# Generate SSL certificates if they don't exist (self-signed for testing)
if [ ! -f nginx/ssl/cert.pem ] || [ ! -f nginx/ssl/key.pem ]; then
    echo "🔐 Generating self-signed SSL certificates..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/key.pem \
        -out nginx/ssl/cert.pem \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    echo "⚠️  Note: Using self-signed certificates. Replace with real certificates for production."
fi

# Build and start services
echo "🏗️  Building Docker images..."
docker-compose -f docker-compose.prod.yml build --no-cache

echo "🗄️  Starting database and Redis..."
docker-compose -f docker-compose.prod.yml up -d db redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🔄 Running database migrations..."
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py migrate

# Create superuser if it doesn't exist
echo "👤 Creating superuser..."
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

# Collect static files
echo "📦 Collecting static files..."
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py collectstatic --noinput

# Generate QR codes for existing tables
echo "🔗 Generating QR codes..."
docker-compose -f docker-compose.prod.yml run --rm backend python manage.py generate_qr_codes --base-url="$CUSTOMER_INTERFACE_URL"

# Start all services
echo "🚀 Starting all services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Health checks
echo "🏥 Running health checks..."
services=("backend:8000" "frontend:80")
for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if curl -f "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "✅ $name is healthy"
    else
        echo "❌ $name health check failed"
    fi
done

# Show running services
echo "📊 Service status:"
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Access Information:"
echo "   🌐 Frontend: https://localhost (or your domain)"
echo "   🔧 Admin Panel: https://localhost/admin"
echo "   📱 Customer Ordering: https://localhost/order/{restaurant-slug}/table/{table-number}"
echo "   👨‍💼 Default Admin: admin / admin123"
echo ""
echo "📝 Next Steps:"
echo "   1. Update your domain DNS to point to this server"
echo "   2. Replace self-signed SSL certificates with real ones"
echo "   3. Configure payment gateways (Stripe, PayPal)"
echo "   4. Set up monitoring (Sentry)"
echo "   5. Configure email settings"
echo "   6. Set up automated backups"
echo ""
echo "📚 Useful Commands:"
echo "   View logs: docker-compose -f docker-compose.prod.yml logs -f [service]"
echo "   Restart: docker-compose -f docker-compose.prod.yml restart [service]"
echo "   Stop: docker-compose -f docker-compose.prod.yml down"
echo "   Backup DB: docker-compose -f docker-compose.prod.yml exec db pg_dump -U postgres restaurant_pos_prod > backup.sql"
echo ""
echo "🔒 Security Reminders:"
echo "   - Change default admin password"
echo "   - Use strong SECRET_KEY"
echo "   - Enable firewall (UFW recommended)"
echo "   - Regular security updates"
echo "   - Monitor logs for suspicious activity"

import React from 'react';
import { Button } from '../ui/Button';

interface Restaurant {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  subscription_plan: string;
  is_active: boolean;
}

interface RestaurantListProps {
  restaurants: Restaurant[];
  onEdit: (restaurant: Restaurant) => void;
  onDelete: (id: string) => void;
}

export const RestaurantList: React.FC<RestaurantListProps> = ({
  restaurants,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <ul className="divide-y divide-gray-200">
        {restaurants.map((restaurant) => (
          <li key={restaurant.id}>
            <div className="px-4 py-4 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900">
                    {restaurant.name}
                  </h3>
                  <p className="text-sm text-gray-500">{restaurant.email}</p>
                  <p className="text-sm text-gray-500">{restaurant.phone}</p>
                  <p className="text-sm text-gray-500">{restaurant.address}</p>
                  <div className="mt-2 flex items-center space-x-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      restaurant.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {restaurant.is_active ? 'Active' : 'Inactive'}
                    </span>
                    <span className="text-sm text-gray-500">
                      Plan: {restaurant.subscription_plan}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(restaurant)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => onDelete(restaurant.id)}
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
      {restaurants.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No restaurants found.</p>
        </div>
      )}
    </div>
  );
};
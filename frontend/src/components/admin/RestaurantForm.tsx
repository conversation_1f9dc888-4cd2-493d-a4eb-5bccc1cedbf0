import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { TextArea } from '../ui/TextArea';

interface RestaurantFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export const RestaurantForm: React.FC<RestaurantFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: initialData || {
      name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      country: '',
      postal_code: '',
      subscription_plan: 'BASIC',
      is_active: true,
    },
  });

  const subscriptionPlans = [
    { value: 'BASIC', label: 'Basic' },
    { value: 'STANDARD', label: 'Standard' },
    { value: 'PREMIUM', label: 'Premium' },
    { value: 'ENTERPRISE', label: 'Enterprise' },
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <Input
          label="Restaurant Name"
          {...register('name', { required: 'Restaurant name is required' })}
          error={errors.name?.message}
        />
        <Input
          label="Email"
          type="email"
          {...register('email', { required: 'Email is required' })}
          error={errors.email?.message}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Input
          label="Phone"
          {...register('phone', { required: 'Phone is required' })}
          error={errors.phone?.message}
        />
        <Select
          label="Subscription Plan"
          {...register('subscription_plan')}
          options={subscriptionPlans}
        />
      </div>

      <TextArea
        label="Address"
        {...register('address', { required: 'Address is required' })}
        error={errors.address?.message}
      />

      <div className="grid grid-cols-3 gap-4">
        <Input
          label="City"
          {...register('city', { required: 'City is required' })}
          error={errors.city?.message}
        />
        <Input
          label="State"
          {...register('state', { required: 'State is required' })}
          error={errors.state?.message}
        />
        <Input
          label="Postal Code"
          {...register('postal_code')}
        />
      </div>

      <Input
        label="Country"
        {...register('country', { required: 'Country is required' })}
        error={errors.country?.message}
      />

      <div className="flex justify-end space-x-3 pt-4">
        <Button type="button" variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : initialData ? 'Update' : 'Create'}
        </Button>
      </div>
    </form>
  );
};
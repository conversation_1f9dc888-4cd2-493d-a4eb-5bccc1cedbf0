/**
 * Menu Item Card component for POS system
 */

import React from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: string;
  category_name: string;
  is_available: boolean;
  is_featured: boolean;
  image?: string;
  calories?: number;
  allergens: string[];
  dietary_info: string[];
  prep_time_minutes: number;
  is_in_stock: boolean;
}

interface MenuItemCardProps {
  item: MenuItem;
  onAddToCart: (item: MenuItem) => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = ({ item, onAddToCart }) => {
  const handleAddToCart = () => {
    if (item.is_available && item.is_in_stock) {
      onAddToCart(item);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow group ${!item.is_available || !item.is_in_stock ? 'opacity-50' : ''}`}>
      <div className="mb-4">
        {item.image ? (
          <img
            src={item.image}
            alt={item.name}
            className="w-full h-32 sm:h-40 object-cover rounded-lg"
          />
        ) : (
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center h-32 sm:h-40">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-blue-600 font-bold text-lg">{item.name.charAt(0)}</span>
              </div>
              <span className="text-gray-500 text-sm">{item.category_name}</span>
            </div>
          </div>
        )}
      </div>

      <div className="flex-1">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-sm sm:text-base leading-tight truncate">
              {item.name}
            </h3>
            {item.is_featured && (
              <span className="mt-1 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                Featured
              </span>
            )}
          </div>
          <span className="text-lg font-bold text-blue-600 ml-2 flex-shrink-0">${item.price}</span>
        </div>

        <p className="text-gray-600 text-xs sm:text-sm mb-3 line-clamp-2">{item.description}</p>

        <div className="flex items-center justify-between">
          <div className="flex flex-col text-xs text-gray-500">
            {item.prep_time_minutes > 0 && (
              <span>⏱️ {item.prep_time_minutes} min</span>
            )}
            {item.calories && (
              <span>🔥 {item.calories} cal</span>
            )}
          </div>

          <button
            onClick={handleAddToCart}
            disabled={!item.is_available || !item.is_in_stock}
            className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              !item.is_available || !item.is_in_stock
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
            }`}
          >
            <PlusIcon className="w-4 h-4 mr-1" />
            Add
          </button>
        </div>

        {!item.is_in_stock && (
          <div className="mt-2 text-xs text-red-600 font-medium">Out of Stock</div>
        )}

        {item.allergens.length > 0 && (
          <div className="mt-2 text-xs text-orange-600">
            ⚠️ Contains: {item.allergens.join(', ')}
          </div>
        )}
      </div>
    </div>
  );
};

export default MenuItemCard;

import React from 'react';
import { Clock, Users, Timer, AlertTriangle } from 'lucide-react';

interface OrderItem {
  id: string;
  menu_item_name: string;
  quantity: number;
  special_instructions: string;
  kitchen_status: 'pending' | 'preparing' | 'ready' | 'served';
  modifiers: Array<{
    id: string;
    modifier_name: string;
    quantity: number;
  }>;
}

interface KitchenOrder {
  id: string;
  order_number: string;
  order_type: string;
  table_name?: string;
  customer_name_display: string;
  status: string;
  confirmed_time: string;
  preparation_time_minutes: number;
  special_instructions: string;
  items: OrderItem[];
}

interface OrderCardProps {
  order: KitchenOrder;
  onUpdateItemStatus: (itemId: string, status: string) => void;
  onUpdateOrderStatus: (orderId: string, status: string) => void;
}

const OrderCard: React.FC<OrderCardProps> = ({
  order,
  onUpdateItemStatus,
  onUpdateOrderStatus,
}) => {
  const getOrderPriority = () => {
    const confirmedTime = new Date(order.confirmed_time);
    const now = new Date();
    const elapsedMinutes = (now.getTime() - confirmedTime.getTime()) / (1000 * 60);
    
    if (elapsedMinutes > order.preparation_time_minutes + 10) return 'urgent';
    if (elapsedMinutes > order.preparation_time_minutes) return 'overdue';
    return 'normal';
  };

  const formatElapsedTime = () => {
    const confirmed = new Date(order.confirmed_time);
    const now = new Date();
    const elapsedMinutes = Math.floor((now.getTime() - confirmed.getTime()) / (1000 * 60));
    
    if (elapsedMinutes < 60) {
      return `${elapsedMinutes}m`;
    } else {
      const hours = Math.floor(elapsedMinutes / 60);
      const minutes = elapsedMinutes % 60;
      return `${hours}h ${minutes}m`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready': return 'bg-green-100 text-green-800 border-green-200';
      case 'served': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = () => {
    const priority = getOrderPriority();
    switch (priority) {
      case 'urgent': return 'border-l-4 border-red-500 bg-red-50';
      case 'overdue': return 'border-l-4 border-orange-500 bg-orange-50';
      default: return 'border-l-4 border-gray-300 bg-white';
    }
  };

  const getOrderTypeColor = () => {
    switch (order.order_type) {
      case 'dine_in': return 'bg-blue-100 text-blue-800';
      case 'takeaway': return 'bg-green-100 text-green-800';
      case 'delivery': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const priority = getOrderPriority();
  const elapsedTime = formatElapsedTime();
  const allItemsReady = order.items.every(item => item.kitchen_status === 'ready');

  return (
    <div className={`rounded-lg shadow-sm border ${getPriorityColor()} transition-all hover:shadow-md`}>
      {/* Order Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-bold text-lg text-gray-900">
            #{order.order_number}
          </h3>
          <div className="flex items-center space-x-2">
            {priority === 'urgent' && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <Timer className="h-4 w-4 text-gray-500" />
            <span className={`text-sm font-medium ${
              priority === 'urgent' ? 'text-red-600' :
              priority === 'overdue' ? 'text-orange-600' :
              'text-gray-600'
            }`}>
              {elapsedTime}
            </span>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>{order.customer_name_display}</span>
          </div>
          {order.table_name && (
            <span className="bg-gray-100 px-2 py-1 rounded text-xs font-medium">
              {order.table_name}
            </span>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getOrderTypeColor()}`}>
            {order.order_type.replace('_', ' ').toUpperCase()}
          </span>
          
          <div className="text-xs text-gray-500">
            Est. {order.preparation_time_minutes} min
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="p-4 space-y-3">
        {order.items.map((item) => (
          <div key={item.id} className="border rounded-lg p-3 bg-gray-50">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">
                  <span className="bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full mr-2">
                    {item.quantity}
                  </span>
                  {item.menu_item_name}
                </h4>
                
                {item.modifiers.length > 0 && (
                  <div className="mt-1 text-sm text-gray-600">
                    <span className="font-medium">Modifiers: </span>
                    {item.modifiers.map((mod, idx) => (
                      <span key={mod.id}>
                        {mod.modifier_name}
                        {mod.quantity > 1 && ` (${mod.quantity})`}
                        {idx < item.modifiers.length - 1 && ', '}
                      </span>
                    ))}
                  </div>
                )}
                
                {item.special_instructions && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <div className="flex items-start space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                      <div>
                        <span className="text-xs font-medium text-yellow-800">Special Instructions:</span>
                        <p className="text-sm text-yellow-700 mt-1">{item.special_instructions}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="ml-3 flex flex-col space-y-2">
                <select
                  value={item.kitchen_status}
                  onChange={(e) => onUpdateItemStatus(item.id, e.target.value)}
                  className={`text-xs font-medium border rounded px-2 py-1 ${getStatusColor(item.kitchen_status)}`}
                >
                  <option value="pending">Pending</option>
                  <option value="preparing">Preparing</option>
                  <option value="ready">Ready</option>
                  <option value="served">Served</option>
                </select>
                
                {/* Quick action buttons */}
                <div className="flex flex-col space-y-1">
                  {item.kitchen_status === 'pending' && (
                    <button
                      onClick={() => onUpdateItemStatus(item.id, 'preparing')}
                      className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700 transition-colors"
                    >
                      Start
                    </button>
                  )}
                  {item.kitchen_status === 'preparing' && (
                    <button
                      onClick={() => onUpdateItemStatus(item.id, 'ready')}
                      className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700 transition-colors"
                    >
                      Ready
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Special Instructions for Order */}
      {order.special_instructions && (
        <div className="px-4 pb-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <h5 className="text-sm font-medium text-yellow-800 mb-1 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-1" />
              Order Instructions:
            </h5>
            <p className="text-sm text-yellow-700">{order.special_instructions}</p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="px-4 pb-4">
        <div className="flex space-x-2">
          {allItemsReady ? (
            <button
              onClick={() => onUpdateOrderStatus(order.id, 'served')}
              className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors flex items-center justify-center"
            >
              <Clock className="h-4 w-4 mr-2" />
              Mark as Served
            </button>
          ) : (
            <button
              onClick={() => {
                // Mark all items as ready
                order.items.forEach(item => {
                  if (item.kitchen_status !== 'ready') {
                    onUpdateItemStatus(item.id, 'ready');
                  }
                });
              }}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <Timer className="h-4 w-4 mr-2" />
              Mark All Ready
            </button>
          )}
          
          {/* Emergency button for urgent orders */}
          {priority === 'urgent' && (
            <button
              onClick={() => {
                // Priority handling - could trigger notifications
                alert(`Order ${order.order_number} is URGENT! Elapsed time: ${elapsedTime}`);
              }}
              className="px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
            >
              🚨
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderCard;

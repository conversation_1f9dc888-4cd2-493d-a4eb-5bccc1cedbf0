import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Clock, CheckCircle, AlertCircle } from 'lucide-react';
import api from '../../lib/api';
import OrderCard from './OrderCard';

interface OrderItem {
  id: string;
  menu_item_name: string;
  quantity: number;
  special_instructions: string;
  kitchen_status: 'pending' | 'preparing' | 'ready' | 'served';
  modifiers: Array<{
    id: string;
    modifier_name: string;
    quantity: number;
  }>;
}

interface KitchenOrder {
  id: string;
  order_number: string;
  order_type: string;
  table_name?: string;
  customer_name_display: string;
  status: string;
  confirmed_time: string;
  preparation_time_minutes: number;
  special_instructions: string;
  items: OrderItem[];
}

const KitchenDisplay: React.FC = () => {

  const [filter, setFilter] = useState<'all' | 'pending' | 'preparing' | 'ready'>('all');
  const queryClient = useQueryClient();

  // Fetch kitchen orders
  const { data: orders = [], isLoading, error } = useQuery({
    queryKey: ['kitchen-orders'],
    queryFn: () => api.get('/orders/orders/kitchen_display/').then(res => res.data),
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  // Update order item status
  const updateItemStatusMutation = useMutation({
    mutationFn: ({ itemId, status }: { itemId: string; status: string }) =>
      api.post(`/orders/order-items/${itemId}/update_kitchen_status/`, {
        kitchen_status: status,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
    },
  });

  // Update order status
  const updateOrderStatusMutation = useMutation({
    mutationFn: ({ orderId, status }: { orderId: string; status: string }) =>
      api.post(`/orders/orders/${orderId}/update_status/`, {
        status,
        notes: `Status updated from kitchen display`,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
    },
  });

  const filteredOrders = orders.filter((order: KitchenOrder) => {
    if (filter === 'all') return true;
    if (filter === 'pending') return order.items.some(item => item.kitchen_status === 'pending');
    if (filter === 'preparing') return order.items.some(item => item.kitchen_status === 'preparing');
    if (filter === 'ready') return order.items.every(item => item.kitchen_status === 'ready');
    return true;
  });



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Connection Error</h2>
          <p className="text-gray-600">Unable to load kitchen orders. Please check your connection.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">Kitchen Display</h1>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{new Date().toLocaleTimeString()}</span>
              </div>
            </div>
            
            {/* Filter Buttons */}
            <div className="flex space-x-2">
              {[
                { key: 'all', label: 'All Orders', count: orders.length },
                { key: 'pending', label: 'Pending', count: orders.filter((o: KitchenOrder) => o.items.some(i => i.kitchen_status === 'pending')).length },
                { key: 'preparing', label: 'Preparing', count: orders.filter((o: KitchenOrder) => o.items.some(i => i.kitchen_status === 'preparing')).length },
                { key: 'ready', label: 'Ready', count: orders.filter((o: KitchenOrder) => o.items.every(i => i.kitchen_status === 'ready')).length },
              ].map(({ key, label, count }) => (
                <button
                  key={key}
                  onClick={() => setFilter(key as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === key
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {label} ({count})
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Orders Grid */}
      <div className="p-6">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
            <p className="text-gray-600">No orders to display at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredOrders.map((order: KitchenOrder) => (
              <OrderCard
                key={order.id}
                order={order}
                onUpdateItemStatus={(itemId, status) =>
                  updateItemStatusMutation.mutate({ itemId, status })
                }
                onUpdateOrderStatus={(orderId, status) =>
                  updateOrderStatusMutation.mutate({ orderId, status })
                }
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default KitchenDisplay;

/**
 * Payment processing page for POS system
 */

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  CreditCardIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
  GiftIcon,
  PrinterIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout';
import { billingAPI, ordersAPI } from '../lib/api';

interface PaymentMethod {
  id: string;
  name: string;
  method_type: string;
  is_active: boolean;
  requires_authorization: boolean;
  processing_fee_percentage: number;
  processing_fee_fixed: number;
  icon?: string;
}

interface Order {
  id: string;
  order_number: string;
  total_amount: number;
  paid_amount: number;
  payment_status: string;
  items: any[];
  customer_name?: string;
  table_number?: string;
}

const PaymentPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [paymentAmount, setPaymentAmount] = useState<string>('');
  const [splitPayments, setSplitPayments] = useState<Array<{ method_id: string; amount: number }>>([]);
  const [notes, setNotes] = useState('');
  const [tipAmount, setTipAmount] = useState<string>('0');

  // Fetch order details
  const { data: order, isLoading: orderLoading } = useQuery({
    queryKey: ['order', orderId],
    queryFn: () => ordersAPI.getOrder(orderId!),
    enabled: !!orderId,
  });

  // Fetch payment methods
  const { data: paymentMethods = [] } = useQuery({
    queryKey: ['payment-methods'],
    queryFn: () => billingAPI.getPaymentMethods(),
  });

  // Process payment mutation
  const processPaymentMutation = useMutation({
    mutationFn: (paymentData: any) => billingAPI.processPayment(paymentData),
    onSuccess: (data) => {
      toast.success('Payment processed successfully!');
      queryClient.invalidateQueries({ queryKey: ['order', orderId] });
      
      // Print receipt if needed
      if (data.receipt_id) {
        printReceipt(data.receipt_id);
      }
      
      navigate('/orders');
    },
    onError: (error: any) => {
      toast.error('Payment failed. Please try again.');
      console.error('Payment error:', error);
    },
  });

  const printReceipt = async (receiptId: string) => {
    try {
      const receiptData = await billingAPI.getReceipt(receiptId);
      // Implement receipt printing logic
      window.print();
    } catch (error) {
      console.error('Receipt printing error:', error);
    }
  };

  const getPaymentMethodIcon = (methodType: string) => {
    switch (methodType) {
      case 'cash':
        return <BanknotesIcon className="h-6 w-6" />;
      case 'credit_card':
      case 'debit_card':
        return <CreditCardIcon className="h-6 w-6" />;
      case 'mobile_payment':
        return <DevicePhoneMobileIcon className="h-6 w-6" />;
      case 'gift_card':
        return <GiftIcon className="h-6 w-6" />;
      default:
        return <CreditCardIcon className="h-6 w-6" />;
    }
  };

  const handlePayment = () => {
    if (!selectedPaymentMethod) {
      toast.error('Please select a payment method');
      return;
    }

    const amount = parseFloat(paymentAmount) || order?.total_amount - order?.paid_amount;
    const tip = parseFloat(tipAmount) || 0;
    const totalAmount = amount + tip;

    if (totalAmount <= 0) {
      toast.error('Payment amount must be greater than 0');
      return;
    }

    const paymentData = {
      order_id: orderId,
      payment_method_id: selectedPaymentMethod,
      amount: totalAmount,
      tip_amount: tip,
      notes,
    };

    processPaymentMutation.mutate(paymentData);
  };

  const handleSplitPayment = () => {
    // Implementation for split payments
    toast.info('Split payment feature coming soon');
  };

  if (orderLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    );
  }

  if (!order) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-600">The order you're looking for doesn't exist.</p>
        </div>
      </Layout>
    );
  }

  const remainingAmount = order.total_amount - order.paid_amount;
  const isFullyPaid = order.payment_status === 'paid';

  return (
    <Layout>
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Process Payment</h1>
          <p className="text-gray-600 mt-1">Order #{order.order_number}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
            
            <div className="space-y-3 mb-4">
              {order.items.map((item: any, index: number) => (
                <div key={index} className="flex justify-between">
                  <span>{item.quantity}x {item.menu_item_name}</span>
                  <span>${parseFloat(item.total_price).toFixed(2)}</span>
                </div>
              ))}
            </div>

            <div className="border-t pt-4 space-y-2">
              <div className="flex justify-between text-lg font-semibold">
                <span>Total Amount:</span>
                <span>${order.total_amount.toFixed(2)}</span>
              </div>
              
              {order.paid_amount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Paid Amount:</span>
                  <span>-${order.paid_amount.toFixed(2)}</span>
                </div>
              )}
              
              <div className="flex justify-between text-xl font-bold text-red-600">
                <span>Remaining:</span>
                <span>${remainingAmount.toFixed(2)}</span>
              </div>
            </div>

            {isFullyPaid && (
              <div className="mt-4 p-3 bg-green-100 text-green-800 rounded-lg flex items-center">
                <CheckIcon className="h-5 w-5 mr-2" />
                Order is fully paid
              </div>
            )}
          </div>

          {/* Payment Form */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold mb-4">Payment Details</h2>

            {!isFullyPaid && (
              <>
                {/* Payment Methods */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Payment Method
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    {paymentMethods.filter((method: PaymentMethod) => method.is_active).map((method: PaymentMethod) => (
                      <button
                        key={method.id}
                        onClick={() => setSelectedPaymentMethod(method.id)}
                        className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                          selectedPaymentMethod === method.id
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {getPaymentMethodIcon(method.method_type)}
                        <span className="text-sm font-medium">{method.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Payment Amount */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Amount
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(e.target.value)}
                    placeholder={remainingAmount.toFixed(2)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Tip Amount */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tip Amount
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      step="0.01"
                      value={tipAmount}
                      onChange={(e) => setTipAmount(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <button
                      onClick={() => setTipAmount((remainingAmount * 0.15).toFixed(2))}
                      className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                    >
                      15%
                    </button>
                    <button
                      onClick={() => setTipAmount((remainingAmount * 0.20).toFixed(2))}
                      className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                    >
                      20%
                    </button>
                  </div>
                </div>

                {/* Notes */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Add any payment notes..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handlePayment}
                    disabled={processPaymentMutation.isPending}
                    className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {processPaymentMutation.isPending ? 'Processing...' : 'Process Payment'}
                  </button>

                  <button
                    onClick={handleSplitPayment}
                    className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200"
                  >
                    Split Payment
                  </button>
                </div>
              </>
            )}

            {/* Print Receipt */}
            <div className="mt-6 pt-6 border-t">
              <button
                onClick={() => printReceipt(order.id)}
                className="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 flex items-center justify-center"
              >
                <PrinterIcon className="h-5 w-5 mr-2" />
                Print Receipt
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default PaymentPage;

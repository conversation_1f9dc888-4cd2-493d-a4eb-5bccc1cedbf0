/**
 * POS (Point of Sale) page for Restaurant POS system
 */

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAppSelector, useAppDispatch } from '../store';
import { addItem } from '../store/slices/cartSlice';
import { menuAPI } from '../lib/api';
import MenuItemCard, { type MenuItem } from '../components/MenuItemCard';
import CartSidebar from '../components/CartSidebar';
import Layout from '../components/Layout';
import { toast } from 'react-hot-toast';

const POSPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Fetch menu categories
  const { data: categoriesData, isLoading: categoriesLoading } = useQuery({
    queryKey: ['menu-categories'],
    queryFn: () => menuAPI.getCategories(),
  });

  // Fetch menu items
  const { data: itemsData, isLoading: itemsLoading } = useQuery({
    queryKey: ['menu-items', selectedCategory, searchTerm],
    queryFn: () => menuAPI.getItems({
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      search: searchTerm || undefined
    }),
  });

  const categories = categoriesData?.data?.results || [];
  const menuItems: MenuItem[] = itemsData?.data?.results || [];

  const handleAddToCart = (menuItem: MenuItem) => {
    const cartItem = {
      id: `${menuItem.id}-${Date.now()}`, // Unique ID for cart item
      menu_item_id: menuItem.id,
      name: menuItem.name,
      price: parseFloat(menuItem.price),
      quantity: 1,
      modifiers: [],
    };

    dispatch(addItem(cartItem));
    toast.success(`${menuItem.name} added to cart`);
  };

  return (
    <Layout>
      <div className="min-h-screen flex flex-col lg:flex-row bg-gray-50">
        {/* Menu Section */}
        <div className="flex-1 p-2 sm:p-4">
          <div className="bg-white rounded-xl shadow-sm h-full flex flex-col">
            <div className="p-4 sm:p-6 border-b border-gray-100">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <div className="mb-4 sm:mb-0">
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Menu</h1>
                  <p className="text-gray-600 mt-1">Select items to add to order</p>
                </div>
                <div className="text-sm text-gray-500 hidden sm:block">
                  Welcome, {user?.first_name || 'User'}
                </div>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search menu items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setSelectedCategory('all')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium ${
                      selectedCategory === 'all'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    All
                  </button>
                  {categories.map((category: any) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium ${
                        selectedCategory === category.id
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex-1 p-4 sm:p-6 overflow-y-auto">
              {itemsLoading || categoriesLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
                  {menuItems.map((item) => (
                    <MenuItemCard
                      key={item.id}
                      item={item}
                      onAddToCart={handleAddToCart}
                    />
                  ))}
                  {menuItems.length === 0 && (
                    <div className="col-span-full text-center py-12">
                      <p className="text-gray-500">No menu items found</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Cart Section */}
        <div className="lg:w-96">
          <CartSidebar />
        </div>
      </div>
    </Layout>
  );
};

export default POSPage;

/**
 * Kitchen Display System page for restaurant staff
 */

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  FireIcon,
} from '@heroicons/react/24/outline';
import { ordersAPI } from '../lib/api';

interface KitchenOrder {
  id: string;
  order_number: string;
  order_type: string;
  table_name?: string;
  customer_name_display: string;
  status: string;
  confirmed_time: string;
  preparation_time_minutes: number;
  special_instructions: string;
  items: KitchenOrderItem[];
}

interface KitchenOrderItem {
  id: string;
  menu_item_name: string;
  quantity: number;
  special_instructions: string;
  kitchen_status: 'pending' | 'preparing' | 'ready' | 'served';
  modifiers: Array<{
    id: string;
    modifier_name: string;
    quantity: number;
  }>;
}

const KitchenDisplayPage: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'pending' | 'preparing' | 'ready'>('all');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const queryClient = useQueryClient();

  // Fetch kitchen orders with auto-refresh
  const { data: orders = [], isLoading, error } = useQuery({
    queryKey: ['kitchen-orders'],
    queryFn: () => ordersAPI.getKitchenOrders(),
    refetchInterval: autoRefresh ? 5000 : false, // Refresh every 5 seconds
  });

  // Update order item status
  const updateItemStatusMutation = useMutation({
    mutationFn: ({ itemId, status }: { itemId: string; status: string }) =>
      ordersAPI.updateOrderItemStatus(itemId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
      toast.success('Item status updated');
    },
    onError: () => {
      toast.error('Failed to update item status');
    },
  });

  // Update order status
  const updateOrderStatusMutation = useMutation({
    mutationFn: ({ orderId, status }: { orderId: string; status: string }) =>
      ordersAPI.updateOrderStatus(orderId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kitchen-orders'] });
      toast.success('Order status updated');
    },
    onError: () => {
      toast.error('Failed to update order status');
    },
  });

  const filteredOrders = orders.filter((order: KitchenOrder) => {
    if (filter === 'all') return true;

    // Filter based on overall order status or item statuses
    const hasItemsInStatus = order.items.some(item => item.kitchen_status === filter);
    return hasItemsInStatus || order.status === filter;
  });

  const getOrderPriority = (order: KitchenOrder) => {
    const confirmedTime = new Date(order.confirmed_time);
    const now = new Date();
    const elapsedMinutes = (now.getTime() - confirmedTime.getTime()) / (1000 * 60);

    if (elapsedMinutes > order.preparation_time_minutes + 10) return 'urgent';
    if (elapsedMinutes > order.preparation_time_minutes) return 'overdue';
    return 'normal';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'served':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-red-500 bg-red-50';
      case 'overdue':
        return 'border-orange-500 bg-orange-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const formatElapsedTime = (confirmedTime: string) => {
    const confirmed = new Date(confirmedTime);
    const now = new Date();
    const elapsedMinutes = Math.floor((now.getTime() - confirmed.getTime()) / (1000 * 60));

    if (elapsedMinutes < 60) {
      return `${elapsedMinutes}m`;
    }

    const hours = Math.floor(elapsedMinutes / 60);
    const minutes = elapsedMinutes % 60;
    return `${hours}h ${minutes}m`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connection Error</h2>
          <p className="text-gray-600">Unable to load kitchen orders. Please check your connection.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Kitchen Display</h1>
              <div className="ml-4 flex items-center space-x-2">
                <ClockIcon className="h-5 w-5 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Auto-refresh toggle */}
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Auto-refresh</span>
              </label>

              {/* Filter buttons */}
              <div className="flex space-x-2">
                {[
                  { key: 'all', label: 'All', count: orders.length },
                  { key: 'pending', label: 'Pending', count: orders.filter((o: KitchenOrder) => o.items.some(i => i.kitchen_status === 'pending')).length },
                  { key: 'preparing', label: 'Preparing', count: orders.filter((o: KitchenOrder) => o.items.some(i => i.kitchen_status === 'preparing')).length },
                  { key: 'ready', label: 'Ready', count: orders.filter((o: KitchenOrder) => o.items.some(i => i.kitchen_status === 'ready')).length },
                ].map((filterOption) => (
                  <button
                    key={filterOption.key}
                    onClick={() => setFilter(filterOption.key as any)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      filter === filterOption.key
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {filterOption.label} ({filterOption.count})
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Grid */}
      <div className="p-6">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
            <p className="text-gray-600">No orders to display at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredOrders.map((order: KitchenOrder) => {
              const priority = getOrderPriority(order);

              return (
                <div
                  key={order.id}
                  className={`rounded-lg border-2 p-4 ${getPriorityColor(priority)} shadow-sm`}
                >
                  {/* Order Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-bold text-lg">{order.order_number}</h3>
                      <p className="text-sm text-gray-600">
                        {order.table_name || order.customer_name_display}
                      </p>
                    </div>

                    <div className="text-right">
                      <div className="flex items-center text-sm text-gray-600">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        {formatElapsedTime(order.confirmed_time)}
                      </div>

                      {priority === 'urgent' && (
                        <div className="flex items-center text-red-600 text-sm mt-1">
                          <FireIcon className="h-4 w-4 mr-1" />
                          URGENT
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Order Items */}
                  <div className="space-y-2 mb-4">
                    {order.items.map((item) => (
                      <div
                        key={item.id}
                        className={`p-2 rounded border ${getStatusColor(item.kitchen_status)}`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">
                            {item.quantity}x {item.menu_item_name}
                          </span>

                          <select
                            value={item.kitchen_status}
                            onChange={(e) => updateItemStatusMutation.mutate({
                              itemId: item.id,
                              status: e.target.value
                            })}
                            className="text-xs border-0 bg-transparent font-medium focus:ring-0"
                          >
                            <option value="pending">Pending</option>
                            <option value="preparing">Preparing</option>
                            <option value="ready">Ready</option>
                            <option value="served">Served</option>
                          </select>
                        </div>

                        {/* Modifiers */}
                        {item.modifiers.length > 0 && (
                          <div className="text-xs text-gray-600 mt-1">
                            {item.modifiers.map((modifier, idx) => (
                              <span key={modifier.id}>
                                {modifier.quantity > 1 && `${modifier.quantity}x `}
                                {modifier.modifier_name}
                                {idx < item.modifiers.length - 1 && ', '}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Special Instructions */}
                        {item.special_instructions && (
                          <div className="text-xs text-orange-700 mt-1 font-medium">
                            Note: {item.special_instructions}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Order Special Instructions */}
                  {order.special_instructions && (
                    <div className="mb-4 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                      <strong>Order Note:</strong> {order.special_instructions}
                    </div>
                  )}

                  {/* Order Actions */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => updateOrderStatusMutation.mutate({
                        orderId: order.id,
                        status: 'ready'
                      })}
                      className="flex-1 bg-green-600 text-white py-2 px-3 rounded text-sm font-medium hover:bg-green-700"
                    >
                      Mark Ready
                    </button>

                    <button
                      onClick={() => updateOrderStatusMutation.mutate({
                        orderId: order.id,
                        status: 'served'
                      })}
                      className="flex-1 bg-blue-600 text-white py-2 px-3 rounded text-sm font-medium hover:bg-blue-700"
                    >
                      Served
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default KitchenDisplayPage;

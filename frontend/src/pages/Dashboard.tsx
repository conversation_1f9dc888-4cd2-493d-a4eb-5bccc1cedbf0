import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAppSelector } from '../store/hooks';
import Navigation from '../components/Navigation';
import { analyticsAPI, ordersAPI, inventoryAPI } from '../lib/api';
import {
  CurrencyDollarIcon,
  ShoppingCartIcon,
  UsersIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface DashboardMetrics {
  revenue: {
    today: number;
    yesterday: number;
    change: number;
  };
  orders: {
    today: number;
    yesterday: number;
    change: number;
    pending: number;
    completed: number;
  };
  averageOrderValue: {
    current: number;
    previous: number;
    change: number;
  };
  staff: {
    onDuty: number;
    total: number;
  };
  lowStockItems: Array<{
    id: string;
    name: string;
    currentStock: number;
    minStock: number;
    category: string;
  }>;
  topSellingItems: Array<{
    id: string;
    name: string;
    quantity: number;
    revenue: number;
  }>;
  recentOrders: Array<{
    id: string;
    orderNumber: string;
    customerName: string;
    total: number;
    status: string;
    createdAt: string;
  }>;
}

const MetricCard: React.FC<{
  title: string;
  value: string | number;
  previousValue?: number;
  change?: number;
  icon: React.ComponentType<any>;
  color: string;
  isLoading?: boolean;
}> = ({ title, value, previousValue, change, icon: Icon, color, isLoading }) => {
  const isPositive = change && change > 0;
  const isNegative = change && change < 0;

  return (
    <div className="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
              <Icon className={`h-6 w-6 ${color}`} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <div className="flex items-center mt-1">
                {isLoading ? (
                  <div className="h-8 w-20 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>
                    <p className="text-2xl font-semibold text-gray-900">{value}</p>
                    {change !== undefined && (
                      <div className={`ml-2 flex items-center text-sm ${
                        isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-gray-500'
                      }`}>
                        {isPositive && <ArrowUpIcon className="h-4 w-4 mr-1" />}
                        {isNegative && <ArrowDownIcon className="h-4 w-4 mr-1" />}
                        <span>{Math.abs(change)}%</span>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AlertCard: React.FC<{
  title: string;
  items: any[];
  icon: React.ComponentType<any>;
  iconColor: string;
  emptyMessage: string;
  renderItem: (item: any, index: number) => React.ReactNode;
}> = ({ title, items, icon: Icon, iconColor, emptyMessage, renderItem }) => (
  <div className="bg-white shadow-sm rounded-lg border border-gray-200">
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Icon className={`h-5 w-5 ${iconColor} mr-2`} />
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
        <span className="text-sm text-gray-500">{items.length} items</span>
      </div>
      <div className="max-h-64 overflow-y-auto">
        {items.length > 0 ? (
          <div className="space-y-3">
            {items.map(renderItem)}
          </div>
        ) : (
          <p className="text-sm text-gray-500 text-center py-4">{emptyMessage}</p>
        )}
      </div>
    </div>
  </div>
);

export const Dashboard: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const queryClient = useQueryClient();
  const [lastRefresh, setLastRefresh] = useState(new Date());

  const { data: metrics, isLoading, error, refetch } = useQuery<DashboardMetrics>({
    queryKey: ['dashboard-metrics'],
    queryFn: async () => {
      const [dashboardData, pendingOrders, lowStock] = await Promise.all([
        analyticsAPI.getDashboardData(),
        ordersAPI.getPendingOrders(),
        inventoryAPI.getLowStockItems(),
      ]);
      
      return {
        revenue: dashboardData.data.revenue || { today: 0, yesterday: 0, change: 0 },
        orders: dashboardData.data.orders || { today: 0, yesterday: 0, change: 0, pending: 0, completed: 0 },
        averageOrderValue: dashboardData.data.averageOrderValue || { current: 0, previous: 0, change: 0 },
        staff: dashboardData.data.staff || { onDuty: 0, total: 0 },
        lowStockItems: lowStock.data || [],
        topSellingItems: dashboardData.data.topSellingItems || [],
        recentOrders: dashboardData.data.recentOrders || [],
      };
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 3,
    onError: (error) => {
      toast.error('Failed to load dashboard data');
    },
  });

  const handleRefresh = async () => {
    try {
      await refetch();
      setLastRefresh(new Date());
      toast.success('Dashboard refreshed');
    } catch (error) {
      toast.error('Failed to refresh dashboard');
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['dashboard-metrics'] });
    }, 60000); // Auto-refresh every minute

    return () => clearInterval(interval);
  }, [queryClient]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-500" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading dashboard</h3>
            <p className="mt-1 text-sm text-gray-500">Please try refreshing the page</p>
            <button
              onClick={handleRefresh}
              className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, {user?.first_name}!
              </h1>
              <p className="text-gray-600 mt-1">
                Here's your restaurant performance overview
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </span>
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <MetricCard
              title="Today's Revenue"
              value={`$${metrics?.revenue.today.toLocaleString() || '0'}`}
              change={metrics?.revenue.change}
              icon={CurrencyDollarIcon}
              color="text-green-600"
              isLoading={isLoading}
            />
            <MetricCard
              title="Orders Today"
              value={metrics?.orders.today || 0}
              change={metrics?.orders.change}
              icon={ShoppingCartIcon}
              color="text-blue-600"
              isLoading={isLoading}
            />
            <MetricCard
              title="Average Order Value"
              value={`$${metrics?.averageOrderValue.current.toFixed(2) || '0.00'}`}
              change={metrics?.averageOrderValue.change}
              icon={ChartBarIcon}
              color="text-purple-600"
              isLoading={isLoading}
            />
            <MetricCard
              title="Staff on Duty"
              value={`${metrics?.staff.onDuty || 0}/${metrics?.staff.total || 0}`}
              icon={UsersIcon}
              color="text-indigo-600"
              isLoading={isLoading}
            />
          </div>

          {/* Alerts and Quick Info */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <AlertCard
              title="Low Stock Alerts"
              items={metrics?.lowStockItems || []}
              icon={ExclamationTriangleIcon}
              iconColor="text-yellow-500"
              emptyMessage="All items are well stocked"
              renderItem={(item, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{item.name}</p>
                    <p className="text-xs text-gray-500">{item.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-yellow-800">{item.currentStock} left</p>
                    <p className="text-xs text-gray-500">Min: {item.minStock}</p>
                  </div>
                </div>
              )}
            />

            <AlertCard
              title="Recent Orders"
              items={metrics?.recentOrders || []}
              icon={ClockIcon}
              iconColor="text-orange-500"
              emptyMessage="No recent orders"
              renderItem={(order, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">#{order.orderNumber}</p>
                    <p className="text-xs text-gray-500">{order.customerName}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">${order.total}</p>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {order.status}
                    </span>
                  </div>
                </div>
              )}
            />
          </div>

          {/* Top Selling Items */}
          {metrics?.topSellingItems && metrics.topSellingItems.length > 0 && (
            <div className="bg-white shadow-sm rounded-lg border border-gray-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Selling Items Today</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity Sold</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {metrics.topSellingItems.slice(0, 5).map((item, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.quantity}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.revenue.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

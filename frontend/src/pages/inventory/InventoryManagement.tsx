import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { inventoryAPI } from '../../lib/api';
import { InventoryItemList } from '../../components/inventory/InventoryItemList';
import { SupplierList } from '../../components/inventory/SupplierList';
import { PurchaseOrderList } from '../../components/inventory/PurchaseOrderList';
import { InventoryItemForm } from '../../components/inventory/InventoryItemForm';
import { SupplierForm } from '../../components/inventory/SupplierForm';
import { Button } from '../../components/ui/Button';
import { Modal } from '../../components/ui/Modal';
import { Tabs } from '../../components/ui/Tabs';
import { toast } from 'react-hot-toast';

export const InventoryManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('items');
  const [isItemModalOpen, setIsItemModalOpen] = useState(false);
  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [editingSupplier, setEditingSupplier] = useState<any>(null);
  const queryClient = useQueryClient();

  const { data: items } = useQuery({
    queryKey: ['inventory-items'],
    queryFn: () => inventoryAPI.getItems().then(res => res.data),
  });

  const { data: suppliers } = useQuery({
    queryKey: ['suppliers'],
    queryFn: () => inventoryAPI.getSuppliers().then(res => res.data),
  });

  const { data: purchaseOrders } = useQuery({
    queryKey: ['purchase-orders'],
    queryFn: () => inventoryAPI.getPurchaseOrders().then(res => res.data),
  });

  const createItemMutation = useMutation({
    mutationFn: inventoryAPI.createItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-items'] });
      setIsItemModalOpen(false);
      toast.success('Inventory item created successfully');
    },
  });

  const createSupplierMutation = useMutation({
    mutationFn: inventoryAPI.createSupplier,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
      setIsSupplierModalOpen(false);
      toast.success('Supplier created successfully');
    },
  });

  const tabs = [
    { id: 'items', label: 'Inventory Items' },
    { id: 'suppliers', label: 'Suppliers' },
    { id: 'purchase-orders', label: 'Purchase Orders' },
    { id: 'adjustments', label: 'Stock Adjustments' },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
        <div className="space-x-2">
          {activeTab === 'items' && (
            <Button onClick={() => setIsItemModalOpen(true)}>
              Add Item
            </Button>
          )}
          {activeTab === 'suppliers' && (
            <Button onClick={() => setIsSupplierModalOpen(true)}>
              Add Supplier
            </Button>
          )}
        </div>
      </div>

      <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />

      <div className="mt-6">
        {activeTab === 'items' && (
          <InventoryItemList
            items={items || []}
            onEdit={setEditingItem}
            onDelete={(id) => console.log('Delete item', id)}
          />
        )}
        {activeTab === 'suppliers' && (
          <SupplierList
            suppliers={suppliers || []}
            onEdit={setEditingSupplier}
            onDelete={(id) => console.log('Delete supplier', id)}
          />
        )}
        {activeTab === 'purchase-orders' && (
          <PurchaseOrderList orders={purchaseOrders || []} />
        )}
      </div>

      <Modal
        isOpen={isItemModalOpen}
        onClose={() => setIsItemModalOpen(false)}
        title="Create Inventory Item"
      >
        <InventoryItemForm
          suppliers={suppliers || []}
          onSubmit={(data) => createItemMutation.mutate(data)}
          onCancel={() => setIsItemModalOpen(false)}
          isLoading={createItemMutation.isPending}
        />
      </Modal>

      <Modal
        isOpen={isSupplierModalOpen}
        onClose={() => setIsSupplierModalOpen(false)}
        title="Create Supplier"
      >
        <SupplierForm
          onSubmit={(data) => createSupplierMutation.mutate(data)}
          onCancel={() => setIsSupplierModalOpen(false)}
          isLoading={createSupplierMutation.isPending}
        />
      </Modal>
    </div>
  );
};
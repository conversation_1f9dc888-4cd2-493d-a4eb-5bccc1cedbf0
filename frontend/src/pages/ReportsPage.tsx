/**
 * Reports and Analytics page for Restaurant POS system
 */

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { analyticsAPI } from '../lib/api';
import Layout from '../components/Layout';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  UsersIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  PrinterIcon,
} from '@heroicons/react/24/outline';

interface SalesData {
  total_sales: string;
  total_orders: number;
  average_order_value: string;
  top_selling_items: Array<{
    name: string;
    quantity_sold: number;
    revenue: string;
  }>;
  sales_by_hour: Array<{
    hour: number;
    sales: string;
    orders: number;
  }>;
  sales_by_category: Array<{
    category: string;
    sales: string;
    percentage: number;
  }>;
}

const ReportsPage: React.FC = () => {
  const [dateRange, setDateRange] = useState<string>('today');
  const [reportType, setReportType] = useState<string>('sales');

  const { data: salesData, isLoading } = useQuery({
    queryKey: ['analytics', dateRange],
    queryFn: () => analyticsAPI.getSalesReport({ period: dateRange }),
  });

  const sales: SalesData = salesData?.data || {
    total_sales: '0.00',
    total_orders: 0,
    average_order_value: '0.00',
    top_selling_items: [],
    sales_by_hour: [],
    sales_by_category: [],
  };

  const dateRangeOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'this_week', label: 'This Week' },
    { value: 'last_week', label: 'Last Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
  ];

  const reportTypeOptions = [
    { value: 'sales', label: 'Sales Report' },
    { value: 'inventory', label: 'Inventory Report' },
    { value: 'staff', label: 'Staff Performance' },
    { value: 'customer', label: 'Customer Analytics' },
  ];

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ComponentType<any>;
    trend?: { value: number; isPositive: boolean };
    color: string;
  }> = ({ title, value, icon: Icon, trend, color }) => (
    <div className="pos-card p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {trend && (
            <div className={`flex items-center mt-2 text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {trend.isPositive ? (
                <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
              ) : (
                <ArrowTrendingDownIcon className="w-4 h-4 mr-1" />
              )}
              {Math.abs(trend.value)}% vs last period
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <Layout>
      <div className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div className="mb-4 sm:mb-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600 mt-1">Track performance and analyze business metrics</p>
          </div>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <button className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <PrinterIcon className="w-4 h-4 mr-2" />
              Export PDF
            </button>
            <button className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <CalendarIcon className="w-4 h-4 mr-2" />
              Custom Range
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 flex space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="pos-input"
            >
              {dateRangeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="pos-input"
            >
              {reportTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard
                title="Total Sales"
                value={`$${sales.total_sales}`}
                icon={CurrencyDollarIcon}
                trend={{ value: 12.5, isPositive: true }}
                color="bg-green-500"
              />
              <StatCard
                title="Total Orders"
                value={sales.total_orders}
                icon={ShoppingCartIcon}
                trend={{ value: 8.2, isPositive: true }}
                color="bg-blue-500"
              />
              <StatCard
                title="Average Order Value"
                value={`$${sales.average_order_value}`}
                icon={ChartBarIcon}
                trend={{ value: 3.1, isPositive: false }}
                color="bg-purple-500"
              />
              <StatCard
                title="Customers Served"
                value={sales.total_orders}
                icon={UsersIcon}
                trend={{ value: 15.7, isPositive: true }}
                color="bg-orange-500"
              />
            </div>

            {/* Charts and Tables */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Selling Items */}
              <div className="pos-card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Selling Items</h3>
                <div className="space-y-3">
                  {sales.top_selling_items.slice(0, 5).map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{item.name}</p>
                          <p className="text-sm text-gray-600">{item.quantity_sold} sold</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">${item.revenue}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Sales by Category */}
              <div className="pos-card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales by Category</h3>
                <div className="space-y-3">
                  {sales.sales_by_category.map((category, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-900">{category.category}</span>
                        <span className="text-sm text-gray-600">${category.sales}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Hourly Sales Chart */}
            <div className="pos-card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales by Hour</h3>
              <div className="grid grid-cols-12 gap-2 h-32">
                {Array.from({ length: 24 }, (_, hour) => {
                  const hourData = sales.sales_by_hour.find(h => h.hour === hour);
                  const hourSales = parseFloat(hourData?.sales || '0');
                  const maxSales = Math.max(...sales.sales_by_hour.map(h => parseFloat(h.sales)));
                  const height = maxSales > 0 ? (hourSales / maxSales) * 100 : 0;
                  
                  return (
                    <div key={hour} className="flex flex-col items-center">
                      <div className="flex-1 flex items-end">
                        <div
                          className="w-full bg-blue-500 rounded-t"
                          style={{ height: `${height}%`, minHeight: hourSales > 0 ? '4px' : '0' }}
                          title={`${hour}:00 - $${hourSales}`}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-600 mt-1">{hour}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ReportsPage;

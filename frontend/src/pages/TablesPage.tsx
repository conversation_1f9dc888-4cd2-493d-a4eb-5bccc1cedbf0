/**
 * Tables page for Restaurant POS system
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { tablesAPI } from '../lib/api';
import Layout from '../components/Layout';

const TablesPage: React.FC = () => {
  const { data: tablesData, isLoading } = useQuery({
    queryKey: ['tables'],
    queryFn: () => tablesAPI.getTables(),
  });

  const tables = tablesData?.data?.results || [];

  return (
    <Layout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Tables</h1>
          <p className="text-gray-600 mt-1">Manage restaurant tables and seating</p>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {tables.map((table: any) => (
              <div key={table.id} className="pos-card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {table.display_name}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    table.status === 'available' 
                      ? 'bg-green-100 text-green-800'
                      : table.status === 'occupied'
                      ? 'bg-red-100 text-red-800'
                      : table.status === 'reserved'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {table.status}
                  </span>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>Capacity: {table.capacity} people</p>
                  {table.section && <p>Section: {table.section}</p>}
                </div>
                <div className="mt-4 flex space-x-2">
                  <button className="pos-button-primary text-xs px-3 py-1">
                    Assign Order
                  </button>
                  <button className="pos-button-secondary text-xs px-3 py-1">
                    Edit
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default TablesPage;

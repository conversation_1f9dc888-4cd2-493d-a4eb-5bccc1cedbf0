import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { menuAPI } from '../../lib/api';
import { CategoryList } from '../../components/menu/CategoryList';
import { ItemList } from '../../components/menu/ItemList';
import { CategoryForm } from '../../components/menu/CategoryForm';
import { ItemForm } from '../../components/menu/ItemForm';
import { Button } from '../../components/ui/Button';
import { Modal } from '../../components/ui/Modal';
import { Tabs } from '../../components/ui/Tabs';
import { toast } from 'react-hot-toast';

export const MenuManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('categories');
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [isItemModalOpen, setIsItemModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [editingItem, setEditingItem] = useState<any>(null);
  const queryClient = useQueryClient();

  const { data: categories } = useQuery({
    queryKey: ['menu-categories'],
    queryFn: () => menuAPI.getCategories().then(res => res.data),
  });

  const { data: items } = useQuery({
    queryKey: ['menu-items'],
    queryFn: () => menuAPI.getItems().then(res => res.data),
  });

  const createCategoryMutation = useMutation({
    mutationFn: menuAPI.createCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-categories'] });
      setIsCategoryModalOpen(false);
      toast.success('Category created successfully');
    },
  });

  const createItemMutation = useMutation({
    mutationFn: menuAPI.createItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      setIsItemModalOpen(false);
      toast.success('Item created successfully');
    },
  });

  const tabs = [
    { id: 'categories', label: 'Categories' },
    { id: 'items', label: 'Menu Items' },
    { id: 'modifiers', label: 'Modifiers' },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Menu Management</h1>
        <div className="space-x-2">
          {activeTab === 'categories' && (
            <Button onClick={() => setIsCategoryModalOpen(true)}>
              Add Category
            </Button>
          )}
          {activeTab === 'items' && (
            <Button onClick={() => setIsItemModalOpen(true)}>
              Add Item
            </Button>
          )}
        </div>
      </div>

      <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />

      <div className="mt-6">
        {activeTab === 'categories' && (
          <CategoryList
            categories={categories || []}
            onEdit={setEditingCategory}
            onDelete={(id) => console.log('Delete category', id)}
          />
        )}
        {activeTab === 'items' && (
          <ItemList
            items={items || []}
            categories={categories || []}
            onEdit={setEditingItem}
            onDelete={(id) => console.log('Delete item', id)}
          />
        )}
      </div>

      <Modal
        isOpen={isCategoryModalOpen}
        onClose={() => setIsCategoryModalOpen(false)}
        title="Create Category"
      >
        <CategoryForm
          onSubmit={(data) => createCategoryMutation.mutate(data)}
          onCancel={() => setIsCategoryModalOpen(false)}
          isLoading={createCategoryMutation.isPending}
        />
      </Modal>

      <Modal
        isOpen={isItemModalOpen}
        onClose={() => setIsItemModalOpen(false)}
        title="Create Menu Item"
      >
        <ItemForm
          categories={categories || []}
          onSubmit={(data) => createItemMutation.mutate(data)}
          onCancel={() => setIsItemModalOpen(false)}
          isLoading={createItemMutation.isPending}
        />
      </Modal>
    </div>
  );
};
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { RestaurantManagement } from './RestaurantManagement';
import { UserManagement } from './UserManagement';
import { SystemSettings } from './SystemSettings';
import { AdminSidebar } from '../../components/admin/AdminSidebar';

export const AdminDashboard: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);

  if (user?.user_type !== 'SUPER_ADMIN') {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <AdminSidebar />
      <div className="flex-1 overflow-auto">
        <Routes>
          <Route path="/" element={<Navigate to="/admin/restaurants" replace />} />
          <Route path="/restaurants" element={<RestaurantManagement />} />
          <Route path="/users" element={<UserManagement />} />
          <Route path="/settings" element={<SystemSettings />} />
        </Routes>
      </div>
    </div>
  );
};

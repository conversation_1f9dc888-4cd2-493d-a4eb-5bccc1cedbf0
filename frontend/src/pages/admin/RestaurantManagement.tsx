import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { restaurantAPI } from '../../lib/api';
import { RestaurantForm } from '../../components/admin/RestaurantForm';
import { RestaurantList } from '../../components/admin/RestaurantList';
import { Button } from '../../components/ui/Button';
import { Modal } from '../../components/ui/Modal';
import { toast } from 'react-hot-toast';

export const RestaurantManagement: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingRestaurant, setEditingRestaurant] = useState<any>(null);
  const queryClient = useQueryClient();

  const { data: restaurants, isLoading } = useQuery({
    queryKey: ['restaurants'],
    queryFn: () => restaurantAPI.getAll().then(res => res.data),
  });

  const createMutation = useMutation({
    mutationFn: restaurantAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
      setIsCreateModalOpen(false);
      toast.success('Restaurant created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create restaurant');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => restaurantAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
      setEditingRestaurant(null);
      toast.success('Restaurant updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update restaurant');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: restaurantAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurants'] });
      toast.success('Restaurant deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete restaurant');
    },
  });

  const handleCreate = (data: any) => {
    createMutation.mutate(data);
  };

  const handleUpdate = (data: any) => {
    if (editingRestaurant) {
      updateMutation.mutate({ id: editingRestaurant.id, data });
    }
  };

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this restaurant?')) {
      deleteMutation.mutate(id);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Restaurant Management</h1>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          Add Restaurant
        </Button>
      </div>

      <RestaurantList
        restaurants={restaurants || []}
        onEdit={setEditingRestaurant}
        onDelete={handleDelete}
      />

      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Create Restaurant"
      >
        <RestaurantForm
          onSubmit={handleCreate}
          onCancel={() => setIsCreateModalOpen(false)}
          isLoading={createMutation.isPending}
        />
      </Modal>

      <Modal
        isOpen={!!editingRestaurant}
        onClose={() => setEditingRestaurant(null)}
        title="Edit Restaurant"
      >
        <RestaurantForm
          initialData={editingRestaurant}
          onSubmit={handleUpdate}
          onCancel={() => setEditingRestaurant(null)}
          isLoading={updateMutation.isPending}
        />
      </Modal>
    </div>
  );
};
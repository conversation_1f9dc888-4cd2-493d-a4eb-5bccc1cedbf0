/**
 * Inventory page for Restaurant POS system
 */

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { inventoryAPI } from '../lib/api';
import Layout from '../components/Layout';
import { toast } from 'react-hot-toast';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  ArchiveBoxIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

interface InventoryItem {
  id: string;
  name: string;
  category_name: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock: number;
  unit_of_measure: string;
  cost_per_unit: string;
  supplier: string;
  last_restocked: string;
  is_active: boolean;
}

const InventoryPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showLowStock, setShowLowStock] = useState<boolean>(false);
  const queryClient = useQueryClient();

  const { data: categoriesData } = useQuery({
    queryKey: ['inventory-categories'],
    queryFn: () => inventoryAPI.getCategories(),
  });

  const { data: itemsData, isLoading } = useQuery({
    queryKey: ['inventory-items', selectedCategory, showLowStock],
    queryFn: () => inventoryAPI.getItems({
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      low_stock: showLowStock || undefined,
    }),
  });

  const updateStockMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => inventoryAPI.updateItem(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory-items'] });
      toast.success('Stock updated successfully');
    },
    onError: () => {
      toast.error('Failed to update stock');
    },
  });

  const categories = categoriesData?.data?.results || [];
  const inventoryItems: InventoryItem[] = itemsData?.data?.results || [];

  const getStockStatus = (item: InventoryItem) => {
    if (item.current_stock <= item.minimum_stock) {
      return { status: 'low', color: 'bg-red-100 text-red-800', icon: ExclamationTriangleIcon };
    } else if (item.current_stock >= item.maximum_stock) {
      return { status: 'high', color: 'bg-yellow-100 text-yellow-800', icon: ExclamationTriangleIcon };
    } else {
      return { status: 'normal', color: 'bg-green-100 text-green-800', icon: ArchiveBoxIcon };
    }
  };

  const handleStockUpdate = (itemId: string, newStock: number) => {
    updateStockMutation.mutate({
      id: itemId,
      data: { current_stock: newStock }
    });
  };

  const lowStockItems = inventoryItems.filter(item => item.current_stock <= item.minimum_stock);

  return (
    <Layout>
      <div className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div className="mb-4 sm:mb-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Inventory</h1>
            <p className="text-gray-600 mt-1">Manage stock levels and inventory items</p>
          </div>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <button className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <ChartBarIcon className="w-4 h-4 mr-2" />
              Stock Report
            </button>
            <button className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Item
            </button>
          </div>
        </div>

        {/* Stock Alerts */}
        {lowStockItems.length > 0 && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-400 mr-2" />
              <h3 className="text-sm font-medium text-red-800">
                Low Stock Alert: {lowStockItems.length} items need restocking
              </h3>
            </div>
            <div className="mt-2 text-sm text-red-700">
              {lowStockItems.slice(0, 3).map(item => item.name).join(', ')}
              {lowStockItems.length > 3 && ` and ${lowStockItems.length - 3} more`}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6 flex space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="pos-input"
            >
              <option value="all">All Categories</option>
              {categories.map((category: any) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showLowStock}
                onChange={(e) => setShowLowStock(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm font-medium text-gray-700">Show only low stock</span>
            </label>
          </div>
        </div>

        {/* Inventory Items */}
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {inventoryItems.map((item) => {
              const stockStatus = getStockStatus(item);
              const StatusIcon = stockStatus.icon;
              
              return (
                <div key={item.id} className="pos-card p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {item.name}
                      </h3>
                      <p className="text-sm text-gray-600">{item.category_name}</p>
                    </div>
                    <div className="flex space-x-1">
                      <button className="p-2 text-gray-400 hover:text-blue-600">
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600">
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Current Stock:</span>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          value={item.current_stock}
                          onChange={(e) => handleStockUpdate(item.id, parseInt(e.target.value) || 0)}
                          className="w-20 text-sm px-2 py-1 border border-gray-300 rounded"
                        />
                        <span className="text-sm text-gray-500">{item.unit_of_measure}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Min Stock:</span>
                      <span className="font-medium">{item.minimum_stock} {item.unit_of_measure}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Max Stock:</span>
                      <span className="font-medium">{item.maximum_stock} {item.unit_of_measure}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Cost per Unit:</span>
                      <span className="font-medium">${item.cost_per_unit}</span>
                    </div>
                    
                    {item.supplier && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Supplier:</span>
                        <span className="font-medium">{item.supplier}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
                      <StatusIcon className="w-3 h-3 mr-1" />
                      {stockStatus.status === 'low' ? 'Low Stock' : 
                       stockStatus.status === 'high' ? 'Overstocked' : 'Normal'}
                    </span>
                    
                    <div className="text-xs text-gray-500">
                      Last restocked: {new Date(item.last_restocked).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {inventoryItems.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <ArchiveBoxIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No inventory items found</h3>
            <p className="text-gray-600">Add inventory items to track stock levels.</p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default InventoryPage;

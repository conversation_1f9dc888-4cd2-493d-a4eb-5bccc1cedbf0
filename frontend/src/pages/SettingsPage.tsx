/**
 * Settings page for Restaurant POS system
 */

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { restaurantsAPI, authAPI } from '../lib/api';
import Layout from '../components/Layout';
import { toast } from 'react-hot-toast';
import {

  BuildingStorefrontIcon,
  UserIcon,
  BellIcon,
  CreditCardIcon,
  PrinterIcon,
  ShieldCheckIcon,

} from '@heroicons/react/24/outline';

interface RestaurantSettings {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  tax_rate: string;
  service_charge: string;
  currency: string;
  timezone: string;
  receipt_footer: string;
  auto_print_receipts: boolean;
  enable_notifications: boolean;
  table_management: boolean;
  inventory_tracking: boolean;
}

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('restaurant');
  const queryClient = useQueryClient();

  const { data: restaurantData } = useQuery({
    queryKey: ['restaurant-settings'],
    queryFn: () => restaurantsAPI.getSettings(),
  });

  const { data: profileData } = useQuery({
    queryKey: ['user-profile'],
    queryFn: () => authAPI.getProfile(),
  });

  const updateRestaurantMutation = useMutation({
    mutationFn: (data: any) => restaurantsAPI.updateSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurant-settings'] });
      toast.success('Restaurant settings updated successfully');
    },
    onError: () => {
      toast.error('Failed to update restaurant settings');
    },
  });

  const updateProfileMutation = useMutation({
    mutationFn: (data: any) => authAPI.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });
      toast.success('Profile updated successfully');
    },
    onError: () => {
      toast.error('Failed to update profile');
    },
  });

  const restaurant: RestaurantSettings = restaurantData?.data || {};
  const profile = profileData?.data || {};

  const [restaurantForm, setRestaurantForm] = useState({
    name: restaurant.name || '',
    address: restaurant.address || '',
    phone: restaurant.phone || '',
    email: restaurant.email || '',
    website: restaurant.website || '',
    tax_rate: restaurant.tax_rate || '0.00',
    service_charge: restaurant.service_charge || '0.00',
    currency: restaurant.currency || 'USD',
    timezone: restaurant.timezone || 'UTC',
    receipt_footer: restaurant.receipt_footer || '',
    auto_print_receipts: restaurant.auto_print_receipts || false,
    enable_notifications: restaurant.enable_notifications ?? true,
    table_management: restaurant.table_management ?? true,
    inventory_tracking: restaurant.inventory_tracking ?? true,
  });

  const [profileForm, setProfileForm] = useState({
    first_name: profile.first_name || '',
    last_name: profile.last_name || '',
    email: profile.email || '',
    phone: profile.phone || '',
  });

  const handleRestaurantSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateRestaurantMutation.mutate(restaurantForm);
  };

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate(profileForm);
  };

  const tabs = [
    { id: 'restaurant', label: 'Restaurant', icon: BuildingStorefrontIcon },
    { id: 'profile', label: 'Profile', icon: UserIcon },
    { id: 'notifications', label: 'Notifications', icon: BellIcon },
    { id: 'payments', label: 'Payments', icon: CreditCardIcon },
    { id: 'printing', label: 'Printing', icon: PrinterIcon },
    { id: 'security', label: 'Security', icon: ShieldCheckIcon },
  ];

  return (
    <Layout>
      <div className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div className="mb-4 sm:mb-0">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-1">Manage your restaurant and system preferences</p>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row lg:space-x-6 space-y-6 lg:space-y-0">
          {/* Sidebar */}
          <div className="lg:w-64 lg:flex-shrink-0">
            <nav className="flex lg:flex-col space-x-1 lg:space-x-0 lg:space-y-1 overflow-x-auto lg:overflow-x-visible">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-shrink-0 lg:w-full flex items-center px-3 py-2 text-sm font-medium rounded-md whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {activeTab === 'restaurant' && (
              <div className="pos-card p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Restaurant Information</h2>
                <form onSubmit={handleRestaurantSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Restaurant Name
                      </label>
                      <input
                        type="text"
                        value={restaurantForm.name}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, name: e.target.value })}
                        className="pos-input"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={restaurantForm.phone}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, phone: e.target.value })}
                        className="pos-input"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Address
                    </label>
                    <textarea
                      value={restaurantForm.address}
                      onChange={(e) => setRestaurantForm({ ...restaurantForm, address: e.target.value })}
                      className="pos-input"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        value={restaurantForm.email}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, email: e.target.value })}
                        className="pos-input"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Website
                      </label>
                      <input
                        type="url"
                        value={restaurantForm.website}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, website: e.target.value })}
                        className="pos-input"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tax Rate (%)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={restaurantForm.tax_rate}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, tax_rate: e.target.value })}
                        className="pos-input"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Service Charge (%)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={restaurantForm.service_charge}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, service_charge: e.target.value })}
                        className="pos-input"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <select
                        value={restaurantForm.currency}
                        onChange={(e) => setRestaurantForm({ ...restaurantForm, currency: e.target.value })}
                        className="pos-input"
                      >
                        <option value="USD">USD ($)</option>
                        <option value="EUR">EUR (€)</option>
                        <option value="GBP">GBP (£)</option>
                        <option value="BTN">BTN (Nu.)</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Receipt Footer
                    </label>
                    <textarea
                      value={restaurantForm.receipt_footer}
                      onChange={(e) => setRestaurantForm({ ...restaurantForm, receipt_footer: e.target.value })}
                      className="pos-input"
                      rows={3}
                      placeholder="Thank you for dining with us!"
                    />
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">Features</h3>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={restaurantForm.auto_print_receipts}
                          onChange={(e) => setRestaurantForm({ ...restaurantForm, auto_print_receipts: e.target.checked })}
                          className="mr-3"
                        />
                        <span className="text-sm text-gray-700">Auto-print receipts</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={restaurantForm.enable_notifications}
                          onChange={(e) => setRestaurantForm({ ...restaurantForm, enable_notifications: e.target.checked })}
                          className="mr-3"
                        />
                        <span className="text-sm text-gray-700">Enable notifications</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={restaurantForm.table_management}
                          onChange={(e) => setRestaurantForm({ ...restaurantForm, table_management: e.target.checked })}
                          className="mr-3"
                        />
                        <span className="text-sm text-gray-700">Table management</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={restaurantForm.inventory_tracking}
                          onChange={(e) => setRestaurantForm({ ...restaurantForm, inventory_tracking: e.target.checked })}
                          className="mr-3"
                        />
                        <span className="text-sm text-gray-700">Inventory tracking</span>
                      </label>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={updateRestaurantMutation.isPending}
                      className="pos-button-primary"
                    >
                      {updateRestaurantMutation.isPending ? 'Saving...' : 'Save Changes'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="pos-card p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Profile Information</h2>
                <form onSubmit={handleProfileSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                      </label>
                      <input
                        type="text"
                        value={profileForm.first_name}
                        onChange={(e) => setProfileForm({ ...profileForm, first_name: e.target.value })}
                        className="pos-input"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                      </label>
                      <input
                        type="text"
                        value={profileForm.last_name}
                        onChange={(e) => setProfileForm({ ...profileForm, last_name: e.target.value })}
                        className="pos-input"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        value={profileForm.email}
                        onChange={(e) => setProfileForm({ ...profileForm, email: e.target.value })}
                        className="pos-input"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={profileForm.phone}
                        onChange={(e) => setProfileForm({ ...profileForm, phone: e.target.value })}
                        className="pos-input"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={updateProfileMutation.isPending}
                      className="pos-button-primary"
                    >
                      {updateProfileMutation.isPending ? 'Saving...' : 'Save Changes'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Other tabs can be implemented similarly */}
            {activeTab !== 'restaurant' && activeTab !== 'profile' && (
              <div className="pos-card p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {tabs.find(tab => tab.id === activeTab)?.label} Settings
                </h2>
                <p className="text-gray-600">This section is under development.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SettingsPage;

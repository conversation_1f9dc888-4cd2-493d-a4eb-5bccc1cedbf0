/**
 * Customer ordering page - QR code ordering interface
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  ShoppingCartIcon,
  PhoneIcon,
  CurrencyDollarIcon,
  MinusIcon,
  PlusIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { customerAPI } from '../../lib/customerAPI';

interface CartItem {
  id: string;
  menu_item_id: string;
  name: string;
  price: number;
  quantity: number;
  special_instructions?: string;
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  logo?: string;
  phone: string;
}

interface Table {
  id: string;
  number: string;
  display_name: string;
  capacity: number;
  section?: string;
}

const CustomerOrderingPage: React.FC = () => {
  const { restaurantSlug, tableNumber } = useParams<{
    restaurantSlug: string;
    tableNumber: string;
  }>();
  const navigate = useNavigate();

  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: '',
  });

  // Fetch restaurant and table info
  const { data: restaurantData, isLoading: restaurantLoading } = useQuery({
    queryKey: ['customer-restaurant', restaurantSlug, tableNumber],
    queryFn: () => customerAPI.getRestaurantByTable(restaurantSlug!, tableNumber!),
    enabled: !!restaurantSlug && !!tableNumber,
  });

  // Fetch menu
  const { data: menuData, isLoading: menuLoading } = useQuery({
    queryKey: ['customer-menu', restaurantSlug],
    queryFn: () => customerAPI.getCustomerMenu(restaurantSlug!),
    enabled: !!restaurantSlug,
  });

  // Create order mutation
  const createOrderMutation = useMutation({
    mutationFn: (orderData: any) =>
      customerAPI.createCustomerOrder(restaurantSlug!, tableNumber!, orderData),
    onSuccess: (data) => {
      toast.success('Order placed successfully!');
      setCart([]);
      setShowCart(false);
      // Navigate to order tracking page
      navigate(`/order/${restaurantSlug}/order/${data.order_id}/status`);
    },
    onError: (error: any) => {
      toast.error('Failed to place order. Please try again.');
      console.error('Order creation error:', error);
    },
  });

  const restaurant: Restaurant | undefined = restaurantData?.restaurant;
  const table: Table | undefined = restaurantData?.table;
  const categories = menuData?.categories || [];

  // Set first category as selected by default
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0].id);
    }
  }, [categories, selectedCategory]);

  const addToCart = (item: any) => {
    const existingItem = cart.find((cartItem) => cartItem.menu_item_id === item.id);
    
    if (existingItem) {
      setCart(cart.map((cartItem) =>
        cartItem.menu_item_id === item.id
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      ));
    } else {
      setCart([...cart, {
        id: `${item.id}-${Date.now()}`,
        menu_item_id: item.id,
        name: item.name,
        price: parseFloat(item.price),
        quantity: 1,
      }]);
    }
    toast.success(`${item.name} added to cart`);
  };

  const updateCartItemQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCart(cart.filter((item) => item.id !== itemId));
    } else {
      setCart(cart.map((item) =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      ));
    }
  };

  const removeFromCart = (itemId: string) => {
    setCart(cart.filter((item) => item.id !== itemId));
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const handlePlaceOrder = () => {
    if (cart.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    if (!customerInfo.name.trim()) {
      toast.error('Please enter your name');
      return;
    }

    const orderData = {
      customer_name: customerInfo.name,
      customer_phone: customerInfo.phone,
      customer_email: customerInfo.email,
      items: cart.map((item) => ({
        menu_item_id: item.menu_item_id,
        quantity: item.quantity,
        special_instructions: item.special_instructions || '',
      })),
    };

    createOrderMutation.mutate(orderData);
  };

  const callWaiter = () => {
    customerAPI.callWaiter(restaurantSlug!, tableNumber!)
      .then(() => {
        toast.success('Waiter has been notified!');
      })
      .catch(() => {
        toast.error('Failed to call waiter. Please try again.');
      });
  };

  const requestBill = () => {
    customerAPI.requestBill(restaurantSlug!, tableNumber!)
      .then(() => {
        toast.success('Bill request sent to staff!');
      })
      .catch(() => {
        toast.error('Failed to request bill. Please try again.');
      });
  };

  if (restaurantLoading || menuLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!restaurant || !table) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Table Not Found</h2>
          <p className="text-gray-600">Please scan a valid QR code.</p>
        </div>
      </div>
    );
  }

  const selectedCategoryData = categories.find((cat: any) => cat.id === selectedCategory);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              {restaurant.logo && (
                <img
                  src={restaurant.logo}
                  alt={restaurant.name}
                  className="h-8 w-8 rounded-full mr-3"
                />
              )}
              <div>
                <h1 className="text-lg font-semibold text-gray-900">{restaurant.name}</h1>
                <p className="text-sm text-gray-500">{table.display_name}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={callWaiter}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Call Waiter"
              >
                <PhoneIcon className="h-5 w-5" />
              </button>
              
              <button
                onClick={requestBill}
                className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Request Bill"
              >
                <CurrencyDollarIcon className="h-5 w-5" />
              </button>
              
              <button
                onClick={() => setShowCart(true)}
                className="relative p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <ShoppingCartIcon className="h-5 w-5" />
                {getTotalItems() > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {getTotalItems()}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Categories Sidebar */}
          <div className="lg:w-64">
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category: any) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="flex-1">
            {selectedCategoryData && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  {selectedCategoryData.name}
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {selectedCategoryData.items.map((item: any) => (
                    <div key={item.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      {item.image && (
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-48 object-cover"
                        />
                      )}
                      
                      <div className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-semibold text-gray-900">{item.name}</h3>
                          <span className="text-lg font-bold text-blue-600">${item.price}</span>
                        </div>
                        
                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                          {item.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-gray-500">
                            {item.prep_time_minutes > 0 && (
                              <span>⏱️ {item.prep_time_minutes} min</span>
                            )}
                          </div>
                          
                          <button
                            onClick={() => addToCart(item)}
                            disabled={!item.is_available || !item.is_in_stock}
                            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                              !item.is_available || !item.is_in_stock
                                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                          >
                            Add to Cart
                          </button>
                        </div>
                        
                        {!item.is_in_stock && (
                          <div className="mt-2 text-xs text-red-600 font-medium">
                            Out of Stock
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Cart Sidebar */}
      {showCart && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowCart(false)} />
          
          <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-lg font-semibold">Your Order</h3>
                <button
                  onClick={() => setShowCart(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="flex-1 overflow-y-auto p-4">
                {cart.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">Your cart is empty</p>
                ) : (
                  <div className="space-y-4">
                    {cart.map((item) => (
                      <div key={item.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{item.name}</h4>
                          <p className="text-sm text-gray-600">${item.price.toFixed(2)} each</p>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => updateCartItemQuantity(item.id, item.quantity - 1)}
                            className="p-1 text-gray-600 hover:text-red-600"
                          >
                            <MinusIcon className="h-4 w-4" />
                          </button>
                          
                          <span className="font-medium">{item.quantity}</span>
                          
                          <button
                            onClick={() => updateCartItemQuantity(item.id, item.quantity + 1)}
                            className="p-1 text-gray-600 hover:text-blue-600"
                          >
                            <PlusIcon className="h-4 w-4" />
                          </button>
                          
                          <button
                            onClick={() => removeFromCart(item.id)}
                            className="p-1 text-gray-600 hover:text-red-600 ml-2"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {cart.length > 0 && (
                <div className="border-t p-4 space-y-4">
                  <div className="space-y-3">
                    <input
                      type="text"
                      placeholder="Your name *"
                      value={customerInfo.name}
                      onChange={(e) => setCustomerInfo({ ...customerInfo, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                    <input
                      type="tel"
                      placeholder="Phone number (optional)"
                      value={customerInfo.phone}
                      onChange={(e) => setCustomerInfo({ ...customerInfo, phone: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between text-lg font-semibold">
                    <span>Total:</span>
                    <span>${getTotalAmount().toFixed(2)}</span>
                  </div>
                  
                  <button
                    onClick={handlePlaceOrder}
                    disabled={createOrderMutation.isPending}
                    className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {createOrderMutation.isPending ? 'Placing Order...' : 'Place Order'}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerOrderingPage;

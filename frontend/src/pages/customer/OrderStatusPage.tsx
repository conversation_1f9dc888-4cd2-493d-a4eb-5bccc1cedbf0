/**
 * Customer order status tracking page
 */

import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  ClockIcon,
  CheckCircleIcon,
  CookingPotIcon,
  TruckIcon,
  PhoneIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import { customerAPI } from '../../lib/customerAPI';

const OrderStatusPage: React.FC = () => {
  const { restaurantSlug, orderId } = useParams<{
    restaurantSlug: string;
    orderId: string;
  }>();

  // Fetch order status with polling
  const { data: orderData, isLoading } = useQuery({
    queryKey: ['customer-order-status', restaurantSlug, orderId],
    queryFn: () => customerAPI.getCustomerOrderStatus(restaurantSlug!, orderId!),
    enabled: !!restaurantSlug && !!orderId,
    refetchInterval: 10000, // Poll every 10 seconds
  });

  const order = orderData;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-8 w-8 text-yellow-500" />;
      case 'confirmed':
        return <CheckCircleIcon className="h-8 w-8 text-blue-500" />;
      case 'preparing':
        return <CookingPotIcon className="h-8 w-8 text-orange-500" />;
      case 'ready':
        return <TruckIcon className="h-8 w-8 text-green-500" />;
      case 'served':
        return <CheckCircleIcon className="h-8 w-8 text-green-600" />;
      case 'completed':
        return <CheckCircleIcon className="h-8 w-8 text-green-700" />;
      default:
        return <ClockIcon className="h-8 w-8 text-gray-500" />;
    }
  };

  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Your order has been received and is waiting for confirmation.';
      case 'confirmed':
        return 'Your order has been confirmed and will be prepared shortly.';
      case 'preparing':
        return 'Your order is being prepared in the kitchen.';
      case 'ready':
        return 'Your order is ready! It will be served to your table soon.';
      case 'served':
        return 'Your order has been served. Enjoy your meal!';
      case 'completed':
        return 'Your order is complete. Thank you for dining with us!';
      default:
        return 'Checking order status...';
    }
  };

  const getStatusSteps = (currentStatus: string) => {
    const steps = [
      { key: 'pending', label: 'Order Received', completed: false },
      { key: 'confirmed', label: 'Confirmed', completed: false },
      { key: 'preparing', label: 'Preparing', completed: false },
      { key: 'ready', label: 'Ready', completed: false },
      { key: 'served', label: 'Served', completed: false },
    ];

    const statusOrder = ['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed'];
    const currentIndex = statusOrder.indexOf(currentStatus);

    return steps.map((step, index) => ({
      ...step,
      completed: index <= currentIndex,
      current: index === currentIndex,
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-600">Please check your order number.</p>
        </div>
      </div>
    );
  }

  const statusSteps = getStatusSteps(order.status);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Order Status</h1>
          <p className="text-gray-600">Order #{order.order_number}</p>
        </div>

        {/* Current Status */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-center mb-4">
            {getStatusIcon(order.status)}
          </div>
          
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {order.status_display}
            </h2>
            <p className="text-gray-600 mb-4">
              {getStatusMessage(order.status)}
            </p>
            
            {order.estimated_time && (
              <p className="text-sm text-blue-600">
                Estimated completion: {new Date(order.estimated_time).toLocaleTimeString()}
              </p>
            )}
          </div>
        </div>

        {/* Progress Steps */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Progress</h3>
          
          <div className="space-y-4">
            {statusSteps.map((step, index) => (
              <div key={step.key} className="flex items-center">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  step.completed
                    ? 'bg-green-500 text-white'
                    : step.current
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {step.completed ? (
                    <CheckCircleIcon className="h-5 w-5" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                
                <div className="ml-4">
                  <p className={`font-medium ${
                    step.completed || step.current ? 'text-gray-900' : 'text-gray-500'
                  }`}>
                    {step.label}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Items */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
          
          <div className="space-y-3">
            {order.items.map((item: any) => (
              <div key={item.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{item.menu_item_name}</p>
                  {item.special_instructions && (
                    <p className="text-sm text-gray-600">Note: {item.special_instructions}</p>
                  )}
                </div>
                
                <div className="text-right">
                  <p className="font-medium text-gray-900">
                    {item.quantity} × ${parseFloat(item.unit_price).toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600">
                    ${parseFloat(item.total_price).toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-lg font-semibold">
              <span>Total:</span>
              <span>${parseFloat(order.total_amount).toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <button
              onClick={() => {
                // Call waiter functionality would go here
                alert('Waiter has been notified!');
              }}
              className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PhoneIcon className="h-5 w-5 mr-2" />
              Call Waiter
            </button>
            
            <button
              onClick={() => {
                // Request bill functionality would go here
                alert('Bill request sent!');
              }}
              className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <CurrencyDollarIcon className="h-5 w-5 mr-2" />
              Request Bill
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderStatusPage;

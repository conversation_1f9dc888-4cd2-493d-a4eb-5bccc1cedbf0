/**
 * Tables slice for POS system
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { tablesAPI } from '../../lib/api';

// Types
export interface Table {
  id: string;
  number: string;
  name?: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'out_of_order';
  section?: string;
  x_position: number;
  y_position: number;
  current_order_id?: string;
}

export interface TableState {
  tables: Table[];
  selectedTable: Table | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: TableState = {
  tables: [],
  selectedTable: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchTables = createAsyncThunk(
  'tables/fetchTables',
  async () => {
    const response = await tablesAPI.getTables();
    return response.data;
  }
);

export const updateTable = createAsyncThunk(
  'tables/updateTable',
  async ({ id, data }: { id: string; data: any }) => {
    const response = await tablesAPI.updateTable(id, data);
    return response.data;
  }
);

const tableSlice = createSlice({
  name: 'tables',
  initialState,
  reducers: {
    setSelectedTable: (state, action) => {
      state.selectedTable = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTables.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchTables.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tables = action.payload.results || action.payload;
      })
      .addCase(fetchTables.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch tables';
      })
      .addCase(updateTable.fulfilled, (state, action) => {
        const index = state.tables.findIndex(table => table.id === action.payload.id);
        if (index !== -1) {
          state.tables[index] = action.payload;
        }
        if (state.selectedTable?.id === action.payload.id) {
          state.selectedTable = action.payload;
        }
      });
  },
});

export const { setSelectedTable, clearError } = tableSlice.actions;
export default tableSlice.reducer;

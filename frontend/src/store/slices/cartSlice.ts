/**
 * Cart slice for POS system
 */

import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

// Types
export interface CartItem {
  id: string;
  menu_item_id: string;
  name: string;
  price: number;
  quantity: number;
  modifiers?: CartModifier[];
  special_instructions?: string;
  subtotal: number;
}

export interface CartModifier {
  id: string;
  name: string;
  price: number;
}

export interface CartState {
  items: CartItem[];
  subtotal: number;
  tax: number;
  service_charge: number;
  discount: number;
  total: number;
  table_id?: string;
  order_type: 'dine_in' | 'takeaway' | 'delivery';
  customer_id?: string;
  waiter_id?: string;
  special_instructions?: string;
}

const initialState: CartState = {
  items: [],
  subtotal: 0,
  tax: 0,
  service_charge: 0,
  discount: 0,
  total: 0,
  order_type: 'dine_in',
};

// Helper functions
const calculateItemSubtotal = (item: Omit<CartItem, 'subtotal'>): number => {
  const modifiersTotal = item.modifiers?.reduce((sum, modifier) => sum + modifier.price, 0) || 0;
  return (item.price + modifiersTotal) * item.quantity;
};

const calculateTotals = (state: CartState) => {
  // Calculate subtotal
  state.subtotal = state.items.reduce((sum, item) => sum + item.subtotal, 0);
  
  // Calculate tax (assuming 8.75% tax rate - should come from restaurant settings)
  state.tax = state.subtotal * 0.0875;
  
  // Calculate service charge (if applicable)
  state.service_charge = state.order_type === 'dine_in' ? state.subtotal * 0.05 : 0;
  
  // Calculate total
  state.total = state.subtotal + state.tax + state.service_charge - state.discount;
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addItem: (state, action: PayloadAction<Omit<CartItem, 'subtotal'>>) => {
      const newItem = action.payload;
      const existingItemIndex = state.items.findIndex(
        item => 
          item.menu_item_id === newItem.menu_item_id &&
          JSON.stringify(item.modifiers) === JSON.stringify(newItem.modifiers) &&
          item.special_instructions === newItem.special_instructions
      );

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        state.items[existingItemIndex].quantity += newItem.quantity;
        state.items[existingItemIndex].subtotal = calculateItemSubtotal(state.items[existingItemIndex]);
      } else {
        // Add new item
        const itemWithSubtotal: CartItem = {
          ...newItem,
          subtotal: calculateItemSubtotal(newItem),
        };
        state.items.push(itemWithSubtotal);
      }

      calculateTotals(state);
    },

    removeItem: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      calculateTotals(state);
    },

    updateItemQuantity: (state, action: PayloadAction<{ id: string; quantity: number }>) => {
      const { id, quantity } = action.payload;
      const item = state.items.find(item => item.id === id);
      
      if (item) {
        if (quantity <= 0) {
          state.items = state.items.filter(item => item.id !== id);
        } else {
          item.quantity = quantity;
          item.subtotal = calculateItemSubtotal(item);
        }
      }

      calculateTotals(state);
    },

    updateItemModifiers: (state, action: PayloadAction<{ id: string; modifiers: CartModifier[] }>) => {
      const { id, modifiers } = action.payload;
      const item = state.items.find(item => item.id === id);
      
      if (item) {
        item.modifiers = modifiers;
        item.subtotal = calculateItemSubtotal(item);
      }

      calculateTotals(state);
    },

    updateItemInstructions: (state, action: PayloadAction<{ id: string; instructions: string }>) => {
      const { id, instructions } = action.payload;
      const item = state.items.find(item => item.id === id);
      
      if (item) {
        item.special_instructions = instructions;
      }
    },

    setOrderType: (state, action: PayloadAction<'dine_in' | 'takeaway' | 'delivery'>) => {
      state.order_type = action.payload;
      calculateTotals(state);
    },

    setTableId: (state, action: PayloadAction<string | undefined>) => {
      state.table_id = action.payload;
    },

    setCustomerId: (state, action: PayloadAction<string | undefined>) => {
      state.customer_id = action.payload;
    },

    setWaiterId: (state, action: PayloadAction<string | undefined>) => {
      state.waiter_id = action.payload;
    },

    setSpecialInstructions: (state, action: PayloadAction<string>) => {
      state.special_instructions = action.payload;
    },

    applyDiscount: (state, action: PayloadAction<number>) => {
      state.discount = action.payload;
      calculateTotals(state);
    },

    clearCart: (state) => {
      return initialState;
    },

    // Load cart from saved order (for editing existing orders)
    loadCart: (state, action: PayloadAction<CartState>) => {
      return action.payload;
    },
  },
});

export const {
  addItem,
  removeItem,
  updateItemQuantity,
  updateItemModifiers,
  updateItemInstructions,
  setOrderType,
  setTableId,
  setCustomerId,
  setWaiterId,
  setSpecialInstructions,
  applyDiscount,
  clearCart,
  loadCart,
} = cartSlice.actions;

export default cartSlice.reducer;

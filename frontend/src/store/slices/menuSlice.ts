/**
 * Menu slice for POS system
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { menuAPI } from '../../lib/api';

// Types
export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  is_available: boolean;
  stock_enabled: boolean;
  stock_quantity?: number;
  modifiers?: MenuModifier[];
}

export interface MenuModifier {
  id: string;
  name: string;
  price: number;
  is_required: boolean;
}

export interface MenuCategory {
  id: string;
  name: string;
  description: string;
  sort_order: number;
}

export interface MenuState {
  categories: MenuCategory[];
  items: MenuItem[];
  selectedCategory: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: MenuState = {
  categories: [],
  items: [],
  selectedCategory: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchCategories = createAsyncThunk(
  'menu/fetchCategories',
  async () => {
    const response = await menuAPI.getCategories();
    return response.data;
  }
);

export const fetchMenuItems = createAsyncThunk(
  'menu/fetchItems',
  async (params?: any) => {
    const response = await menuAPI.getItems(params);
    return response.data;
  }
);

const menuSlice = createSlice({
  name: 'menu',
  initialState,
  reducers: {
    setSelectedCategory: (state, action) => {
      state.selectedCategory = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCategories.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload.results || action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch categories';
      })
      .addCase(fetchMenuItems.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchMenuItems.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload.results || action.payload;
      })
      .addCase(fetchMenuItems.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch menu items';
      });
  },
});

export const { setSelectedCategory, clearError } = menuSlice.actions;
export default menuSlice.reducer;

/**
 * UI slice for managing application UI state
 */

import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

export interface UIState {
  sidebarOpen: boolean;
  currentView: 'pos' | 'kitchen' | 'tables' | 'menu' | 'orders' | 'inventory' | 'reports' | 'settings';
  theme: 'light' | 'dark';
  notifications: Notification[];
  isOffline: boolean;
  loading: {
    [key: string]: boolean;
  };
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
}

const initialState: UIState = {
  sidebarOpen: true,
  currentView: 'pos',
  theme: 'light',
  notifications: [],
  isOffline: false,
  loading: {},
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setCurrentView: (state, action: PayloadAction<UIState['currentView']>) => {
      state.currentView = action.payload;
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: Date.now(),
      };
      state.notifications.unshift(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
  },
});

export const { toggleSidebar, setCurrentView, addNotification, removeNotification } = uiSlice.actions;
export default uiSlice.reducer;

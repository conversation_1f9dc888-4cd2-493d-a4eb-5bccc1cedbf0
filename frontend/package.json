{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@reduxjs/toolkit": "^2.9.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.13", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.87.1", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.543.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "react-hot-toast": "^2.6.0", "react-redux": "^9.2.0", "react-router-dom": "^7.8.2", "recharts": "^3.2.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13", "yup": "^1.7.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}